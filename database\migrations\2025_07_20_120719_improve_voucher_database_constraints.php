<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to vouchers table
        Schema::table('vouchers', function (Blueprint $table) {
            $table->index(['is_active', 'expires_at'], 'idx_vouchers_active_expires');
            $table->index(['type', 'is_active'], 'idx_vouchers_type_active');
            $table->index(['category_id', 'is_active'], 'idx_vouchers_category_active');
        });

        // Add indexes to user_vouchers table
        Schema::table('user_vouchers', function (Blueprint $table) {
            $table->index(['voucher_id', 'used_at'], 'idx_user_vouchers_voucher_used');
            $table->index(['user_id', 'used_at'], 'idx_user_vouchers_user_used');
            $table->index(['expires_at', 'used_at'], 'idx_user_vouchers_expires_used');
        });

        // Add check constraints for data integrity
        DB::statement('ALTER TABLE vouchers ADD CONSTRAINT chk_discount_value_positive CHECK (discount_value >= 0)');
        DB::statement('ALTER TABLE vouchers ADD CONSTRAINT chk_max_discount_amount_positive CHECK (max_discount_amount IS NULL OR max_discount_amount >= 0)');
        DB::statement('ALTER TABLE vouchers ADD CONSTRAINT chk_min_order_amount_positive CHECK (min_order_amount IS NULL OR min_order_amount >= 0)');
        DB::statement('ALTER TABLE vouchers ADD CONSTRAINT chk_max_uses_per_user_positive CHECK (max_uses_per_user > 0)');
        DB::statement('ALTER TABLE vouchers ADD CONSTRAINT chk_total_uses_limit_positive CHECK (total_uses_limit IS NULL OR total_uses_limit > 0)');
        DB::statement('ALTER TABLE vouchers ADD CONSTRAINT chk_percentage_discount_valid CHECK (discount_type != \'percentage\' OR discount_value <= 100)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop check constraints
        DB::statement('ALTER TABLE vouchers DROP CONSTRAINT IF EXISTS chk_discount_value_positive');
        DB::statement('ALTER TABLE vouchers DROP CONSTRAINT IF EXISTS chk_max_discount_amount_positive');
        DB::statement('ALTER TABLE vouchers DROP CONSTRAINT IF EXISTS chk_min_order_amount_positive');
        DB::statement('ALTER TABLE vouchers DROP CONSTRAINT IF EXISTS chk_max_uses_per_user_positive');
        DB::statement('ALTER TABLE vouchers DROP CONSTRAINT IF EXISTS chk_total_uses_limit_positive');
        DB::statement('ALTER TABLE vouchers DROP CONSTRAINT IF EXISTS chk_percentage_discount_valid');

        // Drop indexes from user_vouchers table
        Schema::table('user_vouchers', function (Blueprint $table) {
            $table->dropIndex('idx_user_vouchers_voucher_used');
            $table->dropIndex('idx_user_vouchers_user_used');
            $table->dropIndex('idx_user_vouchers_expires_used');
        });

        // Drop indexes from vouchers table
        Schema::table('vouchers', function (Blueprint $table) {
            $table->dropIndex('idx_vouchers_active_expires');
            $table->dropIndex('idx_vouchers_type_active');
            $table->dropIndex('idx_vouchers_category_active');
        });
    }
};
