<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> sử nạp tiền - Acc<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 py-4 lg:py-8">
    <!-- Page Header -->
    <div class="mb-6 lg:mb-8">
        <h1 class="text-2xl lg:text-3xl font-bold text-gray-900">L<PERSON>ch sử nạp tiền</h1>
        <p class="text-gray-600 mt-1 lg:mt-2 text-sm lg:text-base"><PERSON> dõi tất cả giao dịch nạp tiền của bạn</p>
    </div>

    <?php if($transactions->count() > 0): ?>
        <!-- Transactions List -->
        <div class="space-y-4 lg:space-y-6">
            <?php $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4">
                    <!-- Mobile Layout -->
                    <div class="block lg:hidden">
                        <!-- Header Row -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3
                                    <?php echo e($transaction->method === 'atm' ? 'bg-blue-100' : 'bg-green-100'); ?>">
                                    <i class="fas <?php echo e($transaction->method === 'atm' ? 'fa-university text-blue-600' : 'fa-credit-card text-green-600'); ?> text-sm"></i>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900 text-sm">
                                        <?php echo e($transaction->display_title); ?>

                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <?php echo e($transaction->created_at->format('d/m/Y H:i')); ?>

                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-lg text-gray-900">
                                    <?php echo e(format_money($transaction->actual_amount)); ?>

                                </div>
                                <?php if($transaction->method === 'card' && $transaction->status === 'completed' && $transaction->actual_amount != $transaction->amount): ?>
                                <div class="text-xs text-gray-500">
                                    Thực nhận
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Status Row -->
                        <div class="flex items-center justify-between mb-2">
                            <div class="text-xs text-gray-500">
                                <?php echo e($transaction->transaction_code); ?>

                            </div>
                            <div>
                                <?php if($transaction->status === 'completed'): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Thành công
                                    </span>
                                <?php elseif($transaction->status === 'pending'): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>
                                        Đang xử lý
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times mr-1"></i>
                                        Thất bại
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Card Details (if applicable) -->
                        <?php if($transaction->method === 'card' && $transaction->card_serial): ?>
                        <div class="text-xs text-gray-500 bg-gray-50 rounded p-2">
                            <div>Serial: <?php echo e($transaction->card_serial); ?></div>
                            <div>Code: <?php echo e($transaction->card_code); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Desktop Layout -->
                    <div class="hidden lg:flex lg:items-center lg:justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4
                                <?php echo e($transaction->method === 'atm' ? 'bg-blue-100' : 'bg-green-100'); ?>">
                                <i class="fas <?php echo e($transaction->method === 'atm' ? 'fa-university text-blue-600' : 'fa-credit-card text-green-600'); ?>"></i>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-900 text-lg">
                                    <?php echo e($transaction->display_title); ?>

                                </div>
                                <div class="text-sm text-gray-500 mt-1">
                                    Mã giao dịch: <?php echo e($transaction->transaction_code); ?>

                                </div>
                                <div class="text-sm text-gray-500">
                                    <?php echo e($transaction->created_at->format('d/m/Y H:i:s')); ?>

                                </div>
                                <?php if($transaction->method === 'card' && $transaction->card_serial): ?>
                                <div class="text-sm text-gray-500">
                                    Serial: <?php echo e($transaction->card_serial); ?> - Code: <?php echo e($transaction->card_code); ?>

                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-xl text-gray-900">
                                <?php echo e(format_money($transaction->actual_amount)); ?>

                            </div>
                            <?php if($transaction->method === 'card' && $transaction->status === 'completed' && $transaction->actual_amount != $transaction->amount): ?>
                            <div class="text-xs text-gray-500">
                                Thực nhận
                            </div>
                            <?php endif; ?>
                            <div class="mt-1">
                            <?php if($transaction->status === 'completed'): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Thành công
                                </span>
                            <?php elseif($transaction->status === 'pending'): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>
                                    Đang xử lý
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    Thất bại
                                </span>
                            <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($transaction->note && $transaction->status === 'failed'): ?>
                    <div class="mt-3 p-2 lg:p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div class="text-xs lg:text-sm text-red-700">
                            <strong>Lý do thất bại:</strong>
                            <?php
                                $note = json_decode($transaction->note, true);
                                if (is_array($note) && isset($note['status'])) {
                                    echo \App\Services\CardApiService::getStatusMessage($note['status']);
                                } else {
                                    echo 'Không xác định';
                                }
                            ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            <?php echo e($transactions->links('pagination.custom')); ?>

        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-md p-12 text-center">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-history text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có giao dịch nạp tiền</h3>
                <p class="text-gray-500 mb-6">Bạn chưa thực hiện giao dịch nạp tiền nào.</p>
        </div>
    <?php endif; ?>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/topup/history.blade.php ENDPATH**/ ?>