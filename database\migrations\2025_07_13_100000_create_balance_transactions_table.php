<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('balance_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            
            // Loại giao dịch
            $table->enum('type', [
                'topup_atm',        // Nạp tiền ATM
                'topup_card',       // Nạp tiền thẻ
                'admin_add',        // Admin cộng tiền
                'admin_subtract',   // Admin trừ tiền
                'purchase',         // <PERSON>a sản phẩm
                'refund'           // Hoàn tiền
            ]);
            
            // Số tiền (có thể âm hoặc dương)
            $table->decimal('amount', 15, 2);

            // Balance trước và sau giao dịch
            $table->decimal('balance_before', 15, 2);
            $table->decimal('balance_after', 15, 2);
            
            // <PERSON>ô tả giao dịch
            $table->text('description');
            
            // Admin thực hiện (nếu có)
            $table->unsignedBigInteger('admin_id')->nullable();
            
            // Reference đến bảng khác (topup_transactions, orders, etc.)
            $table->string('reference_type')->nullable(); // App\Models\TopupTransaction
            $table->unsignedBigInteger('reference_id')->nullable();
            
            // Metadata bổ sung (JSON)
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('admin_id')->references('id')->on('users')->onDelete('set null');
            $table->index(['user_id', 'created_at']);
            $table->index(['type', 'created_at']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('balance_transactions');
    }
};
