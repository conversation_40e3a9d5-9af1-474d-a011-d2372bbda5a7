<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Mobile Legends',
                'publisher' => 'Moonton',
                'slug' => 'mobile-legends',
                'status' => 'active',
                'sort_order' => 1,
            ],
            [
                'name' => 'Free Fire',
                'publisher' => 'Garena',
                'slug' => 'free-fire',
                'status' => 'active',
                'sort_order' => 2,
            ],
            [
                'name' => 'PUBG Mobile',
                'publisher' => 'Tencent',
                'slug' => 'pubg-mobile',
                'status' => 'active',
                'sort_order' => 3,
            ],
            [
                'name' => 'Liên Quân Mobile',
                'publisher' => 'Garena',
                'slug' => 'lien-quan-mobile',
                'status' => 'active',
                'sort_order' => 4,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
