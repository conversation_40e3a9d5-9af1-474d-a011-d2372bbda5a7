@extends('layouts.admin')

@section('title', 'Chỉnh sửa Voucher')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Chỉnh sửa Voucher</h1>
            <p class="text-gray-600 mt-1">Cập nhật thông tin mã giảm giá và khuyến mãi</p>
        </div>
        <div class="flex-shrink-0">
            <a href="{{ route('admin.vouchers.index') }}" class="inline-flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <form action="{{ route('admin.vouchers.update', $voucher) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Mã voucher (readonly) -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">Mã voucher</label>
                        <input type="text" id="code" name="code" value="{{ $voucher->code }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
                               readonly>
                        <p class="text-xs text-gray-500 mt-1">Mã voucher không thể thay đổi</p>
                    </div>

                    <!-- Tên voucher -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Tên voucher *</label>
                        <input type="text" id="name" name="name" value="{{ old('name', $voucher->name) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror"
                               placeholder="VD: Voucher chào mừng" required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Loại voucher (readonly) -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Loại voucher</label>
                    <input type="text" value="{{ $voucher->type === 'manual' ? 'Manual - Cấp thủ công' : 'Auto - Tự động khi mua sản phẩm' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
                           readonly>
                    <p class="text-xs text-gray-500 mt-1">Loại voucher không thể thay đổi</p>
                    <!-- Hidden input để gửi giá trị -->
                    <input type="hidden" id="type" name="type" value="{{ $voucher->type }}">
                </div>

                <!-- Mô tả -->
                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Mô tả</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('description') border-red-500 @enderror"
                              placeholder="Mô tả chi tiết về voucher">{{ old('description', $voucher->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    <!-- Loại giảm giá -->
                    <div>
                        <label for="discount_type" class="block text-sm font-medium text-gray-700 mb-2">Loại giảm giá *</label>
                        <select id="discount_type" name="discount_type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('discount_type') border-red-500 @enderror"
                                onchange="toggleDiscountType()" required>
                            <option value="percentage" {{ old('discount_type', $voucher->discount_type) == 'percentage' ? 'selected' : '' }}>Phần trăm (%)</option>
                            <option value="fixed" {{ old('discount_type', $voucher->discount_type) == 'fixed' ? 'selected' : '' }}>Số tiền cố định (VND)</option>
                        </select>
                        @error('discount_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Giảm giá -->
                    <div>
                        <label for="discount_value" class="block text-sm font-medium text-gray-700 mb-2">
                            <span id="discount_label">Giảm giá (%) *</span>
                        </label>
                        <input type="number" id="discount_value" name="discount_value" value="{{ old('discount_value', $voucher->discount_value) }}"
                               min="0" max="100" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('discount_value') border-red-500 @enderror"
                               placeholder="10" required>
                        <p class="text-xs text-gray-500 mt-1" id="discount_hint">Nhập phần trăm giảm giá (0-100)</p>
                        @error('discount_value')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Áp dụng cho -->
                    <div>
                        <label for="applicable_to" class="block text-sm font-medium text-gray-700 mb-2">Áp dụng cho *</label>
                        <select id="applicable_to" name="applicable_to"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('applicable_to') border-red-500 @enderror" required>
                            <option value="both" {{ old('applicable_to', $voucher->applicable_to) === 'both' ? 'selected' : '' }}>Tất cả</option>
                            <option value="products" {{ old('applicable_to', $voucher->applicable_to) === 'products' ? 'selected' : '' }}>Chỉ sản phẩm</option>
                            <option value="services" {{ old('applicable_to', $voucher->applicable_to) === 'services' ? 'selected' : '' }}>Chỉ dịch vụ</option>
                        </select>
                        @error('applicable_to')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <!-- Danh mục áp dụng voucher -->
                    <div>
                        <label for="category_search" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-target text-blue-500 mr-1"></i>
                            Danh mục áp dụng voucher (tùy chọn)
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="category_search"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Tìm kiếm danh mục hoặc để trống cho tất cả..."
                                   value="{{ old('category_name', $voucher->category ? $voucher->category->name : '') }}"
                                   autocomplete="off">
                            <input type="hidden" id="category_id" name="category_id" value="{{ old('category_id', $voucher->category_id) }}">

                            <!-- Dropdown results -->
                            <div id="category_dropdown" class="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto hidden">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                        <p class="text-xs text-blue-600 mt-1">
                            <i class="fas fa-info-circle mr-1"></i>
                            Voucher chỉ dùng được cho sản phẩm/dịch vụ thuộc danh mục này
                        </p>
                    </div>

                    <!-- Đơn hàng tối thiểu -->
                    <div>
                        <label for="min_order_amount" class="block text-sm font-medium text-gray-700 mb-2">Đơn hàng tối thiểu (đ)</label>
                        <input type="number" id="min_order_amount" name="min_order_amount" value="{{ old('min_order_amount', $voucher->min_order_amount) }}"
                               min="0" step="1000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('min_order_amount') border-red-500 @enderror"
                               placeholder="0">
                        @error('min_order_amount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <!-- Số lần dùng/user -->
                    <div>
                        <label for="max_uses_per_user" class="block text-sm font-medium text-gray-700 mb-2">Số lần dùng/user *</label>
                        <input type="number" id="max_uses_per_user" name="max_uses_per_user" value="{{ old('max_uses_per_user', $voucher->max_uses_per_user) }}"
                               min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('max_uses_per_user') border-red-500 @enderror"
                               required>
                        @error('max_uses_per_user')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tổng số lần dùng -->
                    <div>
                        <label for="total_uses_limit" class="block text-sm font-medium text-gray-700 mb-2">Tổng số lần dùng</label>
                        <input type="number" id="total_uses_limit" name="total_uses_limit" value="{{ old('total_uses_limit', $voucher->total_uses_limit) }}"
                               min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('total_uses_limit') border-red-500 @enderror"
                               placeholder="Không giới hạn">
                        @error('total_uses_limit')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Thời hạn (Manual voucher) -->
                <div class="mt-6" id="manual-expires" style="display: {{ $voucher->type === 'manual' ? 'block' : 'none' }};">
                    <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">Thời hạn sử dụng</label>
                    <input type="datetime-local" id="expires_at" name="expires_at" value="{{ old('expires_at', $voucher->expires_at ? $voucher->expires_at->format('Y-m-d\TH:i') : '') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('expires_at') border-red-500 @enderror">
                    <p class="text-xs text-gray-500 mt-1">Để trống nếu không có thời hạn</p>
                    @error('expires_at')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Auto Voucher Settings -->
                <div id="auto-voucher-settings" style="display: {{ $voucher->type === 'auto_product_purchase' ? 'block' : 'none' }};" class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center mb-4">
                        <h3 class="text-lg font-medium text-blue-900">Cấu hình Auto Voucher</h3>
                        <div class="ml-4 px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            <i class="fas fa-magic mr-1"></i>Tự động tặng voucher
                        </div>
                    </div>

                    <!-- Flow Explanation -->
                    <div class="mb-6 p-3 bg-white rounded-lg border border-blue-200">
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <div class="flex items-center text-green-600">
                                <i class="fas fa-shopping-cart mr-1"></i>
                                <span class="font-medium">User mua hàng</span>
                            </div>
                            <i class="fas fa-arrow-right text-gray-400"></i>
                            <div class="flex items-center text-blue-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                <span class="font-medium">Kiểm tra điều kiện</span>
                            </div>
                            <i class="fas fa-arrow-right text-gray-400"></i>
                            <div class="flex items-center text-purple-600">
                                <i class="fas fa-gift mr-1"></i>
                                <span class="font-medium">Tặng voucher</span>
                            </div>
                            <i class="fas fa-arrow-right text-gray-400"></i>
                            <div class="flex items-center text-orange-600">
                                <i class="fas fa-ticket-alt mr-1"></i>
                                <span class="font-medium">User dùng voucher</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Thời hạn cho user -->
                        <div>
                            <label for="user_expires_days" class="block text-sm font-medium text-gray-700 mb-2">Thời hạn cho user (ngày) *</label>
                            <input type="number" id="user_expires_days" name="user_expires_days" value="{{ old('user_expires_days', $voucher->user_expires_days ?? 30) }}"
                                   min="1" max="365"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('user_expires_days') border-red-500 @enderror">
                            <p class="text-xs text-gray-500 mt-1">Voucher sẽ hết hạn sau X ngày kể từ khi cấp cho user</p>
                            @error('user_expires_days')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Giá trị đơn hàng tối thiểu để trigger -->
                        <div>
                            <label for="trigger_min_order_amount" class="block text-sm font-medium text-gray-700 mb-2">Đơn hàng tối thiểu để trigger</label>
                            <input type="number" id="trigger_min_order_amount" name="trigger_min_order_amount" value="{{ old('trigger_min_order_amount', $voucher->trigger_conditions['min_order_amount'] ?? '') }}"
                                   min="0" step="1000"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="0">
                            <p class="text-xs text-gray-500 mt-1">Để trống nếu không giới hạn</p>
                        </div>
                    </div>

                    <!-- Advanced Settings với Tabs -->
                    <div class="mt-6">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button type="button" onclick="switchTab('trigger')" id="trigger-tab"
                                        class="tab-button active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600 flex items-center">
                                    <i class="fas fa-bullseye mr-2"></i>
                                    <div class="text-left">
                                        <div>Điều kiện kích hoạt</div>
                                        <div class="text-xs text-gray-500 font-normal">Khi nào tặng voucher?</div>
                                    </div>
                                </button>
                                <button type="button" onclick="switchTab('applicable')" id="applicable-tab"
                                        class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 flex items-center">
                                    <i class="fas fa-filter mr-2"></i>
                                    <div class="text-left">
                                        <div>Phạm vi áp dụng</div>
                                        <div class="text-xs text-gray-400 font-normal">Voucher dùng được cho gì?</div>
                                    </div>
                                </button>
                            </nav>
                        </div>

                        <!-- Tab Content -->
                        <div class="mt-4">
                            <!-- Trigger Tab -->
                            <div id="trigger-content" class="tab-content">
                                <div class="space-y-4">
                                    <!-- Explanation Box -->
                                    <div class="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                                        <div class="flex items-start">
                                            <i class="fas fa-lightbulb text-green-500 mt-1 mr-3"></i>
                                            <div>
                                                <h4 class="text-sm font-medium text-green-800 mb-1">Điều kiện kích hoạt là gì?</h4>
                                                <p class="text-sm text-green-700 mb-2">Xác định khi nào hệ thống sẽ tự động tặng voucher cho user.</p>
                                                <div class="text-xs text-green-600">
                                                    <strong>Ví dụ:</strong> User mua sản phẩm PUBG → Hệ thống tự động tặng voucher cho user
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Trigger Type Selection -->
                                    <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                        <label class="block text-sm font-medium text-gray-700 mb-3">
                                            <i class="fas fa-bullseye text-blue-500 mr-1"></i>
                                            Chọn loại điều kiện kích hoạt
                                        </label>
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                            <label class="flex items-center p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50">
                                                <input type="radio" name="trigger_type" value="all"
                                                       {{ old('trigger_type', $voucher->trigger_conditions['condition_type'] ?? 'all') == 'all' ? 'checked' : '' }}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                                       onchange="updateTriggerOptions()">
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">Tất cả đơn hàng</div>
                                                    <div class="text-xs text-gray-500">Mọi giao dịch đều trigger</div>
                                                </div>
                                            </label>
                                            <label class="flex items-center p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50">
                                                <input type="radio" name="trigger_type" value="categories"
                                                       {{ old('trigger_type', $voucher->trigger_conditions['condition_type'] ?? '') == 'categories' ? 'checked' : '' }}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                                       onchange="updateTriggerOptions()">
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">Theo danh mục</div>
                                                    <div class="text-xs text-gray-500">Chọn danh mục cụ thể</div>
                                                </div>
                                            </label>
                                            <label class="flex items-center p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50">
                                                <input type="radio" name="trigger_type" value="specific_items"
                                                       {{ old('trigger_type', $voucher->trigger_conditions['condition_type'] ?? '') == 'specific_items' ? 'checked' : '' }}
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                                       onchange="updateTriggerOptions()">
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">Sản phẩm/Dịch vụ cụ thể</div>
                                                    <div class="text-xs text-gray-500">Chọn items cụ thể</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Categories Trigger (Hidden by default) -->
                                    <div id="categories-trigger" class="trigger-option" style="display: none;">
                                        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
                                            <div class="flex items-center mb-2">
                                                <i class="fas fa-tags text-yellow-600 mr-2"></i>
                                                <span class="text-sm font-medium text-yellow-800">Danh mục kích hoạt</span>
                                            </div>
                                            <p class="text-xs text-yellow-700 mb-2">
                                                Chọn danh mục mà khi user mua sản phẩm/dịch vụ từ đó sẽ được tặng voucher
                                            </p>
                                            <div class="text-xs text-yellow-600 bg-yellow-100 p-2 rounded">
                                                <strong>Ví dụ:</strong> Chọn "PUBG" → User mua bất kỳ sản phẩm PUBG nào → Được tặng voucher
                                            </div>
                                        </div>

                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-tags text-green-500 mr-1"></i>
                                            Chọn danh mục kích hoạt
                                        </label>
                                        <div class="relative">
                                            <input type="text" id="trigger-categories-search" placeholder="Tìm kiếm danh mục..."
                                                   class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                   onkeyup="filterOptions('trigger_categories', this.value)">
                                            <select id="trigger_categories" name="trigger_categories[]" multiple
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                    style="height: 120px;">
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" {{ in_array($category->id, old('trigger_categories', $voucher->trigger_conditions['trigger_categories'] ?? [])) ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <p class="text-xs text-green-600 mt-1">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Khi user mua sản phẩm/dịch vụ từ danh mục đã chọn sẽ được tặng voucher
                                        </p>
                                    </div>

                                    <!-- Specific Items Trigger (Hidden by default) -->
                                    <div id="specific-items-trigger" class="trigger-option" style="display: none;">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <!-- Trigger Products -->
                                            <div id="trigger-products-section">
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    <i class="fas fa-box text-green-500 mr-1"></i>
                                                    Sản phẩm kích hoạt
                                                </label>
                                                <div class="relative">
                                                    <input type="text" id="trigger-products-search" placeholder="Tìm kiếm sản phẩm..."
                                                           class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                           onkeyup="filterOptions('trigger_products', this.value)">
                                                    <select id="trigger_products" name="trigger_products[]" multiple
                                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                            style="height: 120px;">
                                                        <!-- Options sẽ được load bằng JavaScript -->
                                                    </select>
                                                </div>
                                                <p class="text-xs text-gray-500 mt-1">Khi user mua các sản phẩm này sẽ được tặng voucher</p>
                                            </div>

                                            <!-- Trigger Services -->
                                            <div id="trigger-services-section">
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    <i class="fas fa-cogs text-purple-500 mr-1"></i>
                                                    Dịch vụ kích hoạt
                                                </label>
                                                <div class="relative">
                                                    <input type="text" id="trigger-services-search" placeholder="Tìm kiếm dịch vụ..."
                                                           class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                           onkeyup="filterOptions('trigger_services', this.value)">
                                                    <select id="trigger_services" name="trigger_services[]" multiple
                                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                            style="height: 120px;">
                                                        <!-- Options sẽ được load bằng JavaScript -->
                                                    </select>
                                                </div>
                                                <p class="text-xs text-gray-500 mt-1">Khi user mua các dịch vụ này sẽ được tặng voucher</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Applicable Tab -->
                            <div id="applicable-content" class="tab-content hidden">
                                <div class="space-y-4">
                                    <!-- Explanation Box -->
                                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-200 mb-4">
                                        <div class="flex items-start">
                                            <i class="fas fa-filter text-purple-500 mt-1 mr-3"></i>
                                            <div>
                                                <h4 class="text-sm font-medium text-purple-800 mb-1">Phạm vi áp dụng là gì?</h4>
                                                <p class="text-sm text-purple-700 mb-2">Xác định voucher có thể được sử dụng cho những sản phẩm/dịch vụ nào.</p>
                                                <div class="text-xs text-purple-600">
                                                    <strong>Ví dụ:</strong> Voucher chỉ dùng được cho dịch vụ Mobile Legends (không dùng được cho PUBG)
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                        <input type="checkbox" id="specific_items_only" name="specific_items_only" value="1"
                                               {{ old('specific_items_only', $voucher->specific_items_only) ? 'checked' : '' }}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                               onchange="toggleSpecificItems()">
                                        <label for="specific_items_only" class="ml-3 block text-sm font-medium text-gray-900">
                                            <i class="fas fa-filter text-yellow-600 mr-1"></i>
                                            Chỉ áp dụng cho sản phẩm/dịch vụ cụ thể
                                        </label>
                                    </div>

                                    <div class="text-xs text-gray-600 bg-gray-50 p-3 rounded-lg">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        <strong>Lưu ý:</strong> Nếu không check, voucher sẽ áp dụng theo cài đặt "Áp dụng cho" và "Danh mục áp dụng voucher" ở phần trên
                                    </div>

                                    <div id="specific-items-settings" style="display: none;" class="space-y-4">
                                        <!-- Products & Services trong cùng 1 container -->
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <!-- Sản phẩm -->
                                            <div id="products-section" style="display: none;">
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    <i class="fas fa-box text-green-500 mr-1"></i>
                                                    Sản phẩm áp dụng
                                                </label>
                                                <select id="applicable_products" name="applicable_products[]" multiple
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                        style="height: 120px;">
                                                    <!-- Options sẽ được load bằng JavaScript -->
                                                </select>
                                                <p class="text-xs text-gray-500 mt-1">Voucher chỉ dùng được cho các sản phẩm này</p>
                                            </div>

                                            <!-- Dịch vụ -->
                                            <div id="services-section" style="display: none;">
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    <i class="fas fa-cogs text-purple-500 mr-1"></i>
                                                    Dịch vụ áp dụng
                                                </label>
                                                <select id="applicable_services" name="applicable_services[]" multiple
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                        style="height: 120px;">
                                                    <!-- Options sẽ được load bằng JavaScript -->
                                                </select>
                                                <p class="text-xs text-gray-500 mt-1">Voucher chỉ dùng được cho các dịch vụ này</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Example Box -->
                <div class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                    <h4 class="text-sm font-medium text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        Ví dụ thực tế về Auto Voucher
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                        <div class="bg-white p-3 rounded border border-green-200">
                            <div class="font-medium text-green-800 mb-2">🎯 Chiến lược Cross-selling</div>
                            <div class="space-y-1 text-gray-700">
                                <div><strong>Danh mục áp dụng voucher:</strong> Mobile Legends</div>
                                <div><strong>Điều kiện kích hoạt:</strong> Mua từ danh mục PUBG</div>
                                <div><strong>Kết quả:</strong> User mua PUBG → Tặng voucher cho Mobile Legends</div>
                            </div>
                        </div>
                        <div class="bg-white p-3 rounded border border-blue-200">
                            <div class="font-medium text-blue-800 mb-2">🎁 Chiến lược Upselling</div>
                            <div class="space-y-1 text-gray-700">
                                <div><strong>Phạm vi áp dụng:</strong> Dịch vụ VIP</div>
                                <div><strong>Điều kiện kích hoạt:</strong> Mua account cơ bản</div>
                                <div><strong>Kết quả:</strong> User mua account → Tặng voucher cho dịch vụ VIP</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-end space-x-4 mt-8">
                    <a href="{{ route('admin.vouchers.index') }}" 
                       class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200">
                        Hủy
                    </a>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        Cập nhật Voucher
                    </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<style>
.tab-button {
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    padding: 12px 16px;
}

.tab-button:hover {
    color: #374151 !important;
    border-color: #d1d5db !important;
    background-color: #f9fafb;
}

.tab-button.active {
    color: #2563eb !important;
    border-color: #2563eb !important;
    background-color: #eff6ff;
}

.tab-button.active .text-gray-500 {
    color: #6b7280 !important;
}

.tab-button.active .text-gray-400 {
    color: #9ca3af !important;
}

.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Custom select styling */
select[multiple] {
    background-image: none;
}

select[multiple] option {
    padding: 8px 12px;
    border-bottom: 1px solid #f3f4f6;
}

select[multiple] option:hover {
    background-color: #f3f4f6;
}

select[multiple] option:checked {
    background-color: #dbeafe;
    color: #1d4ed8;
}
</style>
@endpush

@push('scripts')
<script>
function toggleAutoVoucherFields() {
    // In edit form, voucher type cannot be changed, so we don't toggle display
    // Display is already set correctly in the Blade template based on $voucher->type
    const type = document.getElementById('type').value;
    const userExpiresDays = document.getElementById('user_expires_days');

    if (type === 'auto_product_purchase') {
        if (userExpiresDays) {
            userExpiresDays.required = true;
        }
    } else {
        if (userExpiresDays) {
            userExpiresDays.required = false;
        }
    }
}

function toggleDiscountType() {
    const discountType = document.getElementById('discount_type').value;
    const discountInput = document.getElementById('discount_value');
    const discountLabel = document.getElementById('discount_label');
    const discountHint = document.getElementById('discount_hint');

    if (discountType === 'percentage') {
        discountInput.max = '100';
        discountInput.placeholder = '10';
        discountLabel.textContent = 'Giảm giá (%) *';
        discountHint.textContent = 'Nhập phần trăm giảm giá (0-100)';
    } else {
        discountInput.removeAttribute('max');
        discountInput.placeholder = '50000';
        discountLabel.textContent = 'Giảm giá (VND) *';
        discountHint.textContent = 'Nhập số tiền giảm giá cố định';
    }
}

// Tab switching function
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');

    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeTab.classList.remove('border-transparent', 'text-gray-500');
}

// Update trigger options based on selected type
function updateTriggerOptions() {
    const triggerType = document.querySelector('input[name="trigger_type"]:checked').value;

    // Hide all trigger options
    document.querySelectorAll('.trigger-option').forEach(option => {
        option.style.display = 'none';
    });

    // Show relevant trigger option
    if (triggerType === 'categories') {
        document.getElementById('categories-trigger').style.display = 'block';
    } else if (triggerType === 'specific_items') {
        document.getElementById('specific-items-trigger').style.display = 'block';
        populateTriggerItems();
    }
}

// Populate trigger products and services
function populateTriggerItems() {
    populateTriggerProducts();
    populateTriggerServices();
}

function populateTriggerProducts() {
    const categoryId = document.getElementById('category_id').value;
    const select = document.getElementById('trigger_products');
    select.innerHTML = '';

    // Filter products theo category của voucher (nếu có)
    let filteredProducts = allProducts;
    if (categoryId && categoryId !== '') {
        filteredProducts = allProducts.filter(product => product.category_id == categoryId);
    }

    if (filteredProducts.length === 0) {
        select.innerHTML = '<option value="">Không có sản phẩm nào</option>';
        return;
    }

    // Get existing selected trigger products
    const existingTriggerProducts = @json(old('trigger_products', $voucher->trigger_conditions['trigger_products'] ?? []));

    filteredProducts.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        const categoryName = product.category ? product.category.name : 'Chưa phân loại';
        option.textContent = `${product.name} (${categoryName})`;

        // Select if this product was previously selected as trigger
        if (existingTriggerProducts.includes(String(product.id)) ||
            existingTriggerProducts.includes(parseInt(product.id)) ||
            existingTriggerProducts.includes(product.id)) {
            option.selected = true;
        }

        select.appendChild(option);
    });
}

function populateTriggerServices() {
    const categoryId = document.getElementById('category_id').value;
    const select = document.getElementById('trigger_services');
    select.innerHTML = '';

    // Filter services theo category của voucher (nếu có)
    let filteredServices = allServices;
    if (categoryId && categoryId !== '') {
        filteredServices = allServices.filter(service => service.category_id == categoryId);
    }

    if (filteredServices.length === 0) {
        select.innerHTML = '<option value="">Không có dịch vụ nào</option>';
        return;
    }

    // Get existing selected trigger services
    const existingTriggerServices = @json(old('trigger_services', $voucher->trigger_conditions['trigger_services'] ?? []));

    filteredServices.forEach(service => {
        const option = document.createElement('option');
        option.value = service.id;
        const categoryName = service.category ? service.category.name : 'Chưa phân loại';
        option.textContent = `${service.name} (${categoryName})`;

        // Select if this service was previously selected as trigger
        if (existingTriggerServices.includes(String(service.id)) ||
            existingTriggerServices.includes(parseInt(service.id)) ||
            existingTriggerServices.includes(service.id)) {
            option.selected = true;
        }

        select.appendChild(option);
    });
}

// Filter options function
function filterOptions(selectId, searchValue) {
    const select = document.getElementById(selectId);
    const options = select.querySelectorAll('option');

    options.forEach(option => {
        const text = option.textContent.toLowerCase();
        const search = searchValue.toLowerCase();

        if (text.includes(search)) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
}

function toggleSpecificItems() {
    const checkbox = document.getElementById('specific_items_only');
    const settings = document.getElementById('specific-items-settings');
    const applicableTo = document.getElementById('applicable_to').value;

    if (checkbox.checked) {
        settings.style.display = 'block';
        updateSpecificItemsSections();
    } else {
        settings.style.display = 'none';
    }
}

function updateSpecificItemsSections() {
    const applicableTo = document.getElementById('applicable_to').value;
    const productsSection = document.getElementById('products-section');
    const servicesSection = document.getElementById('services-section');

    // Reset visibility
    productsSection.style.display = 'none';
    servicesSection.style.display = 'none';

    // Show relevant sections
    if (applicableTo === 'products' || applicableTo === 'both') {
        productsSection.style.display = 'block';
        populateProducts();
    }

    if (applicableTo === 'services' || applicableTo === 'both') {
        servicesSection.style.display = 'block';
        populateServices();
    }
}

// Data từ server
const allProducts = @json($products);
const allServices = @json($services);



function populateProducts() {
    const categoryId = document.getElementById('category_id').value;
    const select = document.getElementById('applicable_products');
    const container = select.parentElement;

    // Clear existing options and search
    select.innerHTML = '';

    // Remove existing search if any
    const existingSearch = container.querySelector('.product-search');
    if (existingSearch) existingSearch.remove();

    // Filter products theo category
    let filteredProducts = allProducts;
    if (categoryId && categoryId !== '') {
        filteredProducts = allProducts.filter(product => product.category_id == categoryId);
    }

    if (filteredProducts.length === 0) {
        select.innerHTML = '<option value="">Không có sản phẩm nào</option>';
        return;
    }

    // Get existing selected products from voucher applicable items
    const existingApplicableProducts = @json($voucher->applicableProducts->pluck('applicable_id')->toArray());

    // Add search input
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'product-search w-full px-3 py-2 mb-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm';
    searchInput.placeholder = 'Tìm kiếm sản phẩm...';
    searchInput.onkeyup = () => filterOptions('applicable_products', searchInput.value);
    container.insertBefore(searchInput, select);

    filteredProducts.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        const categoryName = product.category ? product.category.name : 'Chưa phân loại';
        option.textContent = `${product.name} (${categoryName})`;

        // Select if this product was previously selected
        if (existingApplicableProducts.includes(String(product.id)) ||
            existingApplicableProducts.includes(parseInt(product.id)) ||
            existingApplicableProducts.includes(product.id)) {
            option.selected = true;
        }

        select.appendChild(option);
    });
}

function populateServices() {
    const categoryId = document.getElementById('category_id').value;
    const select = document.getElementById('applicable_services');
    const container = select.parentElement;

    // Clear existing options and search
    select.innerHTML = '';

    // Remove existing search if any
    const existingSearch = container.querySelector('.service-search');
    if (existingSearch) existingSearch.remove();

    // Filter services theo category
    let filteredServices = allServices;
    if (categoryId && categoryId !== '') {
        filteredServices = allServices.filter(service => service.category_id == categoryId);
    }

    if (filteredServices.length === 0) {
        select.innerHTML = '<option value="">Không có dịch vụ nào</option>';
        return;
    }

    // Get existing selected services from voucher applicable items
    const existingApplicableServices = @json($voucher->applicableServices->pluck('applicable_id')->toArray());

    // Add search input
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'service-search w-full px-3 py-2 mb-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm';
    searchInput.placeholder = 'Tìm kiếm dịch vụ...';
    searchInput.onkeyup = () => filterOptions('applicable_services', searchInput.value);
    container.insertBefore(searchInput, select);

    filteredServices.forEach(service => {
        const option = document.createElement('option');
        option.value = service.id;
        const categoryName = service.category ? service.category.name : 'Chưa phân loại';
        option.textContent = `${service.name} (${categoryName})`;

        // Select if this service was previously selected
        if (existingApplicableServices.includes(String(service.id)) ||
            existingApplicableServices.includes(parseInt(service.id)) ||
            existingApplicableServices.includes(service.id)) {
            option.selected = true;
        }

        select.appendChild(option);
    });
}

// Category search functionality
let categorySearchTimeout;
const allCategories = @json($categories);

function setupCategorySearch() {
    const searchInput = document.getElementById('category_search');
    const hiddenInput = document.getElementById('category_id');
    const dropdown = document.getElementById('category_dropdown');

    searchInput.addEventListener('input', function() {
        clearTimeout(categorySearchTimeout);
        const query = this.value.trim().toLowerCase();

        categorySearchTimeout = setTimeout(() => {
            if (query === '') {
                hiddenInput.value = '';
                hideDropdown();
                return;
            }

            const filteredCategories = allCategories.filter(category =>
                category.name.toLowerCase().includes(query)
            );

            displayCategoryResults(filteredCategories);
        }, 300);
    });

    searchInput.addEventListener('focus', function() {
        if (this.value.trim() !== '') {
            const query = this.value.trim().toLowerCase();
            const filteredCategories = allCategories.filter(category =>
                category.name.toLowerCase().includes(query)
            );
            displayCategoryResults(filteredCategories);
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            hideDropdown();
        }
    });
}

function displayCategoryResults(categories) {
    const dropdown = document.getElementById('category_dropdown');

    if (categories.length === 0) {
        dropdown.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm">Không tìm thấy danh mục nào</div>';
    } else {
        dropdown.innerHTML = categories.map(category => `
            <div class="px-3 py-2 hover:bg-gray-100 cursor-pointer category-item"
                 data-id="${category.id}"
                 data-name="${category.name}">
                ${category.name}
            </div>
        `).join('');

        // Add click handlers
        dropdown.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                selectCategory(this.dataset.id, this.dataset.name);
            });
        });
    }

    dropdown.classList.remove('hidden');
}

function selectCategory(id, name) {
    document.getElementById('category_search').value = name;
    document.getElementById('category_id').value = id;
    hideDropdown();

    // Trigger change event for other listeners
    if (document.getElementById('specific_items_only').checked) {
        updateSpecificItemsSections();
    }

    // Update trigger items when category changes
    if (document.querySelector('input[name="trigger_type"]:checked') &&
        document.querySelector('input[name="trigger_type"]:checked').value === 'specific_items') {
        populateTriggerItems();
    }
}

function hideDropdown() {
    document.getElementById('category_dropdown').classList.add('hidden');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleAutoVoucherFields();
    toggleSpecificItems();
    toggleDiscountType(); // Initialize discount type
    updateTriggerOptions(); // Initialize trigger options
    setupCategorySearch(); // Initialize category search

    // Auto populate trigger items if this is edit form and trigger type is specific_items
    const currentTriggerType = '{{ old("trigger_type", $voucher->trigger_conditions["condition_type"] ?? "all") }}';
    if (currentTriggerType === 'specific_items') {
        // Small delay to ensure DOM is ready
        setTimeout(() => {
            populateTriggerItems();
        }, 100);
    }

    // Auto populate applicable items if specific_items_only is checked
    const specificItemsOnly = {{ $voucher->specific_items_only ? 'true' : 'false' }};
    if (specificItemsOnly) {
        // Small delay to ensure DOM is ready
        setTimeout(() => {
            updateSpecificItemsSections();
        }, 150);
    }

    // Listen for applicable_to changes
    document.getElementById('applicable_to').addEventListener('change', function() {
        if (document.getElementById('specific_items_only').checked) {
            updateSpecificItemsSections();
        }
    });
});
</script>
@endpush
