<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Voucher;
use App\Models\Category;
use App\Services\VoucherService;
use Illuminate\Support\Facades\Auth;

class VoucherController extends Controller
{
    protected $voucherService;

    public function __construct(VoucherService $voucherService)
    {
        $this->voucherService = $voucherService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Voucher::with(['category', 'createdByAdmin'])
            ->withCount(['userVouchers', 'userVouchers as used_count' => function ($query) {
                $query->whereNotNull('used_at');
            }]); // Sử dụng withCount thay vì load tất cả userVouchers

        // Include trashed vouchers if requested
        if ($request->filled('show_archived') && $request->show_archived === '1') {
            $query->withTrashed();
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'archived') {
                $query->onlyTrashed();
            } elseif ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $vouchers = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.vouchers.index', compact('vouchers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::orderBy('name')->get();
        $products = \App\Models\Product::with('category:id,name')
            ->select('id', 'name', 'category_id')
            ->orderBy('name')
            ->get();
        $services = \App\Models\Service::with('category:id,name')
            ->select('id', 'name', 'category_id')
            ->orderBy('name')
            ->get();

        return view('admin.vouchers.create', compact('categories', 'products', 'services'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $baseValidation = [
            'code' => 'required|string|unique:vouchers,code|max:50',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:manual,auto_product_purchase',
            'discount_type' => 'required|in:percentage,fixed',
            'discount_value' => [
                'required',
                'numeric',
                'min:0',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->discount_type === 'percentage' && $value > 100) {
                        $fail('Phần trăm giảm giá không được vượt quá 100%.');
                    }
                    if ($request->discount_type === 'fixed' && $value < 0) {
                        $fail('Số tiền giảm giá không được âm.');
                    }
                },
            ],
            'max_discount_amount' => 'nullable|numeric|min:0',
            'applicable_to' => 'required|in:products,services,both',
            'category_id' => 'nullable|exists:categories,id',
            'min_order_amount' => 'nullable|numeric|min:0',
            'max_uses_per_user' => 'required|integer|min:1',
            'total_uses_limit' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'specific_items_only' => 'boolean',
        ];

        // Validation cho auto voucher
        if ($request->type === 'auto_product_purchase') {
            $baseValidation['user_expires_days'] = 'required|integer|min:1|max:365';
            $baseValidation['trigger_categories'] = 'nullable|array';
            $baseValidation['trigger_categories.*'] = 'exists:categories,id';
            $baseValidation['trigger_min_order_amount'] = 'nullable|numeric|min:0';
        }

        // Validation cho specific items
        if ($request->specific_items_only) {
            $baseValidation['applicable_products'] = 'nullable|array';
            $baseValidation['applicable_products.*'] = 'exists:products,id';
            $baseValidation['applicable_services'] = 'nullable|array';
            $baseValidation['applicable_services.*'] = 'exists:services,id';
        }

        // Thêm validation tùy theo discount_type
        if ($request->discount_type === 'percentage') {
            $baseValidation['discount_value'] = 'required|numeric|min:0|max:100';
        } else {
            $baseValidation['discount_value'] = 'required|numeric|min:0';
        }

        $request->validate($baseValidation);

        // Additional validation for specific items
        if ($request->boolean('specific_items_only')) {
            $hasProducts = $request->filled('applicable_products') && !empty($request->applicable_products);
            $hasServices = $request->filled('applicable_services') && !empty($request->applicable_services);

            if (!$hasProducts && !$hasServices) {
                return back()->withErrors([
                    'specific_items_only' => 'Khi chọn "Chỉ áp dụng cho sản phẩm/dịch vụ cụ thể", bạn phải chọn ít nhất một sản phẩm hoặc dịch vụ.'
                ])->withInput();
            }
        }

        $data = [
            'code' => strtoupper($request->code),
            'name' => $request->name,
            'description' => $request->description,
            'type' => $request->type,
            'discount_type' => $request->discount_type,
            'discount_value' => $request->discount_value,
            'max_discount_amount' => $request->max_discount_amount,
            'applicable_to' => $request->applicable_to,
            'category_id' => $request->category_id,
            'min_order_amount' => $request->min_order_amount,
            'max_uses_per_user' => $request->max_uses_per_user,
            'total_uses_limit' => $request->total_uses_limit,
            'expires_at' => $request->expires_at,
            'is_active' => true,
            'created_by_admin_id' => Auth::id(),
            'specific_items_only' => $request->boolean('specific_items_only'),
        ];

        // Xử lý trigger conditions cho auto voucher
        if ($request->type === 'auto_product_purchase') {
            $data['user_expires_days'] = $request->user_expires_days;

            $triggerConditions = [
                'trigger_type' => 'product_purchase',
                'min_order_amount' => $request->trigger_min_order_amount,
            ];

            // Xử lý trigger condition type
            $triggerType = $request->trigger_type ?? 'all';
            $triggerConditions['condition_type'] = $triggerType;

            if ($triggerType === 'categories') {
                $triggerConditions['trigger_categories'] = $request->trigger_categories ?? [];
            } elseif ($triggerType === 'specific_items') {
                $triggerConditions['trigger_products'] = $request->trigger_products ?? [];
                $triggerConditions['trigger_services'] = $request->trigger_services ?? [];
            }

            $data['trigger_conditions'] = $triggerConditions;
        }

        $voucher = Voucher::create($data);

        // Lưu specific items nếu có
        if ($request->boolean('specific_items_only')) {
            $this->saveApplicableItems($voucher, $request);
        }

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Voucher $voucher)
    {
        $voucher->load(['category', 'createdByAdmin', 'applicableItems']);

        // Sử dụng withCount để tránh load tất cả userVouchers
        $voucher->loadCount([
            'userVouchers as total_granted',
            'userVouchers as total_used' => function ($query) {
                $query->whereNotNull('used_at');
            }
        ]);

        $usageStats = [
            'total_granted' => $voucher->total_granted,
            'total_used' => $voucher->total_used,
            'usage_rate' => $voucher->total_granted > 0 ? round(($voucher->total_used / $voucher->total_granted) * 100, 2) : 0
        ];

        // Load recent user vouchers với pagination để tránh load quá nhiều
        $recentUserVouchers = $voucher->userVouchers()
            ->with(['user:id,name,email', 'order:id,order_code', 'serviceOrder:id,order_code'])
            ->latest()
            ->limit(20)
            ->get();

        return view('admin.vouchers.show', compact('voucher', 'usageStats', 'recentUserVouchers'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Voucher $voucher)
    {
        $categories = Category::orderBy('name')->get();
        $products = \App\Models\Product::with('category:id,name')
            ->select('id', 'name', 'category_id')
            ->orderBy('name')
            ->get();
        $services = \App\Models\Service::with('category:id,name')
            ->select('id', 'name', 'category_id')
            ->orderBy('name')
            ->get();

        // Load applicable items if exists
        $voucher->load(['applicableItems', 'applicableProducts', 'applicableServices']);

        return view('admin.vouchers.edit', compact('voucher', 'categories', 'products', 'services'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Voucher $voucher)
    {
        $baseValidation = [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'discount_type' => 'required|in:percentage,fixed',
            'discount_value' => 'required|numeric|min:0',
            'applicable_to' => 'required|in:products,services,both',
            'category_id' => 'nullable|exists:categories,id',
            'min_order_amount' => 'nullable|numeric|min:0',
            'max_uses_per_user' => 'required|integer|min:1',
            'total_uses_limit' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
        ];

        // Validation cho auto voucher (nếu là auto voucher)
        if ($voucher->type === 'auto_product_purchase') {
            $baseValidation['user_expires_days'] = 'required|integer|min:1|max:365';
            $baseValidation['trigger_categories'] = 'nullable|array';
            $baseValidation['trigger_categories.*'] = 'exists:categories,id';
            $baseValidation['trigger_products'] = 'nullable|array';
            $baseValidation['trigger_products.*'] = 'exists:products,id';
            $baseValidation['trigger_services'] = 'nullable|array';
            $baseValidation['trigger_services.*'] = 'exists:services,id';
            $baseValidation['trigger_min_order_amount'] = 'nullable|numeric|min:0';
            $baseValidation['applicable_products'] = 'nullable|array';
            $baseValidation['applicable_products.*'] = 'exists:products,id';
            $baseValidation['applicable_services'] = 'nullable|array';
            $baseValidation['applicable_services.*'] = 'exists:services,id';
        }

        // Thêm validation tùy theo discount_type
        if ($request->discount_type === 'percentage') {
            $baseValidation['discount_value'] = 'required|numeric|min:0|max:100';
        } else {
            $baseValidation['discount_value'] = 'required|numeric|min:0';
        }

        $request->validate($baseValidation);

        $data = [
            'name' => $request->name,
            'description' => $request->description,
            'discount_type' => $request->discount_type,
            'discount_value' => $request->discount_value,
            'applicable_to' => $request->applicable_to,
            'category_id' => $request->category_id,
            'min_order_amount' => $request->min_order_amount,
            'max_uses_per_user' => $request->max_uses_per_user,
            'total_uses_limit' => $request->total_uses_limit,
            'expires_at' => $request->expires_at,
            'specific_items_only' => $request->boolean('specific_items_only'),
        ];

        // Cập nhật auto config nếu là auto voucher
        if ($voucher->type === 'auto_product_purchase') {
            $data['user_expires_days'] = $request->user_expires_days;

            // Trigger conditions - Sử dụng cấu trúc nhất quán với store method
            $triggerConditions = [
                'trigger_type' => 'product_purchase',
                'min_order_amount' => $request->trigger_min_order_amount,
            ];

            // Xử lý trigger condition type
            $triggerType = $request->trigger_type ?? 'all';
            $triggerConditions['condition_type'] = $triggerType;

            if ($triggerType === 'categories') {
                $triggerConditions['trigger_categories'] = $request->trigger_categories ?? [];
            } elseif ($triggerType === 'specific_items') {
                $triggerConditions['trigger_products'] = $request->trigger_products ?? [];
                $triggerConditions['trigger_services'] = $request->trigger_services ?? [];
            }

            $data['trigger_conditions'] = $triggerConditions;
        }

        $voucher->update($data);

        // Cập nhật specific items nếu có
        if ($request->boolean('specific_items_only')) {
            $this->saveApplicableItems($voucher, $request);
        } else {
            // Xóa tất cả applicable items nếu không specific
            $voucher->applicableItems()->delete();
        }

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher đã được cập nhật thành công!');
    }

    /**
     * Toggle voucher status
     */
    public function toggleStatus(Voucher $voucher)
    {
        $voucher->update(['is_active' => !$voucher->is_active]);

        // Model event sẽ tự động cleanup user_vouchers khi deactivate

        $status = $voucher->is_active ? 'kích hoạt' : 'vô hiệu hóa';
        return redirect()->back()->with('success', "Voucher đã được {$status}!");
    }

    /**
     * Soft delete voucher (archive)
     */
    public function destroy(Voucher $voucher)
    {
        // Soft delete voucher (không xóa thật)
        $voucher->delete();

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher đã được lưu trữ (archived) thành công!');
    }

    /**
     * Restore soft deleted voucher
     */
    public function restore($id)
    {
        $voucher = Voucher::withTrashed()->findOrFail($id);
        $voucher->restore();

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher đã được khôi phục thành công!');
    }

    /**
     * Permanently delete voucher (chỉ admin cấp cao)
     */
    public function forceDelete($id)
    {
        $voucher = Voucher::withTrashed()->findOrFail($id);

        // Kiểm tra có user_vouchers đã sử dụng không
        if ($voucher->userVouchers()->whereNotNull('used_at')->exists()) {
            return redirect()->back()->with('error', 'Không thể xóa vĩnh viễn voucher đã được sử dụng!');
        }

        // Xóa vĩnh viễn
        $voucher->forceDelete();

        return redirect()->route('admin.vouchers.index')
            ->with('success', 'Voucher đã được xóa vĩnh viễn!');
    }

    /**
     * Lưu applicable items cho voucher
     */
    private function saveApplicableItems(Voucher $voucher, $request)
    {
        // Xóa các items cũ
        $voucher->applicableItems()->delete();

        // Lưu products
        if ($request->applicable_products) {
            foreach ($request->applicable_products as $productId) {
                $voucher->applicableItems()->create([
                    'applicable_type' => 'product',
                    'applicable_id' => $productId,
                ]);
            }
        }

        // Lưu services
        if ($request->applicable_services) {
            foreach ($request->applicable_services as $serviceId) {
                $voucher->applicableItems()->create([
                    'applicable_type' => 'service',
                    'applicable_id' => $serviceId,
                ]);
            }
        }
    }
}
