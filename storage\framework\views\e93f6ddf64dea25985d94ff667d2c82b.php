<?php $__env->startSection('title', 'Quản lý Dịch vụ'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Dịch vụ</h1>
            <p class="text-gray-600 mt-1">Quản lý các dịch vụ và gói dịch vụ cho khách hàng</p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo e(route('admin.services.create')); ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Thêm Dịch vụ
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <form method="GET" class="flex flex-wrap items-center gap-3">
            <div class="flex-1 min-w-0 sm:min-w-64">
                <input type="text" name="search" value="<?php echo e(request('search')); ?>"
                       placeholder="Tìm kiếm dịch vụ..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <!-- Category search -->
            <div class="w-48 relative">
                <input type="hidden" name="category_id" id="category_id" value="<?php echo e(request('category_id')); ?>">
                <input type="text" id="category_search"
                       placeholder="Tìm danh mục..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       autocomplete="off">
                <div id="category_dropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden max-h-60 overflow-y-auto">
                    <!-- Dropdown items will be populated by JavaScript -->
                </div>
            </div>
            <div class="w-48">
                <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Tất cả trạng thái</option>
                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Tạm dừng</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                <i class="fas fa-search mr-2"></i>Tìm kiếm
            </button>
        </form>
    </div>

    <!-- Services Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Tên dịch vụ
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Danh mục
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Giá
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Trạng thái
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Ngày tạo
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Thao tác
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($service->id); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($service->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e(Str::limit($service->description, 50)); ?></div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($service->category->name); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($service->formatted_price); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php if($service->status === 'active'): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>Hoạt động
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-pause mr-1"></i>Tạm dừng
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($service->created_at->format('d/m/Y H:i')); ?>

                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.services.edit', $service)); ?>"
                                       class="text-blue-600 hover:text-blue-900 transition-colors" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="confirmDelete(<?php echo e($service->id); ?>)"
                                            class="text-red-600 hover:text-red-900 transition-colors" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-4 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-tools text-4xl mb-4 block"></i>
                                    <p class="text-lg">Chưa có dịch vụ nào</p>
                                    <p class="text-sm">Hãy thêm dịch vụ đầu tiên</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($services->hasPages()): ?>
            <div class="px-4 py-3 border-t border-gray-200">
                <?php echo e($services->appends(request()->query())->links('pagination.custom')); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function confirmDelete(serviceId) {
    if (confirm('Bạn có chắc muốn xóa dịch vụ này? Hành động này không thể hoàn tác!')) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/<?php echo e(env('ADMIN_SECRET_KEY', 'admin-secret')); ?>/admin/services/${serviceId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);

        // Add method override
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Category search functionality
document.addEventListener('DOMContentLoaded', function() {
    const categorySearch = document.getElementById('category_search');
    const categoryId = document.getElementById('category_id');
    const dropdown = document.getElementById('category_dropdown');

    // Set initial value if category is selected
    const currentCategoryId = '<?php echo e(request("category_id")); ?>';
    if (currentCategoryId) {
        categoryId.value = currentCategoryId;
        // Find and set the category name from the categories passed to view
        const categories = <?php echo json_encode($categories, 15, 512) ?>;
        const selectedCategory = categories.find(cat => cat.id == currentCategoryId);
        if (selectedCategory) {
            categorySearch.value = selectedCategory.name;
        }
    }

    let searchTimeout;

    categorySearch.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                searchCategories(query);
            }, 300);
        } else {
            hideDropdown();
            if (query.length === 0) {
                categoryId.value = '';
            }
        }
    });

    categorySearch.addEventListener('focus', function() {
        if (this.value.length >= 2) {
            searchCategories(this.value);
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!categorySearch.contains(e.target) && !dropdown.contains(e.target)) {
            hideDropdown();
        }
    });

    function searchCategories(query) {
        fetch(`<?php echo e(route('api.admin.search.categories')); ?>?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                showDropdown(data);
            })
            .catch(error => {
                console.error('Error:', error);
                hideDropdown();
            });
    }

    function showDropdown(categories) {
        dropdown.innerHTML = '';

        if (categories.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'px-3 py-2 text-sm text-gray-500';
            noResults.textContent = 'Không tìm thấy danh mục';
            dropdown.appendChild(noResults);
        } else {
            // Add "All categories" option
            const allOption = document.createElement('div');
            allOption.className = 'px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer border-b border-gray-100';
            allOption.textContent = 'Tất cả danh mục';
            allOption.addEventListener('click', function() {
                categorySearch.value = '';
                categoryId.value = '';
                hideDropdown();
            });
            dropdown.appendChild(allOption);

            categories.forEach(category => {
                const item = document.createElement('div');
                item.className = 'px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer';
                item.textContent = category.name;
                item.addEventListener('click', function() {
                    categorySearch.value = category.name;
                    categoryId.value = category.id;
                    hideDropdown();
                });
                dropdown.appendChild(item);
            });
        }

        dropdown.classList.remove('hidden');
    }

    function hideDropdown() {
        dropdown.classList.add('hidden');
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/services/index.blade.php ENDPATH**/ ?>