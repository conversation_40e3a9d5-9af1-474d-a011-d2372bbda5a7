<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('topup_transactions', function (Blueprint $table) {
            $table->id();
            $table->enum('method', ['atm', 'card']); // Phương thức nạp tiền
            $table->unsignedBigInteger('user_id');
            $table->decimal('amount', 15, 2); // Số tiền nạp
            $table->enum('status', ['pending', 'completed', 'failed'])->default('pending');
            
            // Thông tin chung
            $table->string('transaction_code')->nullable(); // Mã giao dịch
            $table->text('note')->nullable(); // Ghi chú
            
            // Thông tin SePay (cho ATM)
            $table->string('sepay_id')->nullable(); // ID từ SePay
            $table->string('gateway')->nullable();
            $table->string('transactionDate')->nullable();
            $table->string('accountNumber')->nullable();
            $table->string('subAccount')->nullable();
            $table->string('code')->nullable();
            $table->string('content')->nullable();
            $table->string('transferType')->nullable();
            $table->string('description', 1000)->nullable();
            $table->string('referenceCode')->nullable();
            
            // Thông tin Card (cho tương lai)
            $table->string('card_type')->nullable(); // Loại thẻ
            $table->string('card_serial')->nullable(); // Serial thẻ
            $table->string('card_code')->nullable(); // Mã thẻ
            
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'status', 'method']);
            $table->index(['sepay_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('topup_transactions');
    }
};
