<?php

namespace App\Services;

use App\Models\BalanceTransaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class UserService
{
    /**
     * L<PERSON>y danh sách users với filter và search
     */
    public function getUsersList(array $filters = [], $perPage = 15)
    {
        $query = User::withCount(['orders', 'topupTransactions']);

        // Search
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function($q) use ($search) {
                $q->where('username', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('id', $search);
            });
        }

        // Filter by status
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $query->whereNotNull('email_verified_at');
            } else {
                $query->whereNull('email_verified_at');
            }
        }

        // Filter by role
        if (!empty($filters['role'])) {
            $query->where('role', $filters['role']);
        }

        return $query->latest()->paginate($perPage);
    }

    /**
     * Lấy thống kê users
     */
    public function getUserStats()
    {
        return [
            'total_users' => User::count(),
            'active_users' => User::whereNotNull('email_verified_at')->count(),
            'pending_users' => User::whereNull('email_verified_at')->count(),
            'admin_users' => User::where('role', 'admin')->count(),
            'total_balance' => User::where('role', 'user')->sum('balance'),
        ];
    }

    /**
     * Tạo user mới
     */
    public function createUser(array $userData)
    {
        $data = [
            'username' => $userData['username'],
            'display_name' => $userData['display_name'] ?? null,
            'email' => $userData['email'],
            'password' => Hash::make($userData['password']),
            'role' => $userData['role'],
            'balance' => $userData['balance'] ?? 0,
            'email_verified_at' => isset($userData['email_verified']) && $userData['email_verified'] ? now() : null,
        ];

        return User::create($data);
    }

    /**
     * Cập nhật user
     */
    public function updateUser(User $user, array $userData)
    {
        $data = [
            'username' => $userData['username'],
            'display_name' => $userData['display_name'] ?? null,
            'email' => $userData['email'],
            'role' => $userData['role'],
            'balance' => $userData['balance'] ?? 0,
            'email_verified_at' => isset($userData['email_verified']) && $userData['email_verified']
                ? ($user->email_verified_at ?? now()) : null,
        ];

        if (!empty($userData['password'])) {
            $data['password'] = Hash::make($userData['password']);
        }

        $user->update($data);
        return $user;
    }

    /**
     * Xóa user (kiểm tra điều kiện)
     */
    public function deleteUser(User $user)
    {
        if ($user->orders()->count() > 0) {
            throw new \Exception('Không thể xóa user đã có đơn hàng!');
        }

        return $user->delete();
    }

    /**
     * Toggle trạng thái active/inactive
     */
    public function toggleUserStatus(User $user)
    {
        $user->update([
            'is_active' => !$user->is_active
        ]);

        return $user->is_active ? 'kích hoạt' : 'vô hiệu hóa';
    }

    /**
     * Điều chỉnh balance user (cộng/trừ tiền)
     */
    public function adjustUserBalance(User $user, User $admin, string $type, int $amount, string $reason)
    {
        // Kiểm tra nếu trừ tiền, user có đủ balance không
        if ($type === 'subtract' && $user->balance < $amount) {
            throw ValidationException::withMessages([
                'amount' => 'User không đủ số dư để trừ ' . number_format($amount) . 'đ'
            ]);
        }

        return DB::transaction(function () use ($user, $admin, $type, $amount, $reason) {
            // Tạo balance transaction log
            $transactionType = $type === 'add' 
                ? BalanceTransaction::TYPE_ADMIN_ADD 
                : BalanceTransaction::TYPE_ADMIN_SUBTRACT;
            
            $transactionAmount = $type === 'add' ? $amount : -$amount;

            BalanceTransaction::createTransaction(
                user: $user,
                type: $transactionType,
                amount: $transactionAmount,
                description: $reason,
                admin: $admin,
                metadata: [
                    'admin_username' => $admin->username,
                    'adjustment_type' => $type,
                    'original_amount' => $amount
                ]
            );

            // Cập nhật balance user
            if ($type === 'add') {
                $user->addBalance($amount);
            } else {
                $user->subtractBalance($amount);
            }

            return $user->fresh();
        });
    }

    /**
     * Lấy lịch sử giao dịch của user
     */
    public function getUserTransactions($userId, $perPage = 20)
    {
        return BalanceTransaction::forUser($userId)
            ->with(['admin'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Lấy đơn hàng của user
     */
    public function getUserOrders(User $user, $perPage = 10)
    {
        return $user->orders()
            ->with('orderItems.product')
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Lấy thống kê wallet
     */
    public function getWalletStats()
    {
        // Tính tổng tiền nạp hôm nay (= doanh thu)
        $todayTopup = BalanceTransaction::today()
            ->whereIn('type', [BalanceTransaction::TYPE_TOPUP_ATM, BalanceTransaction::TYPE_TOPUP_CARD])
            ->sum('amount');

        // Tính tổng tiền chi tiêu hôm nay (mua hàng)
        $todaySpent = abs(BalanceTransaction::today()
            ->where('type', BalanceTransaction::TYPE_PURCHASE)
            ->sum('amount'));

        // Tính tổng doanh thu tất cả thời gian
        $totalRevenue = BalanceTransaction::whereIn('type', [
                BalanceTransaction::TYPE_TOPUP_ATM,
                BalanceTransaction::TYPE_TOPUP_CARD
            ])->sum('amount');

        return [
            'total_users' => User::count(), // Tính cả admin và user thường
            'total_balance' => User::sum('balance'), // Tổng số dư của tất cả users (bao gồm admin)
            'total_revenue' => $totalRevenue,
            'today_topup' => $todayTopup,
            'today_spent' => $todaySpent,
            'today_revenue' => $todayTopup, // Doanh thu hôm nay = tiền nạp hôm nay
        ];
    }

    /**
     * Đăng ký user mới (cho public registration)
     */
    public function registerUser(array $userData)
    {
        return User::create([
            'username' => $userData['username'],
            'email' => $userData['email'],
            'password' => Hash::make($userData['password']),
            'role' => 'user',
            'balance' => 0,
        ]);
    }

    /**
     * Tìm user theo username hoặc email
     */
    public function findUserByCredentials(string $credentials)
    {
        return User::where('username', $credentials)
            ->orWhere('email', $credentials)
            ->first();
    }

    /**
     * Bulk actions cho users
     */
    public function bulkAction(array $userIds, string $action)
    {
        $users = User::whereIn('id', $userIds);
        
        switch ($action) {
            case 'activate':
                $users->update(['email_verified_at' => now()]);
                return 'Đã kích hoạt ' . count($userIds) . ' user!';
                
            case 'deactivate':
                $users->update(['email_verified_at' => null]);
                return 'Đã vô hiệu hóa ' . count($userIds) . ' user!';
                
            case 'delete':
                // Kiểm tra xem có user nào có đơn hàng không
                $usersWithOrders = User::whereIn('id', $userIds)
                    ->whereHas('orders')
                    ->count();
                    
                if ($usersWithOrders > 0) {
                    throw new \Exception('Không thể xóa user đã có đơn hàng!');
                }
                
                $users->delete();
                return 'Đã xóa ' . count($userIds) . ' user!';
                
            default:
                throw new \Exception('Action không hợp lệ!');
        }
    }


}
