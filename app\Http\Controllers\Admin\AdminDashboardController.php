<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BalanceTransaction;
use App\Models\Category;
use App\Models\Order;
use App\Models\Product;
use App\Models\ServiceOrder;
use App\Models\User;
use App\Services\RevenueService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    protected $revenueService;

    public function __construct(RevenueService $revenueService)
    {
        $this->revenueService = $revenueService;
    }

    public function index()
    {
        try {
        // Thống kê tổng quan
        $stats = $this->getOverallStats();

        // Thống kê doanh thu chi tiết (cho charts)
        $revenueStats = $this->revenueService->getRevenueOverview();
        $revenueTimeStats = $this->revenueService->getTimeBasedStats(); // Đổi tên
        $topupMethodStats = $this->revenueService->getTopupMethodStats();

        // Thống kê hoạt động hôm nay (cho "Hoạt động hôm nay" section)
        $timeStats = $this->getTimeBasedStats(); // Sử dụng method riêng

        // Hoạt động gần đây
        $recentActivities = $this->getRecentActivities();

        // Top sản phẩm bán chạy
        $topProducts = $this->getTopProducts();

        // Top users
        $topUsers = $this->getTopUsers();

        return view('admin.dashboard', compact(
            'stats',
            'revenueStats',
            'revenueTimeStats', // Đổi tên
            'timeStats',        // Data cho "Hoạt động hôm nay"
            'topupMethodStats',
            'recentActivities',
            'topProducts',
            'topUsers'
        ));
        } catch (\Exception $e) {
            \Log::error('Dashboard error: ' . $e->getMessage());

            // Return với default data nếu có lỗi
            $stats = [
                'total_users' => 0,
                'total_products' => 0,
                'total_categories' => 0,
                'total_orders' => 0,
                'total_balance' => 0,
                'products_in_stock' => 0,
                'products_out_of_stock' => 0,
            ];

            $revenueStats = [
                'topup_revenue' => 0,
                'product_revenue' => 0,
                'service_revenue' => 0,
                'business_revenue' => 0,
                'total_revenue' => 0,
                'breakdown' => [
                    'topup_percentage' => 0,
                    'product_percentage' => 0,
                    'service_percentage' => 0,
                ]
            ];

            $timeStats = [
                'today' => ['topup_revenue' => 0, 'product_revenue' => 0, 'service_revenue' => 0, 'total_revenue' => 0],
                'yesterday' => ['topup_revenue' => 0, 'product_revenue' => 0, 'service_revenue' => 0, 'total_revenue' => 0],
                'this_week' => ['topup_revenue' => 0, 'product_revenue' => 0, 'service_revenue' => 0, 'total_revenue' => 0],
                'this_month' => ['topup_revenue' => 0, 'product_revenue' => 0, 'service_revenue' => 0, 'total_revenue' => 0],
            ];

            $topupMethodStats = [
                'atm_revenue' => 0,
                'card_revenue' => 0,
                'total_topup' => 0,
                'atm_percentage' => 0,
                'card_percentage' => 0,
            ];


            $recentActivities = ['transactions' => collect(), 'orders' => collect(), 'new_users' => collect()];
            $topProducts = collect();
            $topUsers = collect();

            return view('admin.dashboard', compact(
                'stats',
                'revenueStats',
                'timeStats',
                'topupMethodStats',
                'recentActivities',
                'topProducts',
                'topUsers'
            ))->with('error', 'Có lỗi khi tải dashboard: ' . $e->getMessage());
        }
    }

    private function getOverallStats()
    {
        return [
            'total_users' => User::count(),
            'total_products' => Product::count(),
            'total_categories' => Category::where('status', 'active')->count(),
            'total_orders' => Order::count() + ServiceOrder::count(),
            'total_balance' => User::sum('balance'),
            'products_in_stock' => Product::whereHas('productAccounts', function($q) {
                $q->where('status', 'available');
            })->count(),
            'products_out_of_stock' => Product::whereDoesntHave('productAccounts', function($q) {
                $q->where('status', 'available');
            })->count(),
        ];
    }

    private function getTimeBasedStats()
{
    $today = Carbon::today();
    $yesterday = Carbon::yesterday();
    $thisWeek = Carbon::now()->startOfWeek();
    $thisMonth = Carbon::now()->startOfMonth();

    return [
        'today' => [
            'topup_revenue' => BalanceTransaction::whereDate('created_at', $today)
                ->whereIn('type', [BalanceTransaction::TYPE_TOPUP_ATM, BalanceTransaction::TYPE_TOPUP_CARD])
                ->sum('amount'),
            'orders' => Order::whereDate('created_at', $today)->count(),
            'new_users' => User::whereDate('created_at', $today)->count(),
            'transactions' => BalanceTransaction::whereDate('created_at', $today)->count(),
        ],
        'yesterday' => [
            'topup_revenue' => BalanceTransaction::whereDate('created_at', $yesterday)
                ->whereIn('type', [BalanceTransaction::TYPE_TOPUP_ATM, BalanceTransaction::TYPE_TOPUP_CARD])
                ->sum('amount'),
            'orders' => Order::whereDate('created_at', $yesterday)->count(),
        ],
        // ... other periods
    ];
}

    private function getChartData()
    {
        // Doanh thu 7 ngày gần đây
        $revenueChart = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $revenue = BalanceTransaction::whereDate('created_at', $date)
                ->whereIn('type', [BalanceTransaction::TYPE_TOPUP_ATM, BalanceTransaction::TYPE_TOPUP_CARD])
                ->sum('amount');
            
            $revenueChart[] = [
                'date' => $date->format('d/m'),
                'revenue' => $revenue
            ];
        }

        // Đơn hàng 7 ngày gần đây
        $ordersChart = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $orders = Order::whereDate('created_at', $date)->count();
            
            $ordersChart[] = [
                'date' => $date->format('d/m'),
                'orders' => $orders
            ];
        }

        return [
            'revenue' => $revenueChart,
            'orders' => $ordersChart
        ];
    }

    private function getRecentActivities()
    {
        // Lấy 10 hoạt động gần đây nhất
        $transactions = BalanceTransaction::with(['user'])
            ->latest()
            ->limit(5)
            ->get();

        $orders = Order::with(['user', 'orderItems.product'])
            ->latest()
            ->limit(5)
            ->get();

        $newUsers = User::where('role', 'user')
            ->latest()
            ->limit(5)
            ->get();

        return [
            'transactions' => $transactions,
            'orders' => $orders,
            'new_users' => $newUsers
        ];
    }

    private function getTopProducts()
    {
        // Lấy top products dựa trên OrderItem
        return Product::with('category')
            ->withCount(['orderItems' => function($query) {
                $query->whereHas('order', function($q) {
                    $q->where('created_at', '>=', Carbon::now()->subDays(30));
                });
            }])
            ->having('order_items_count', '>', 0)
            ->orderBy('order_items_count', 'desc')
            ->limit(5)
            ->get();
    }

    private function getTopUsers()
    {
        return User::where('role', 'user')
            ->withCount(['orders' => function($query) {
                $query->where('created_at', '>=', Carbon::now()->subDays(30));
            }])
            ->withSum(['orders as total_spent' => function($query) {
                $query->where('created_at', '>=', Carbon::now()->subDays(30));
            }], 'total_amount')
            ->having('orders_count', '>', 0)
            ->orderBy('total_spent', 'desc')
            ->limit(5)
            ->get();
    }
}
