<?php $__env->startSection('title', 'Đơn dịch vụ của tôi - AccReroll'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
    <!-- Header -->
    <div class="mb-4 sm:mb-6">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Đơn dịch vụ của tôi</h1>
        <p class="text-gray-600 mt-1 lg:mt-2 text-sm lg:text-base">Quản lý và theo dõi các đơn dịch vụ đã đặt</p>
    </div>

    <?php if($orders->count() > 0): ?>
        <!-- Orders List -->
        <div class="space-y-3 sm:space-y-4">
            <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('service-orders.show', $order)); ?>" class="block bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-100 hover:shadow-md hover:border-blue-200 transition-all duration-200 cursor-pointer">
                <!-- Service Order Card Content -->
                <div class="p-2 sm:p-3 lg:p-4">
                    <!-- Header Row - Mobile Optimized -->
                    <div class="space-y-2 mb-3">
                        <!-- Order Number and Price Row -->
                        <div class="flex items-center justify-between">
                            <h3 class="text-sm sm:text-base font-semibold text-blue-700 truncate mr-2">
                                <?php echo e($order->order_number ?? 'SRV' . str_pad($order->id, 4, '0', STR_PAD_LEFT)); ?>

                            </h3>
                            <div class="text-base sm:text-lg lg:text-xl font-bold text-red-600 whitespace-nowrap">
                                <?php echo e($order->formatted_price); ?>

                            </div>
                        </div>

                        <!-- Status and Date Row -->
                        <div class="flex items-center justify-between flex-wrap gap-2">
                            <div class="flex items-center space-x-2 flex-wrap">
                               <p class="text-xs sm:text-sm text-gray-500 whitespace-nowrap"><?php echo e($order->created_at->format('d/m/Y H:i')); ?></p> 
                            </div>
                             <div class="flex items-center space-x-2 flex-wrap">
                            <span class="inline-flex items-center px-2 sm:px-2.5 py-1 rounded-full text-xs font-medium whitespace-nowrap
                                    <?php if($order->status === 'pending'): ?> bg-amber-100 text-amber-800
                                    <?php elseif($order->status === 'processing'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($order->status === 'completed'): ?> bg-emerald-100 text-emerald-800
                                    <?php elseif($order->status === 'cancelled'): ?> bg-red-100 text-red-800
                                    <?php endif; ?>">
                                    <?php if($order->status === 'pending'): ?>
                                        <i class="fas fa-clock mr-1"></i>
                                    <?php elseif($order->status === 'processing'): ?>
                                        <i class="fas fa-cog mr-1"></i>
                                    <?php elseif($order->status === 'completed'): ?>
                                        <i class="fas fa-check-circle mr-1"></i>
                                    <?php elseif($order->status === 'cancelled'): ?>
                                        <i class="fas fa-times-circle mr-1"></i>
                                    <?php endif; ?>
                                    <?php echo e($order->status_text); ?>

                                </span>
                                <?php if($order->usedVoucher): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 whitespace-nowrap">
                                        <i class="fas fa-ticket-alt mr-1"></i>Voucher
                                    </span>
                                <?php endif; ?>
                             </div>
                        </div>
                    </div>

                    <!-- Service Content -->
                    <div class="flex items-start space-x-2 sm:space-x-3 p-2 bg-gray-50 rounded-lg">
                        <!-- Service Image -->
                        <div class="flex-shrink-0">
                            <div class="w-10 h-8 sm:w-12 sm:h-10 lg:w-16 lg:h-12 bg-white rounded-lg flex items-center justify-center overflow-hidden border border-gray-300 shadow-sm">
                                <?php if($order->service && $order->service->first_image): ?>
                                    <img src="<?php echo e($order->service->first_image); ?>" alt="<?php echo e($order->service->name ?? 'Dịch vụ'); ?>"
                                         class="max-w-full max-h-full object-contain">
                                <?php else: ?>
                                    <i class="fas fa-cogs text-gray-400 text-sm sm:text-lg"></i>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Service Info -->
                        <div class="flex-1 min-w-0">
                             <!-- Service Name Row -->
                        <div class="text-xs sm:text-sm text-gray-600 truncate mb-1">
                           <strong><?php echo e($order->service->name ?? 'N/A'); ?></strong>
                        </div>
                            <?php if($order->notes): ?>
                                <p class="text-xs text-blue-600 font-medium">
                                    <i class="fas fa-sticky-note mr-1"></i>Có ghi chú thêm
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>

                </div>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($orders->hasPages()): ?>
            <div class="mt-4 sm:mt-6">
                <?php echo e($orders->links('pagination.custom')); ?>

            </div>
        <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-8 sm:py-12">
                <i class="fas fa-inbox text-gray-400 text-3xl sm:text-4xl mb-4"></i>
                <h3 class="text-base sm:text-lg font-medium text-gray-900 mb-2">Chưa có đơn dịch vụ nào</h3>
                <p class="text-sm sm:text-base text-gray-500 mb-4">Bạn chưa đặt đơn dịch vụ nào.</p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/service-orders/index.blade.php ENDPATH**/ ?>