<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TopupTransaction extends Model
{
    use HasFactory;

    protected $table = 'topup_transactions';

    protected $fillable = [
        'method',
        'user_id',
        'amount',
        'status',
        'transaction_code',
        'note',
        // SePay fields
        'sepay_id',
        'gateway',
        'transactionDate',
        'accountNumber',
        'subAccount',
        'code',
        'content',
        'transferType',
        'description',
        'referenceCode',
        // Card fields
        'card_type',
        'card_serial',
        'card_code',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'user_id' => 'integer',
    ];

    // Constants
    public const METHOD_ATM = 'atm';
    public const METHOD_CARD = 'card';
    
    public const STATUS_PENDING = 'pending';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_FAILED = 'failed';

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeAtm($query)
    {
        return $query->where('method', self::METHOD_ATM);
    }

    public function scopeCard($query)
    {
        return $query->where('method', self::METHOD_CARD);
    }

    // Helper methods
    public function getActualAmountAttribute()
    {
        // Chỉ áp dụng cho thẻ cào đã hoàn thành
        if ($this->method === self::METHOD_CARD && $this->status === self::STATUS_COMPLETED) {
            // Lấy số tiền thực nhận từ balance transaction
            $balanceTransaction = \App\Models\BalanceTransaction::where('reference_type', self::class)
                ->where('reference_id', $this->id)
                ->where('type', \App\Models\BalanceTransaction::TYPE_TOPUP_CARD)
                ->first();

            return $balanceTransaction ? $balanceTransaction->amount : $this->amount;
        }

        return $this->amount;
    }

    public function getDisplayTitleAttribute()
    {
        if ($this->method === self::METHOD_CARD) {
            return "Nạp thẻ cào ({$this->card_type}) - Mệnh giá: " . number_format((float)$this->amount) . "đ";
        }

        return $this->method === self::METHOD_ATM ? 'Nạp tiền qua ATM' : 'Nạp tiền';
    }

    // Helper methods
    public function isAtm()
    {
        return $this->method === self::METHOD_ATM;
    }

    public function isCard()
    {
        return $this->method === self::METHOD_CARD;
    }

    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isFailed()
    {
        return $this->status === self::STATUS_FAILED;
    }
}
