<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ServiceOrder;
use App\Models\User;
use App\Models\Service;

class ServiceOrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testUser = User::where('username', 'testuser')->first();
        $demoUser = User::where('username', 'demo')->first();
        $services = Service::all();

        $serviceOrders = [
            [
                'user_id' => $testUser->id,
                'service_id' => $services->first()->id,
                'order_number' => 'SRV' . date('Ymd') . '0001',
                'status' => 'completed',
                'price' => $services->first()->price,
                'content' => 'Tài khoản: <EMAIL> | Mật khẩu: 123456',
                'notes' => 'Cần push rank từ Epic lên Mythic',
                'admin_notes' => 'Đã hoàn thành push rank thành công',
                'completed_at' => now()->subDays(1),
            ],
            [
                'user_id' => $demoUser->id,
                'service_id' => $services->skip(1)->first()->id,
                'order_number' => 'SRV' . date('Ymd') . '0002',
                'status' => 'pending',
                'price' => $services->skip(1)->first()->price,
                'content' => 'ID Game: 123456789 | Server: Việt Nam',
                'notes' => 'Nạp 1000 kim cương',
                'admin_notes' => null,
                'completed_at' => null,
            ],
        ];

        foreach ($serviceOrders as $serviceOrder) {
            ServiceOrder::create($serviceOrder);
        }
    }
}
