<?php $__env->startSection('title', 'Nạp thẻ cào - AccReroll'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="<?php echo e(route('topup.index')); ?>" class="mr-4 text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Nạp thẻ cào</h1>
                <p class="text-gray-600 mt-2">Nạp tiền bằng thẻ cào điện thoại</p>
            </div>
        </div>
    </div>

    <!-- Main Content - Single Column -->
    <div class="max-w-md mx-auto">
        <!-- Card Form -->
        <div>
            <div class="bg-white rounded-lg shadow-md p-6">
            <form method="POST" action="<?php echo e(route('topup.card.process')); ?>" id="cardForm">
                <?php echo csrf_field(); ?>

                <!-- Telco Selection -->
                <div class="mb-6">
                    <label for="telco" class="block text-sm font-medium text-gray-700 mb-2">
                        Loại thẻ<span class="text-red-500">*</span>
                    </label>
                    <select name="telco" id="telco"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['telco'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            onchange="updateAmountOptions()">
                        <option value="">Chọn loại thẻ</option>
                        <?php $__currentLoopData = $telcos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $telco): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($code); ?>" <?php echo e(old('telco') == $code ? 'selected' : ''); ?>>
                                <?php echo e($telco['name']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['telco'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Amount Selection -->
                <div class="mb-6">
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Mệnh giá - chiết khấu<span class="text-red-500">*</span>
                    </label>
                    <select name="amount" id="amount"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            disabled>
                        <option value="">Chọn một tùy chọn</option>
                    </select>
                    <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Serial -->
                <div class="mb-6">
                    <label for="serial" class="block text-sm font-medium text-gray-700 mb-2">
                        Số serial của thẻ<span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="serial" name="serial"
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['serial'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Nhập số serial của thẻ"
                           value="<?php echo e(old('serial')); ?>">
                    <p id="serial-hint" class="mt-1 text-sm text-gray-500"></p>
                    <?php $__errorArgs = ['serial'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Code -->
                <div class="mb-6">
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                        Mã thẻ cào<span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="code" name="code"
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="Nhập mã thẻ cào"
                           value="<?php echo e(old('code')); ?>">
                    <p id="code-hint" class="mt-1 text-sm text-gray-500"></p>
                    <?php $__errorArgs = ['code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <?php if(session('success')): ?>
                    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                         <div class="flex">
                            <i class="fas fa-check-circle text-green-400 mr-2 mt-0.5"></i>
                            <div class="text-sm text-green-700"><?php echo e(session('success')); ?></div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Error Messages -->
                <?php if($errors->has('card')): ?>
                    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-red-400 mr-2 mt-0.5"></i>
                            <div class="text-sm text-red-700"><?php echo e($errors->first('card')); ?></div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Submit Button -->
                <div class="text-right">
                    <div id="formStatus" class="text-sm text-gray-500 mb-2 hidden">
                        Vui lòng nhập đầy đủ thông tin để tiếp tục
                    </div>
                    <button type="submit" id="submitBtn"
                            class="bg-gray-400 text-white font-medium py-3 px-8 rounded-lg transition-colors cursor-not-allowed"
                            disabled>
                        Nạp thẻ
                    </button>
                </div>

                <!-- Important Notes -->
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="flex items-start text-sm">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-0.5"></i>
                        <span class="text-gray-700"><strong>Lưu ý:</strong> Thẻ phải còn hạn sử dụng và chưa được sử dụng. Sai mệnh giá có thể bị trừ 50% số tiền. Có lỗi hãy liên hệ FB để được hỗ trợ nhé.</span>
                    </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</div>

<script>
// Dữ liệu mệnh giá cho từng nhà mạng
const telcoAmounts = <?php echo json_encode($telcos, 15, 512) ?>;

function updateAmountOptions() {
    const telcoSelect = document.getElementById('telco');
    const amountSelect = document.getElementById('amount');
    const serialHint = document.getElementById('serial-hint');
    const codeHint = document.getElementById('code-hint');
    const selectedTelco = telcoSelect.value;

    // Reset amount select
    amountSelect.innerHTML = '<option value="">Chọn một tùy chọn</option>';

    // Update hints based on card type
    if (selectedTelco === 'ZING') {
        serialHint.textContent = 'Thẻ Zing: 8-15 ký tự, chữ hoa và số';
        codeHint.textContent = 'Thẻ Zing: 8-15 ký tự, chữ hoa và số';
    } else if (selectedTelco) {
        serialHint.textContent = 'Thẻ điện thoại: 10-20 ký tự, chỉ số';
        codeHint.textContent = 'Thẻ điện thoại: 10-20 ký tự, chỉ số';
    } else {
        serialHint.textContent = '';
        codeHint.textContent = '';
    }

    // Check form validation after telco change
    validateForm();

    if (selectedTelco && telcoAmounts[selectedTelco]) {
        // Enable amount select
        amountSelect.disabled = false;

        // Add amount options với tỉ lệ 80%
        const amounts = telcoAmounts[selectedTelco].amounts;
        amounts.forEach(amount => {
            const discountedAmount = Math.floor(amount * 0.8);
            const option = document.createElement('option');
            option.value = amount;
            option.textContent = `${new Intl.NumberFormat('de-DE').format(amount)}đ - Nhận ${new Intl.NumberFormat('de-DE').format(discountedAmount)}đ`;

            // Check if this was the old selected value
            if ('<?php echo e(old("amount")); ?>' == amount) {
                option.selected = true;
            }

            amountSelect.appendChild(option);
        });
    } else {
        // Disable amount select
        amountSelect.disabled = true;
    }
}

// Validate form function
function validateForm() {
    const telco = document.getElementById('telco').value;
    const amount = document.getElementById('amount').value;
    const serial = document.getElementById('serial').value.trim();
    const code = document.getElementById('code').value.trim();
    const submitBtn = document.getElementById('submitBtn');

    let isValid = true;

    // Check if all fields are filled
    if (!telco || !amount || !serial || !code) {
        isValid = false;
    }

    // Validate based on card type
    if (telco && serial && code) {
        if (telco === 'ZING') {
            // Zing card validation
            if (serial.length < 8 || serial.length > 15 || !/^[A-Z0-9]+$/.test(serial)) {
                isValid = false;
            }
            if (code.length < 8 || code.length > 15 || !/^[A-Z0-9]+$/.test(code)) {
                isValid = false;
            }
        } else {
            // Phone card validation
            if (serial.length < 10 || serial.length > 20 || !/^[0-9]+$/.test(serial)) {
                isValid = false;
            }
            if (code.length < 10 || code.length > 20 || !/^[0-9]+$/.test(code)) {
                isValid = false;
            }
        }
    }

    // Update submit button and status
    const formStatus = document.getElementById('formStatus');

    if (isValid) {
        submitBtn.disabled = false;
        submitBtn.className = 'bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-8 rounded-lg transition-colors cursor-pointer';
        formStatus.classList.add('hidden');
    } else {
        submitBtn.disabled = true;
        submitBtn.className = 'bg-gray-400 text-white font-medium py-3 px-8 rounded-lg transition-colors cursor-not-allowed';

        // Show status message if any field is filled (user is trying to fill form)
        if (telco || amount || serial || code) {
            formStatus.classList.remove('hidden');
        } else {
            formStatus.classList.add('hidden');
        }
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateAmountOptions();

    // Add event listeners for validation
    document.getElementById('telco').addEventListener('change', validateForm);
    document.getElementById('amount').addEventListener('change', validateForm);
    document.getElementById('serial').addEventListener('input', validateForm);
    document.getElementById('code').addEventListener('input', validateForm);

    // Initial validation
    validateForm();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/topup/card.blade.php ENDPATH**/ ?>