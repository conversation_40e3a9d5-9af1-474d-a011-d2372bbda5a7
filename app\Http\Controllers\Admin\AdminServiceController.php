<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Category;
use Illuminate\Support\Facades\Storage;

class AdminServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Service::with('category');

        // Search
        if ($request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Filter by category
        if ($request->category_id) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        $services = $query->orderBy('created_at', 'desc')->paginate(15);
        $categories = Category::where('status', 'active')
            ->withCount(['services' => function ($query) {
                $query->where('status', 'active');
            }])
            ->orderBy('name')
            ->get();

        return view('admin.services.index', compact('services', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('status', 'active')
            ->orderBy('name')
            ->get();
        return view('admin.services.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'order_instructions' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'status' => 'required|in:active,inactive',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $service = new Service();
        $service->name = $request->name;
        $service->description = $request->description;
        $service->order_instructions = $request->order_instructions;
        $service->price = $request->price;
        $service->category_id = $request->category_id;
        $service->status = $request->status;

        // Handle image uploads
        $images = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('services', $imageName, 'public');
                $images[] = $imagePath;
            }
        }
        $service->images = $images;

        $service->save();

        return redirect()->route('admin.services.index')->with('success', 'Dịch vụ đã được tạo thành công!');
    }



    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Service $service)
    {
        $categories = Category::where('status', 'active')
            ->orderBy('name')
            ->get();
        return view('admin.services.edit', compact('service', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Service $service)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'order_instructions' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'status' => 'required|in:active,inactive',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $service->name = $request->name;
        $service->description = $request->description;
        $service->order_instructions = $request->order_instructions;
        $service->price = $request->price;
        $service->category_id = $request->category_id;
        $service->status = $request->status;

        // Handle image uploads
        if ($request->hasFile('images')) {
            // Delete old images
            if ($service->images) {
                foreach ($service->images_array as $oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }

            // Upload new images
            $images = [];
            foreach ($request->file('images') as $image) {
                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('services', $imageName, 'public');
                $images[] = $imagePath;
            }
            $service->images = $images;
        }

        $service->save();

        return redirect()->route('admin.services.index')->with('success', 'Dịch vụ đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Service $service)
    {
        // Delete images
        if ($service->images) {
            foreach ($service->images_array as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $service->delete();

        return redirect()->route('admin.services.index')->with('success', 'Dịch vụ đã được xóa thành công!');
    }
}
