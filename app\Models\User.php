<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Notifications\ResetPasswordNotification;
use App\Notifications\BrevoResetPasswordNotification;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'display_name',
        'email',
        'password',
        'role',
        'balance',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'balance' => 'decimal:2',
            'is_active' => 'boolean',
        ];
    }

    // Accessors
    public function getDisplayNameAttribute($value)
    {
        return $value ?: $this->username;
    }

    public function getNameForDisplayAttribute()
    {
        return $this->display_name ?: $this->username;
    }

    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'Hoạt động' : 'Bị khóa';
    }

    public function getStatusColorAttribute()
    {
        return $this->is_active ? 'green' : 'red';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    // Methods
    public function activate()
    {
        $this->update(['is_active' => true]);
    }

    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    public function toggleStatus()
    {
        $this->update(['is_active' => !$this->is_active]);
    }

    // Relationships
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function serviceOrders()
    {
        return $this->hasMany(ServiceOrder::class);
    }

    public function topupTransactions()
    {
        return $this->hasMany(TopupTransaction::class);
    }

    public function userVouchers()
    {
        return $this->hasMany(UserVoucher::class);
    }

    public function availableVouchers()
    {
        return $this->userVouchers()
            ->with(['voucher.category']) // Eager load voucher và category để tránh N+1
            ->whereNull('used_at')
            ->whereHas('voucher', function ($query) {
                $query->where('is_active', true)
                      ->whereNull('deleted_at') // Exclude archived vouchers
                      ->where(function ($q) {
                          $q->whereNull('expires_at')
                            ->orWhere('expires_at', '>', now());
                      });
            });
    }



    // Helper methods để tạo nội dung nạp tiền (lowercase)
    public function getTopupContentAttribute()
    {
        $pattern = config('sepay.pattern', 'guidang');
        return strtolower("{$pattern}{$this->id}");
    }

    // Helper methods cho balance
    public function addBalance($amount)
    {
        $this->increment('balance', $amount);
        return $this;
    }

    public function subtractBalance($amount)
    {
        if ($this->balance >= $amount) {
            $this->decrement('balance', $amount);
            return true;
        }
        return false;
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token)
    {
        // Sử dụng Brevo API thay vì SMTP
        if (config('services.brevo.api_key')) {
            $this->notify(new BrevoResetPasswordNotification($token));
        } else {
            // Fallback về SMTP nếu không có Brevo API key
            $this->notify(new ResetPasswordNotification($token));
        }
    }
}
