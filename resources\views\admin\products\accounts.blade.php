@extends('layouts.admin')

@section('title', 'Tài khoản - ' . ($product->name ?? 'N/A'))

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
        <div>
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('admin.products.index') }}" class="text-gray-600 hover:text-blue-600">
                            <i class="fas fa-box mr-2"></i>Sản phẩm
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                            <span class="text-gray-900 font-medium">T<PERSON><PERSON> <PERSON><PERSON><PERSON></span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">Quản lý Tài khoản</h1>
            <p class="text-gray-600">{{ $product->name ?? 'N/A' }}</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-2 flex-shrink-0">
            <button onclick="showAddAccountModal()" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Thêm Tài khoản
            </button>
            <button onclick="showBulkImportModal()" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-upload mr-2"></i>Import Hàng loạt
            </button>
        </div>
    </div>

    <!-- Product Info -->
    <div class="bg-white rounded-lg shadow mb-6 p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0 h-16 w-16">
                @if($product->main_image && $product->main_image !== 'default-product.jpg')
                    <img class="h-16 w-16 rounded-lg object-cover" src="{{ storage_url($product->main_image) }}" alt="{{ $product->name ?? 'N/A' }}">
                @else
                    <div class="h-16 w-16 rounded-lg bg-gray-300 flex items-center justify-center">
                        <i class="fas fa-image text-gray-600 text-xl"></i>
                    </div>
                @endif
            </div>
            <div class="ml-6 flex-1">
                <h3 class="text-lg font-medium text-gray-900">{{ $product->name ?? 'N/A' }}</h3>
                <p class="text-gray-600">{{ $product->category->name ?? 'Chưa phân loại' }}</p>
                <div class="mt-2 flex items-center space-x-4">
                    <span class="text-sm text-gray-500">Giá: {{ format_money($product->price ?? 0) }}</span>
                    <span class="text-sm text-gray-500">Trạng thái:
                        @if(($product->status ?? 'active') == 'active')
                            <span class="text-green-600">Đang bán</span>
                        @else
                            <span class="text-red-600">Đã ẩn</span>
                        @endif
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fas fa-users text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Tổng tài khoản</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $totalAccounts ?? 0 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fas fa-check-circle text-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Còn lại</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $availableAccounts ?? 0 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <i class="fas fa-shopping-cart text-red-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Đã bán</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $soldAccounts ?? 0 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fas fa-percentage text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Tỷ lệ bán</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $soldPercentage ?? 0 }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow mb-6 p-4">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div class="flex-1 min-w-48">
                <label class="block text-sm font-medium text-gray-700 mb-1">Tìm kiếm</label>
                <input type="text" name="search" value="{{ request('search') }}"
                       placeholder="Tìm theo username..."
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Trạng thái</label>
                <select name="status" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Tất cả</option>
                    <option value="available" {{ request('status') == 'available' ? 'selected' : '' }}>Còn lại</option>
                    <option value="sold" {{ request('status') == 'sold' ? 'selected' : '' }}>Đã bán</option>
                </select>
            </div>

            <div class="flex gap-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-search mr-1"></i>Tìm
                </button>
                <a href="{{ route('admin.products.accounts', $product) }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-times mr-1"></i>Xóa
                </a>
            </div>
        </form>
    </div>

    <!-- Accounts Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Username
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Password
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Trạng thái
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Ngày bán
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Ngày tạo
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Thao tác
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($accounts ?? [] as $account)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_accounts[]" value="{{ $account->id ?? '' }}" class="rounded border-gray-300 account-checkbox">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $account->username ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-900 password-hidden" id="password-{{ $account->id ?? 0 }}">••••••••</span>
                                    <button onclick="togglePassword({{ $account->id ?? 0 }}, '{{ $account->password ?? '' }}')"
                                            class="ml-2 text-gray-400 hover:text-gray-600 transition-colors" title="Hiện/ẩn mật khẩu">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                    <button onclick="copyPassword('{{ $account->password ?? '' }}')"
                                            class="ml-2 text-gray-400 hover:text-gray-600 transition-colors" title="Copy mật khẩu">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if(($account->status ?? 'available') == 'available')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Còn lại
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-shopping-cart mr-1"></i>Đã bán
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ isset($account->sold_at) ? $account->sold_at->format('d/m/Y H:i') : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ isset($account->created_at) ? $account->created_at->format('d/m/Y H:i') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button onclick="editAccount({{ $account->id ?? 0 }}, '{{ $account->username ?? '' }}', '{{ $account->password ?? '' }}')"
                                            class="text-green-600 hover:text-green-900 transition-colors" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="confirmDelete({{ $account->id ?? 0 }})"
                                            class="text-red-600 hover:text-red-900 transition-colors" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-users text-4xl mb-4 block"></i>
                                    <p class="text-lg">Chưa có tài khoản nào</p>
                                    <p class="text-sm">Hãy thêm tài khoản đầu tiên cho sản phẩm này</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if(isset($accounts) && $accounts->hasPages())
            <div class="px-6 py-3 border-t border-gray-200">
                {{ $accounts->withQueryString()->links('pagination.custom') }}
            </div>
        @endif
    </div>

    <!-- Bulk Actions -->
    <div id="bulk-actions" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border p-4 hidden">
        <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">
                <span id="selected-count">0</span> tài khoản được chọn
            </span>
            <button onclick="bulkAction('delete')" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                <i class="fas fa-trash mr-1"></i>Xóa
            </button>
        </div>
    </div>
</div>

<!-- Add Account Modal -->
<div id="addAccountModal" class="fixed inset-0 hidden z-50" onclick="closeAddAccountModal()" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6 shadow-2xl" onclick="event.stopPropagation()">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user-plus text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Thêm Tài Khoản Mới</h3>
                        <p class="text-sm text-gray-500">{{ $product->name }}</p>
                    </div>
                </div>
                <button onclick="closeAddAccountModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Form -->
            <form id="addAccountForm" method="POST" action="{{ route('admin.products.accounts.store', $product) }}">
                @csrf
                <div class="space-y-5">
                    <div>
                        <label for="add-username" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-gray-400"></i>Username
                        </label>
                        <input type="text" id="add-username" name="username" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                               placeholder="Nhập username">
                    </div>

                    <div>
                        <label for="add-password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-gray-400"></i>Password
                        </label>
                        <input type="text" id="add-password" name="password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                               placeholder="Nhập password">
                    </div>
                </div>

                <!-- Modal Actions -->
                <div class="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeAddAccountModal()"
                            class="px-5 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors">
                        Hủy
                    </button>
                    <button type="submit"
                            class="px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm">
                        <i class="fas fa-plus mr-2"></i>Thêm Tài Khoản
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Account Modal -->
<div id="editAccountModal" class="fixed inset-0 hidden z-50" onclick="closeEditAccountModal()" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6 shadow-2xl" onclick="event.stopPropagation()">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user-edit text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Sửa Tài Khoản</h3>
                        <p class="text-sm text-gray-500">{{ $product->name }}</p>
                    </div>
                </div>
                <button onclick="closeEditAccountModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Form -->
            <form id="editAccountForm" method="POST">
                @csrf
                @method('PUT')
                <div class="space-y-5">
                    <div>
                        <label for="edit-username" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-gray-400"></i>Username
                        </label>
                        <input type="text" name="username" id="edit-username" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                    </div>

                    <div>
                        <label for="edit-password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-gray-400"></i>Password
                        </label>
                        <input type="text" name="password" id="edit-password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                    </div>
                </div>

                <!-- Modal Actions -->
                <div class="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeEditAccountModal()"
                            class="px-5 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors">
                        Hủy
                    </button>
                    <button type="submit"
                            class="px-5 py-2.5 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors shadow-sm">
                        <i class="fas fa-save mr-2"></i>Cập Nhật
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div id="bulkImportModal" class="fixed inset-0 hidden z-50" onclick="closeBulkImportModal()" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full p-6 shadow-2xl" onclick="event.stopPropagation()">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Import Tài khoản Hàng loạt</h3>
                <button onclick="closeBulkImportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Hướng dẫn:</h4>
                <p class="text-sm text-blue-800 mb-2">Nhập danh sách tài khoản, mỗi dòng một tài khoản theo định dạng:</p>
                <code class="bg-blue-100 px-2 py-1 rounded text-sm">username|password</code>
                <p class="text-sm text-blue-800 mt-2">Ví dụ:</p>
                <pre class="bg-blue-100 p-2 rounded text-sm mt-1">user1|pass123
user2|abc456
user3|xyz789</pre>
            </div>

            <form method="POST" action="{{ route('admin.products.accounts.bulk-import', $product) }}">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Danh sách tài khoản</label>
                        <textarea name="accounts_data" rows="10" required
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="user1|pass123&#10;user2|abc456&#10;user3|xyz789"></textarea>
                    </div>
                </div>

                <div class="flex justify-end gap-2 mt-6">
                    <button type="button" onclick="closeBulkImportModal()"
                            class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors">
                        Hủy
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-upload mr-2"></i>Import
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Select all checkbox
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.account-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActions();
});

// Individual checkbox change
document.querySelectorAll('.account-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActions);
});

function updateBulkActions() {
    const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    if (selectedCheckboxes.length > 0) {
        bulkActions.classList.remove('hidden');
        selectedCount.textContent = selectedCheckboxes.length;
    } else {
        bulkActions.classList.add('hidden');
    }
}

// Modal functions
function showAddAccountModal() {
    // Show modal
    document.getElementById('addAccountModal').classList.remove('hidden');

    // Focus first input
    document.getElementById('add-username').focus();
}

function closeAddAccountModal() {
    // Hide modal
    document.getElementById('addAccountModal').classList.add('hidden');

    // Reset form
    document.getElementById('addAccountForm').reset();
}

function showEditAccountModal() {
    // Show modal
    document.getElementById('editAccountModal').classList.remove('hidden');

    // Focus first input
    document.getElementById('edit-username').focus();
}

function closeEditAccountModal() {
    // Hide modal
    document.getElementById('editAccountModal').classList.add('hidden');

    // Reset form
    document.getElementById('editAccountForm').reset();
}

// Close modal on ESC key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const addModal = document.getElementById('addAccountModal');
        const editModal = document.getElementById('editAccountModal');
        const bulkModal = document.getElementById('bulkImportModal');

        if (!addModal.classList.contains('hidden')) {
            closeAddAccountModal();
        }
        if (!editModal.classList.contains('hidden')) {
            closeEditAccountModal();
        }
        if (!bulkModal.classList.contains('hidden')) {
            closeBulkImportModal();
        }
    }
});

function showBulkImportModal() {
    document.getElementById('bulkImportModal').classList.remove('hidden');
}

function closeBulkImportModal() {
    document.getElementById('bulkImportModal').classList.add('hidden');
}

// function editAccount(id, username, password) {
//     document.getElementById('edit-username').value = username;
//     document.getElementById('edit-password').value = password;
//     document.getElementById('editAccountForm').action = `/admin/products/{{ $product->id }}/accounts/${id}`;
//     showEditAccountModal();
// }
function editAccount(id, username, password) {
    document.getElementById('edit-username').value = username;
    document.getElementById('edit-password').value = password;
    document.getElementById('editAccountForm').action = `{{ url(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin/products') }}/{{ $product->id }}/accounts/${id}`;
    showEditAccountModal();
}

function togglePassword(accountId, password) {
    const element = document.getElementById(`password-${accountId}`);
    if (element.textContent === '••••••••') {
        element.textContent = password;
    } else {
        element.textContent = '••••••••';
    }
}

function copyPassword(password) {
    navigator.clipboard.writeText(password).then(function() {
        // Show toast notification
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
        toast.textContent = 'Đã copy mật khẩu!';
        document.body.appendChild(toast);

        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}

// function confirmDelete(accountId) {
//     if (confirm('Bạn có chắc chắn muốn xóa tài khoản này?')) {
//         const form = document.createElement('form');
//         form.method = 'POST';
//         form.action = `/admin/products/{{ $product->id }}/accounts/${accountId}`;

//         const csrfToken = document.createElement('input');
//         csrfToken.type = 'hidden';
//         csrfToken.name = '_token';
//         csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

//         const methodInput = document.createElement('input');
//         methodInput.type = 'hidden';
//         methodInput.name = '_method';
//         methodInput.value = 'DELETE';

//         form.appendChild(csrfToken);
//         form.appendChild(methodInput);
//         document.body.appendChild(form);
//         form.submit();
//     }
// }
function confirmDelete(accountId) {
    if (confirm('Bạn có chắc chắn muốn xóa tài khoản này?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin/products') }}/{{ $product->id }}/accounts/${accountId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function bulkAction(action) {
    const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
    if (selectedCheckboxes.length === 0) return;

    let message = '';
    switch(action) {
        case 'delete':
            message = 'Bạn có chắc chắn muốn xóa các tài khoản đã chọn? Hành động này không thể hoàn tác!';
            break;
    }

    if (confirm(message)) {
        const accountIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/products/{{ $product->id }}/accounts/bulk-action`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;

        const idsInput = document.createElement('input');
        idsInput.type = 'hidden';
        idsInput.name = 'account_ids';
        idsInput.value = JSON.stringify(accountIds);

        form.appendChild(csrfToken);
        form.appendChild(actionInput);
        form.appendChild(idsInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
