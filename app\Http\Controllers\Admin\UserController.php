<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Rules\UniqueDisplayName;

class UserController extends Controller
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * Hiển thị danh sách users
     */
    public function index(Request $request)
    {
        $filters = $request->only(['search', 'status', 'role']);
        $users = $this->userService->getUsersList($filters);
        $stats = $this->userService->getUserStats();

        return view('admin.users.index', compact('users') + $stats);
    }

    /**
     * Hiển thị form tạo user mới
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Lưu user mới
     */
    public function store(Request $request)
    {
        $request->validate([
            'username' => 'required|string|min:6|max:20|unique:users|regex:/^[a-z0-9]+$/',
            'display_name' => ['nullable', 'string', 'min:3', 'max:20', 'regex:/^[a-zA-Z0-9\s\p{L}]+$/u', new UniqueDisplayName()],
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|max:30|confirmed',
            'role' => 'required|in:user,admin',
            'balance' => 'nullable|numeric|min:0',
            'email_verified' => 'boolean',
        ], [
            'username.min' => 'Username phải có ít nhất 6 ký tự.',
            'username.max' => 'Username không được vượt quá 20 ký tự.',
            'username.regex' => 'Username chỉ được chứa chữ cái thường (a-z) và số (0-9).',
            'display_name.min' => 'Tên hiển thị phải có ít nhất 3 ký tự.',
            'display_name.max' => 'Tên hiển thị không được vượt quá 20 ký tự.',
            'display_name.regex' => 'Tên hiển thị chỉ được chứa chữ cái, số và khoảng trắng.',
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự.',
            'password.max' => 'Mật khẩu không được vượt quá 30 ký tự.',
        ]);

        $this->userService->createUser($request->all());

        return redirect()->route('admin.users.index')
            ->with('success', 'Tạo người dùng thành công!');
    }

    /**
     * Hiển thị chi tiết user
     */
    public function show(User $user)
    {
        $orders = $this->userService->getUserOrders($user);

        return view('admin.users.show', compact('user', 'orders'));
    }

    /**
     * Hiển thị form chỉnh sửa user
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Cập nhật user
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'username' => ['required', 'string', 'min:6', 'max:20', 'regex:/^[a-z0-9]+$/', Rule::unique('users')->ignore($user->id)],
            'display_name' => ['nullable', 'string', 'min:3', 'max:20', 'regex:/^[a-zA-Z0-9\s\p{L}]+$/u', new UniqueDisplayName($user->id)],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|max:30|confirmed',
            'role' => 'required|in:user,admin',
            'balance' => 'nullable|numeric|min:0',
            'email_verified' => 'boolean',
        ], [
            'username.min' => 'Username phải có ít nhất 6 ký tự.',
            'username.max' => 'Username không được vượt quá 20 ký tự.',
            'username.regex' => 'Username chỉ được chứa chữ cái thường (a-z) và số (0-9).',
            'display_name.min' => 'Tên hiển thị phải có ít nhất 3 ký tự.',
            'display_name.max' => 'Tên hiển thị không được vượt quá 20 ký tự.',
            'display_name.regex' => 'Tên hiển thị chỉ được chứa chữ cái, số và khoảng trắng.',
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự.',
            'password.max' => 'Mật khẩu không được vượt quá 30 ký tự.',
        ]);

        $this->userService->updateUser($user, $request->all());

        return redirect()->route('admin.users.index')
            ->with('success', 'Cập nhật người dùng thành công!');
    }

    /**
     * Xóa user
     */
    public function destroy(User $user)
    {
        try {
            $this->userService->deleteUser($user);
            return redirect()->route('admin.users.index')
                ->with('success', 'Xóa user thành công!');
        } catch (\Exception $e) {
            return redirect()->route('admin.users.index')
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Toggle trạng thái active/inactive
     */
    public function toggleStatus(User $user)
    {
        $status = $this->userService->toggleUserStatus($user);

        return redirect()->back()
            ->with('success', "Đã {$status} user thành công!");
    }

    /**
     * Adjust user balance
     */
    public function adjustBalance(Request $request, User $user)
    {
        $request->validate([
            'action' => 'required|in:add,subtract',
            'amount' => 'required|numeric|min:1000|max:1000000',
            'description' => 'required|string|max:255',
        ]);

        try {
            $this->userService->adjustUserBalance(
                $user,
                auth()->user(),
                $request->action,
                (int) $request->amount,
                $request->description
            );

            $message = $request->action === 'add'
                ? "Đã cộng " . number_format($request->amount) . "đ vào tài khoản {$user->username}"
                : "Đã trừ " . number_format($request->amount) . "đ từ tài khoản {$user->username}";

            return response()->json([
                'success' => true,
                'message' => $message,
                'new_balance' => $user->fresh()->balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
