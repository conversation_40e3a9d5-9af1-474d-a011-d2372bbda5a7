<?php

namespace App\Http\Controllers;

use App\Models\Guide;
use Illuminate\Http\Request;

class GuideController extends Controller
{
    /**
     * Hi<PERSON>n thị danh sách hướng dẫn
     */
    public function index(Request $request)
    {
        $query = Guide::with('author')
            ->published()
            ->ordered();

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $guides = $query->paginate(15);

        return view('guides.index', compact('guides'));
    }

    /**
     * Hiển thị chi tiết hướng dẫn
     */
    public function show($slug)
    {
        $guide = Guide::with('author')
            ->where('slug', $slug)
            ->where('status', 'published')
            ->firstOrFail();

        // Get related guides
        $relatedGuides = Guide::published()
            ->where('id', '!=', $guide->id)
            ->ordered()
            ->limit(4)
            ->get();

        return view('guides.show', compact('guide', 'relatedGuides'));
    }
}
