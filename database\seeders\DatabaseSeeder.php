<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Chạy các seeder theo thứ tự phụ thuộc
        $this->call([
            UserSeeder::class,              // Tạo users trước (cần cho admin_id)
            CategorySeeder::class,          // Tạo categories trước (cần cho products/services)
            ProductSeeder::class,           // Tạo products (cần category_id)
            ProductAccountSeeder::class,    // Tạo accounts (cần product_id)
            ServiceSeeder::class,           // Tạo services (cần category_id)
            // VoucherSeeder::class,        // Bỏ voucher seeder - user tự tạo
            GuideSeeder::class,             // Tạo guides (cần admin_id)
            OrderSeeder::class,             // Tạo orders (cần user_id, product_id, account_id)
            ServiceOrderSeeder::class,      // Tạo service orders (cần user_id, service_id)
            TopupTransactionSeeder::class,  // Tạo topup transactions (cần user_id)
            BalanceTransactionSeeder::class, // Tạo balance transactions (cần user_id, admin_id)
        ]);
    }
}
