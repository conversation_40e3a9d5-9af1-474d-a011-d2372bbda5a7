@extends('layouts.admin')

@section('title', 'Sửa danh mục')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Sửa danh mục: {{ $category->name }}</h1>
            <p class="text-gray-600 mt-1">Chỉnh sửa thông tin và cài đặt danh mục</p>
        </div>
        <div class="flex-shrink-0">
            <a href="{{ route('admin.categories.index') }}" class="inline-flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <form action="{{ route('admin.categories.update', $category) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Category Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Tên danh mục <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ old('name', $category->name) }}"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                        placeholder="Nhập tên danh mục"
                    >
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Slug -->
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                        Slug
                    </label>
                    <input
                        type="text"
                        id="slug"
                        name="slug"
                        value="{{ old('slug', $category->slug) }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('slug') border-red-500 @enderror"
                        placeholder="vd: lien-minh-huyen-thoai"
                    >
                    @error('slug')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Publisher -->
                <div>
                    <label for="publisher" class="block text-sm font-medium text-gray-700 mb-2">
                        Nhà phát hành
                    </label>
                    <input
                        type="text"
                        id="publisher"
                        name="publisher"
                        value="{{ old('publisher', $category->publisher) }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('publisher') border-red-500 @enderror"
                        placeholder="vd: Riot Games, EA Sports"
                    >
                    @error('publisher')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Trạng thái <span class="text-red-500">*</span>
                    </label>
                    <select
                        id="status"
                        name="status"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-500 @enderror"
                    >
                        <option value="">Chọn trạng thái</option>
                        <option value="active" {{ old('status', $category->status) === 'active' ? 'selected' : '' }}>Hoạt động</option>
                        <option value="inactive" {{ old('status', $category->status) === 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Current Image -->
            @if($category->image)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Hình ảnh hiện tại:</label>
                    <div class="w-32 h-32 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden mb-4">
                        <img src="{{ storage_url($category->image) }}" alt="{{ $category->name }}" class="w-full h-full object-contain">
                    </div>
                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-3">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-amber-500 mt-0.5 mr-2"></i>
                            <p class="text-sm text-amber-700">
                                Chọn hình ảnh mới bên dưới sẽ thay thế hình ảnh hiện tại
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    {{ $category->image ? 'Thay đổi hình ảnh' : 'Hình ảnh danh mục' }}
                </label>

                <!-- Upload Zone -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors" id="upload-zone">
                    <div class="space-y-2">
                        <i class="fas fa-image text-3xl text-gray-400"></i>
                        <div>
                            <p class="text-sm text-gray-600">
                                <span class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer" id="file-trigger">
                                    Nhấp để chọn ảnh
                                </span>
                                hoặc kéo thả ảnh vào đây
                            </p>
                            <p class="text-xs text-gray-500 mt-1">PNG, JPG, GIF tối đa 2MB</p>
                        </div>
                    </div>
                    <input
                        type="file"
                        id="image"
                        name="image"
                        accept="image/*"
                        class="hidden"
                    >
                </div>

                @error('image')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror

                <!-- Image Preview -->
                <div id="image-preview" class="mt-4 hidden">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-sm font-medium text-gray-900">Ảnh mới đã chọn</h4>
                        <button type="button" id="remove-image" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-trash mr-1"></i>Xóa ảnh
                        </button>
                    </div>
                    <div class="w-32 h-32 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                        <img id="preview-img" src="" alt="Preview" class="w-full h-full object-contain">
                    </div>
                </div>
            </div>



            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.categories.index') }}"
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Hủy
                </a>
                <button type="submit"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Cập nhật
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image upload functionality
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    const removeBtn = document.getElementById('remove-image');
    const uploadZone = document.getElementById('upload-zone');
    const fileTrigger = document.getElementById('file-trigger');

    // Click handlers
    fileTrigger.addEventListener('click', () => imageInput.click());

    // File input change handler
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFile(file);
        }
    });

    // Remove image handler
    removeBtn.addEventListener('click', function() {
        imageInput.value = '';
        imagePreview.classList.add('hidden');
        previewImg.src = '';
    });

    // Drag and drop handlers
    uploadZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadZone.classList.add('border-blue-400', 'bg-blue-50');
    });

    uploadZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('border-blue-400', 'bg-blue-50');
    });

    uploadZone.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('border-blue-400', 'bg-blue-50');

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith('image/')) {
            // Set the file to the input
            const dt = new DataTransfer();
            dt.items.add(files[0]);
            imageInput.files = dt.files;

            handleFile(files[0]);
        }
    });

    function handleFile(file) {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }
    }

    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');

    nameInput.addEventListener('input', function() {
        if (!slugInput.dataset.manual) {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            slugInput.value = slug;
        }
    });

    slugInput.addEventListener('input', function() {
        this.dataset.manual = 'true';
    });
});


</script>
@endpush
@endsection
