@extends('layouts.app')

@section('title', 'Đơn hàng của tôi - AccReroll')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-4 lg:py-8">
    <!-- Page Header -->
    <div class="mb-6 lg:mb-8">
        <h1 class="text-2xl lg:text-3xl font-bold text-gray-900">Đơn hàng của tôi</h1>
        <p class="text-gray-600 mt-1 lg:mt-2 text-sm lg:text-base">Quản lý và theo dõi các đơn hàng đã mua</p>
    </div>

    @if($orders->count() > 0)
        <!-- Orders List -->
        <div class="space-y-4">
            @foreach($orders as $order)
            <a href="{{ route('orders.show', $order) }}" class="block bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md hover:border-blue-200 transition-all duration-200 cursor-pointer">
                <!-- Order Card Content -->
                <div class="p-2 sm:p-3 lg:p-4">
                    <!-- Header Row -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <h3 class="text-sm lg:text-base font-semibold text-blue-700">{{ $order->order_number }}</h3>
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium
                                    @if($order->status === 'pending') bg-amber-100 text-amber-800
                                    @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                                    @elseif($order->status === 'completed') bg-emerald-100 text-emerald-800
                                    @elseif($order->status === 'cancelled') bg-red-100 text-red-800
                                    @endif">
                                    @if($order->status === 'pending')
                                        <i class="fas fa-clock mr-1.5"></i>
                                    @elseif($order->status === 'processing')
                                        <i class="fas fa-cog mr-1.5"></i>
                                    @elseif($order->status === 'completed')
                                        <i class="fas fa-check-circle mr-1.5"></i>
                                    @elseif($order->status === 'cancelled')
                                        <i class="fas fa-times-circle mr-1.5"></i>
                                    @endif
                                    {{ $order->status_text }}
                                </span>
                            </div>
                            <p class="text-sm text-gray-500">{{ $order->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg lg:text-xl font-bold text-red-600">{{ number_format($order->total_amount) }}đ</div>
                            <p class="text-sm text-gray-500">{{ $order->orderItems->count() }} sản phẩm</p>
                        </div>
                    </div>

                    <!-- Products List -->
                    <div class="space-y-2">
                        @php
                            $groupedItems = $order->orderItems->groupBy('product_id');
                        @endphp

                        @foreach($groupedItems as $productId => $items)
                        @php
                            $firstItem = $items->first();
                            $totalQuantity = $items->sum('quantity');
                            $totalPrice = $items->sum(function($item) { return $item->price * $item->quantity; });
                        @endphp
                        <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                            <!-- Product Image -->
                            <div class="flex-shrink-0">
                                <div class="w-12 h-10 lg:w-16 lg:h-12 bg-white rounded-lg flex items-center justify-center overflow-hidden border border-gray-300 shadow-sm">
                                    @if($firstItem->product->main_image)
                                        <img src="{{ storage_url($firstItem->product->main_image) }}" alt="{{ $firstItem->product->name }}"
                                             class="max-w-full max-h-full object-contain">
                                    @else
                                        <i class="fas fa-gamepad text-gray-400 text-lg"></i>
                                    @endif
                                </div>
                            </div>

                            <!-- Product Info -->
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-gray-800 text-sm lg:text-base truncate">{{ $firstItem->product->name }}</h4>
                                <p class="text-xs lg:text-sm text-gray-500">{{ $firstItem->product->category->name }}</p>
                                @if($totalQuantity > 1)
                                    <p class="text-xs text-blue-600 font-medium">{{ $totalQuantity }} tài khoản</p>
                                @endif
                            </div>

                            <!-- Price Info -->
                            <div class="text-right flex-shrink-0">
                                <div class="font-semibold text-red-600 text-sm lg:text-base">{{ format_money($totalPrice) }}</div>
                                <div class="text-xs lg:text-sm text-gray-500">{{ format_money($firstItem->price) }} × {{ $totalQuantity }}</div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                </div>
            </a>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8 flex justify-center">
            {{ $orders->links('pagination.custom') }}
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="fas fa-shopping-bag text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Chưa có đơn hàng nào</h3>
            <p class="text-gray-600 mb-6">Bạn chưa mua sản phẩm nào. Hãy khám phá các sản phẩm tuyệt vời của chúng tôi!</p>
        </div>
    @endif
</div>
@endsection
