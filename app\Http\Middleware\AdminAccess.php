<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Kiểm tra secret key trong URL
        $secretKey = env('ADMIN_SECRET_KEY');
        
        if (!$secretKey) {
            abort(404, 'Admin secret key not configured');
        }

        // Lấy secret key từ URL path
        $pathSegments = explode('/', trim($request->path(), '/'));
        $urlSecretKey = $pathSegments[0] ?? null;

        // Kiểm tra secret key có đúng không
        if ($urlSecretKey !== $secretKey) {
            abort(404, 'Page not found');
        }

        // Nếu đang truy cập trang login admin, không cần kiểm tra auth
        if ($request->routeIs('admin.login') || $request->routeIs('admin.login.post')) {
            // Nếu đã đăng nhập và là admin, redirect về dashboard
            if (Auth::check() && Auth::user()->role === 'admin') {
                return redirect()->route('admin.dashboard');
            }
            return $next($request);
        }

        // Kiểm tra đăng nhập
        if (!Auth::check()) {
            return redirect()->route('admin.login');
        }

        // Kiểm tra role admin
        if (Auth::user()->role !== 'admin') {
            Auth::logout();
            return redirect()->route('admin.login')->withErrors([
                'login' => 'Bạn không có quyền truy cập vào khu vực này.'
            ]);
        }

        return $next($request);
    }
}
