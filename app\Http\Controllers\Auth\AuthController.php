<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use App\Rules\TurnstileRule;
use App\Rules\UniqueDisplayName;

class AuthController extends Controller
{
    // Hiển thị form đăng nhập
    public function showLoginForm()
    {
        return view('auth.login');
    }

    // Xử lý đăng nhập
    public function login(Request $request)
    {
        $rules = [
            'login' => 'required|string',
            'password' => 'required',
        ];

        // Add CAPTCHA validation only if both keys are configured
        if (env('TURNSTILE_SECRET_KEY') && env('TURNSTILE_SITE_KEY')) {
            $rules['cf-turnstile-response'] = ['required', new TurnstileRule()];
        }

        $request->validate($rules);

        $loginField = filter_var($request->login, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        
        $credentials = [
            $loginField => $request->login,
            'password' => $request->password,
            'is_active' => true
        ];

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();

            return redirect()->intended('/');
        }

        return back()->withErrors([
            'login' => 'Thông tin đăng nhập không chính xác.',
        ])->onlyInput('login');
    }

    // Hiển thị form đăng ký
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    // Xử lý đăng ký
    public function register(Request $request)
    {
        $rules = [
            'username' => 'required|string|min:6|max:20|unique:users|regex:/^[a-z0-9]+$/',
            'display_name' => ['nullable', 'string', 'min:3', 'max:20', 'regex:/^[a-zA-Z0-9\s\p{L}]+$/u', new UniqueDisplayName()],
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', 'min:8', 'max:30'],
        ];

        // Add CAPTCHA validation only if both keys are configured
        if (env('TURNSTILE_SECRET_KEY') && env('TURNSTILE_SITE_KEY')) {
            $rules['cf-turnstile-response'] = ['required', new TurnstileRule()];
        }

        $request->validate($rules, [
            'username.min' => 'Username phải có ít nhất 6 ký tự.',
            'username.max' => 'Username không được vượt quá 20 ký tự.',
            'username.regex' => 'Username chỉ được chứa chữ cái thường (a-z) và số (0-9).',
            'display_name.min' => 'Tên hiển thị phải có ít nhất 3 ký tự.',
            'display_name.max' => 'Tên hiển thị không được vượt quá 20 ký tự.',
            'display_name.regex' => 'Tên hiển thị chỉ được chứa chữ cái, số và khoảng trắng.',
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự.',
            'password.max' => 'Mật khẩu không được vượt quá 30 ký tự.',
        ]);

        $user = User::create([
            'username' => $request->username,
            'display_name' => $request->display_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'user',
            'balance' => 0,
        ]);

        Auth::login($user);

        return redirect('/')->with('success', 'Đăng ký thành công!');
    }

    // Đăng xuất
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
