<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\ServiceOrder;
use App\Models\Category;
use App\Models\BalanceTransaction;
use App\Services\VoucherService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ServiceController extends Controller
{
    /**
     * Hiển thị danh sách services
     */
    public function index(Request $request)
    {
        $query = Service::active()->with('category');

        // Filter by category
        if ($request->category) {
            $query->byCategory($request->category);
        }

        // Search
        if ($request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Sort
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');

        if ($sortBy === 'price') {
            $query->orderBy('price', $sortOrder);
        } else {
            $query->orderBy('name', $sortOrder);
        }

        $services = $query->paginate(12);
        $categories = Category::where('status', 'active')
            ->withCount(['services' => function ($query) {
                $query->where('status', 'active');
            }])
            ->orderBy('sort_order')
            ->get();

        return view('services.index', compact('services', 'categories'));
    }

    /**
     * Hiển thị chi tiết service
     */
    public function show(Service $service)
    {
        $service->load('category');
        $relatedServices = Service::active()
            ->with('category') // Eager load category để tránh N+1
            ->where('category_id', $service->category_id)
            ->where('id', '!=', $service->id)
            ->limit(4)
            ->get();

        return view('services.show', compact('service', 'relatedServices'));
    }

    /**
     * Xử lý mua service
     */
    public function purchase(Request $request, Service $service)
    {
        $request->validate([
            'content' => 'required|string|max:2000',
            'voucher_code' => 'nullable|string'
        ], [
            'content.required' => 'Vui lòng nhập thông tin cần thiết.',
            'content.max' => 'Thông tin không được vượt quá 2000 ký tự.'
        ]);

        // Kiểm tra service có active không
        if ($service->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Dịch vụ này hiện không khả dụng.'
            ]);
        }

        $user = Auth::user();
        $totalAmount = $service->price;
        $discountAmount = 0;
        $voucherService = new VoucherService();

        // Xử lý voucher nếu có
        if ($request->voucher_code) {
            $voucherValidation = $voucherService->validateVoucherForOrder(
                $request->voucher_code,
                $user,
                'service',
                $service->category_id,
                $totalAmount,
                [$service->id] // Truyền service ID để kiểm tra specific items
            );

            if (!$voucherValidation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $voucherValidation['message']
                ]);
            }

            $discountAmount = $voucherValidation['discount_amount'];
        }

        $finalAmount = $totalAmount - $discountAmount;

        // Kiểm tra số dư
        if ($user->balance < $finalAmount) {
            return response()->json([
                'success' => false,
                'message' => 'Số dư không đủ để thực hiện giao dịch này.'
            ]);
        }

        $serviceOrder = null;

        try {
            DB::transaction(function () use ($user, $service, $request, $finalAmount, $voucherService, &$serviceOrder) {
                // Trừ tiền user
                // $user->subtractBalance($finalAmount);

                // Tạo service order
                $serviceOrder = ServiceOrder::create([
                    'user_id' => $user->id,
                    'service_id' => $service->id,
                    'status' => 'pending',
                    'price' => $finalAmount,
                    'content' => $request->content,
                    'notes' => null
                ]);

                // Tạo order number sau khi có ID
                $serviceOrder->update([
                    'order_number' => $serviceOrder->generateOrderNumber()
                ]);
                
                //Tạo BalanceTransaction log trước khi trừ tiền
                $description = "Mua dịch vụ: {$service->name}";
                if ($request->voucher_code) {
                    $description .= " (Voucher: {$request->voucher_code})";
                }

                BalanceTransaction::createTransaction(
                    user: $user,
                    type: BalanceTransaction::TYPE_SERVICE_PURCHASE,
                    amount: -$finalAmount, // Số âm vì trừ tiền
                    description: $description,
                    reference: $serviceOrder,
                    metadata: [
                        'service_id' => $service->id,
                        'service_name' => $service->name,
                        'service_price' => $service->price,
                        'final_amount' => $finalAmount,
                        'voucher_code' => $request->voucher_code,
                        'order_number' => $serviceOrder->order_number,
                        'content' => $request->content
                    ]
                );

                // Trừ tiền user
                $user->subtractBalance($finalAmount);

                // Áp dụng voucher nếu có
                if ($request->voucher_code) {
                    $voucherService->applyVoucherToOrder($request->voucher_code, $user, null, $serviceOrder->id);
                }

                // Tạo voucher tự động cho service purchase
                $voucherService->createAutoVoucherForServicePurchase($serviceOrder);
            });

            // Kiểm tra xem serviceOrder có được tạo thành công không
            if (!$serviceOrder || !$serviceOrder->id) {
                throw new \Exception('Không thể tạo đơn hàng dịch vụ.');
            }

            return response()->json([
                'success' => true,
                'message' => 'Đặt dịch vụ thành công! Chúng tôi sẽ xử lý yêu cầu của bạn trong thời gian sớm nhất.',
                'redirect' => route('service-orders.show', $serviceOrder->id)
            ]);

        } catch (\Exception $e) {
            Log::error('Service purchase failed', [
                'user_id' => $user->id,
                'service_id' => $service->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xử lý đơn hàng. Vui lòng thử lại sau.'
            ]);
        }
    }
}
