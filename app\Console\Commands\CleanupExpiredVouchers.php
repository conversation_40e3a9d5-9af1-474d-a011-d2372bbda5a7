<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserVoucher;
use Carbon\Carbon;

class CleanupExpiredVouchers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vouchers:cleanup
                            {--days=0 : Number of days after expiry to keep vouchers (0 = delete immediately)}
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--force : Force deletion without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup expired and unused vouchers to free up database space';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info("🧹 Voucher Cleanup Tool");
        if ($days === 0) {
            $this->info("Looking for expired vouchers (immediate cleanup)...");
        } else {
            $this->info("Looking for vouchers expired more than {$days} days ago...");
        }

        // Tính ngày cutoff
        if ($days === 0) {
            // Xóa ngay khi hết hạn
            $cutoffDate = Carbon::now();
        } else {
            // Xóa sau grace period
            $cutoffDate = Carbon::now()->subDays($days);
        }

        // Tìm voucher hết hạn và chưa sử dụng
        $expiredVouchers = UserVoucher::whereNull('used_at')
            ->where('expires_at', '<', $cutoffDate)
            ->with(['voucher:id,name,code', 'user:id,name,email']);

        $count = $expiredVouchers->count();

        if ($count === 0) {
            $this->info("✅ No expired vouchers found to cleanup.");
            return 0;
        }

        $this->warn("Found {$count} expired vouchers to cleanup:");

        // Hiển thị thống kê
        $this->showStatistics($expiredVouchers->get());

        if ($dryRun) {
            $this->info("🔍 DRY RUN MODE - No vouchers will be deleted");
            $this->showVoucherDetails($expiredVouchers->limit(10)->get());
            return 0;
        }

        // Xác nhận trước khi xóa
        if (!$force && !$this->confirm("Are you sure you want to delete {$count} expired vouchers?")) {
            $this->info("Operation cancelled.");
            return 0;
        }

        // Thực hiện cleanup
        $this->performCleanup($expiredVouchers, $count);

        return 0;
    }

    /**
     * Hiển thị thống kê voucher hết hạn
     */
    private function showStatistics($vouchers)
    {
        $stats = $vouchers->groupBy('voucher.name')->map(function ($group) {
            return [
                'count' => $group->count(),
                'code' => $group->first()->voucher->code ?? 'N/A',
                'oldest_expiry' => $group->min('expires_at'),
                'newest_expiry' => $group->max('expires_at'),
            ];
        });

        $this->table(
            ['Voucher Name', 'Code', 'Count', 'Oldest Expiry', 'Newest Expiry'],
            $stats->map(function ($stat, $name) {
                return [
                    $name,
                    $stat['code'],
                    $stat['count'],
                    Carbon::parse($stat['oldest_expiry'])->format('Y-m-d'),
                    Carbon::parse($stat['newest_expiry'])->format('Y-m-d'),
                ];
            })->toArray()
        );
    }

    /**
     * Hiển thị chi tiết một số voucher
     */
    private function showVoucherDetails($vouchers)
    {
        $this->info("\nSample vouchers to be deleted:");
        $this->table(
            ['ID', 'User', 'Voucher', 'Expired At', 'Days Ago'],
            $vouchers->map(function ($userVoucher) {
                return [
                    $userVoucher->id,
                    $userVoucher->user->name ?? 'Unknown',
                    $userVoucher->voucher->name ?? 'Unknown',
                    Carbon::parse($userVoucher->expires_at)->format('Y-m-d H:i'),
                    Carbon::parse($userVoucher->expires_at)->diffInDays(now()) . ' days',
                ];
            })->toArray()
        );

        if ($vouchers->count() >= 10) {
            $this->info("... and more");
        }
    }

    /**
     * Thực hiện cleanup
     */
    private function performCleanup($expiredVouchersQuery, $totalCount)
    {
        $this->info("🗑️  Starting cleanup...");

        $bar = $this->output->createProgressBar($totalCount);
        $bar->start();

        $deletedCount = 0;
        $batchSize = 100;

        // Xóa theo batch để tránh memory issues
        $expiredVouchersQuery->chunk($batchSize, function ($vouchers) use (&$deletedCount, $bar) {
            $ids = $vouchers->pluck('id')->toArray();
            
            // Xóa batch
            $deleted = UserVoucher::whereIn('id', $ids)->delete();
            $deletedCount += $deleted;
            
            $bar->advance($deleted);
        });

        $bar->finish();
        $this->newLine();

        $this->info("✅ Cleanup completed!");
        $this->info("Deleted {$deletedCount} expired vouchers");

        // Hiển thị thống kê sau cleanup
        $remainingExpired = UserVoucher::whereNull('used_at')
            ->where('expires_at', '<', now())
            ->count();

        if ($remainingExpired > 0) {
            $this->warn("⚠️  {$remainingExpired} expired vouchers still remain (within {$this->option('days')} days grace period)");
        } else {
            $this->info("🎉 No expired vouchers remaining!");
        }
    }
}
