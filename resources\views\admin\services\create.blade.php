@extends('layouts.admin')

@section('title', 'Thêm Dịch vụ')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Thêm dịch vụ</h1>
            <p class="text-gray-600 mt-1">Tạo dịch vụ mới cho khách hàng</p>
        </div>
        <div class="flex-shrink-0">
            <a href="{{ route('admin.services.index') }}" class="inline-flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <form method="POST" action="{{ route('admin.services.store') }}" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Service Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Tên dịch vụ <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ old('name') }}"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                        placeholder="VD: Tăng like Facebook, Tăng follow Instagram"
                    >
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category_search" class="block text-sm font-medium text-gray-700 mb-2">
                        Danh mục <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="hidden" name="category_id" id="category_id" value="{{ old('category_id') }}" required>
                        <input type="text" id="category_search"
                               placeholder="Tìm kiếm danh mục..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('category_id') border-red-500 @enderror"
                               autocomplete="off">
                        <div id="category_dropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden max-h-60 overflow-y-auto">
                            <!-- Dropdown items will be populated by JavaScript -->
                        </div>
                    </div>
                    @error('category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Price -->
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                        Giá bán (VNĐ) <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="number"
                        id="price"
                        name="price"
                        value="{{ old('price') }}"
                        required
                        min="0"
                        step="1000"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('price') border-red-500 @enderror"
                        placeholder="50000"
                    >
                    @error('price')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Trạng thái <span class="text-red-500">*</span>
                    </label>
                    <select
                        id="status"
                        name="status"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-500 @enderror"
                    >
                        <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Hoạt động</option>
                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Tạm dừng</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả <span class="text-red-500">*</span>
                </label>
                <textarea
                    id="description"
                    name="description"
                    rows="4"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror"
                    placeholder="Mô tả chi tiết về dịch vụ..."
                >{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Order Instructions -->
            <div>
                <label for="order_instructions" class="block text-sm font-medium text-gray-700 mb-2">
                    Hướng dẫn đặt hàng
                    <span class="text-gray-500 text-xs">(Hiển thị trong modal đặt dịch vụ)</span>
                </label>
                <textarea
                    id="order_instructions"
                    name="order_instructions"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('order_instructions') border-red-500 @enderror"
                    placeholder="Ví dụ: Hãy nhập link Facebook của bạn, Hãy nhập tài khoản Instagram..."
                >{{ old('order_instructions') }}</textarea>
                <p class="mt-1 text-xs text-gray-500">Để trống sẽ hiển thị hướng dẫn mặc định</p>
                @error('order_instructions')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    Hình ảnh dịch vụ
                </label>

                <!-- Upload Zone -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors" id="upload-zone">
                    <div class="space-y-2">
                        <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                        <div>
                            <p class="text-sm text-gray-600">
                                <span class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer" id="file-trigger">
                                    Nhấp để chọn ảnh
                                </span>
                                hoặc kéo thả ảnh vào đây
                            </p>
                            <p class="text-xs text-gray-500 mt-1">PNG, JPG, GIF tối đa 2MB mỗi file. Tối đa 5 ảnh.</p>
                        </div>
                    </div>
                    <input
                        type="file"
                        id="images"
                        name="images[]"
                        accept="image/*"
                        multiple
                        class="hidden"
                    >
                </div>

                @error('images.*')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror

                <!-- Action Buttons -->
                <div class="flex gap-2 mt-3">
                    <button type="button" id="add-more-images" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Thêm ảnh
                    </button>
                    <button type="button" id="clear-images" class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 transition-colors hidden">
                        <i class="fas fa-trash mr-2"></i>Xóa tất cả
                    </button>
                </div>

                <!-- Hidden inputs for additional files -->
                <div id="additional-inputs"></div>

                <!-- Image Preview -->
                <div id="image-preview" class="mt-6 hidden">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-900">Ảnh đã chọn (<span id="image-count">0</span>/5)</h4>
                        <p class="text-xs text-gray-500">Ảnh đầu tiên sẽ là ảnh chính</p>
                    </div>
                    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-4" id="preview-container">
                        <!-- Preview images will be inserted here -->
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4">
                <a href="{{ route('admin.services.index') }}"
                   class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                    Hủy
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>Tạo Dịch vụ
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image management with accumulative file selection
    let allSelectedFiles = [];
    const imageInput = document.getElementById('images');
    const imagePreview = document.getElementById('image-preview');
    const previewContainer = document.getElementById('preview-container');
    const imageCount = document.getElementById('image-count');
    const addMoreBtn = document.getElementById('add-more-images');
    const clearBtn = document.getElementById('clear-images');
    const additionalInputsContainer = document.getElementById('additional-inputs');
    const uploadZone = document.getElementById('upload-zone');
    const fileTrigger = document.getElementById('file-trigger');

    // File input change handler
    imageInput.addEventListener('change', function(e) {
        handleFiles(Array.from(e.target.files));
        e.target.value = '';
    });

    // Click handlers
    fileTrigger.addEventListener('click', () => imageInput.click());
    addMoreBtn.addEventListener('click', function() {
        if (allSelectedFiles.length < 5) {
            imageInput.click();
        } else {
            alert('Chỉ được chọn tối đa 5 ảnh');
        }
    });

    clearBtn.addEventListener('click', function() {
        allSelectedFiles = [];
        additionalInputsContainer.innerHTML = '';
        updatePreview();
    });

    // Drag and drop handlers
    uploadZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadZone.classList.add('border-blue-400', 'bg-blue-50');
    });

    uploadZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('border-blue-400', 'bg-blue-50');
    });

    uploadZone.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('border-blue-400', 'bg-blue-50');

        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        handleFiles(files);
    });

    function handleFiles(newFiles) {
        // Add new files to the collection (up to 5 total)
        newFiles.forEach(file => {
            if (allSelectedFiles.length < 5 && file.type.startsWith('image/')) {
                allSelectedFiles.push(file);
            }
        });

        updatePreview();
        createHiddenInputs();
    }

    function createHiddenInputs() {
        // Clear existing hidden inputs
        additionalInputsContainer.innerHTML = '';

        // Create hidden inputs for all files
        allSelectedFiles.forEach((file, index) => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'file';
            hiddenInput.name = `images[${index}]`;
            hiddenInput.style.display = 'none';

            // Create a new FileList with just this file
            const dt = new DataTransfer();
            dt.items.add(file);
            hiddenInput.files = dt.files;

            additionalInputsContainer.appendChild(hiddenInput);
        });
    }

    function updatePreview() {
        if (allSelectedFiles.length === 0) {
            imagePreview.classList.add('hidden');
            clearBtn.classList.add('hidden');
            return;
        }

        imagePreview.classList.remove('hidden');
        clearBtn.classList.remove('hidden');
        previewContainer.innerHTML = '';
        imageCount.textContent = allSelectedFiles.length;

        allSelectedFiles.forEach((file, index) => {
            // Create container first
            const div = document.createElement('div');
            div.className = 'relative bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow';

            // Add loading placeholder
            div.innerHTML = '<div class="w-full h-24 bg-gray-100 flex items-center justify-center text-gray-500 text-sm">Đang tải...</div>';
            previewContainer.appendChild(div);

            // Try to create object URL first (more reliable)
            let imageSrc;
            try {
                imageSrc = URL.createObjectURL(file);
            } catch (e) {
                console.error('Failed to create object URL:', e);
                // Fallback to FileReader
                const reader = new FileReader();
                reader.onload = function(e) {
                    imageSrc = e.target.result;
                    createImageElement();
                };
                reader.onerror = function() {
                    div.innerHTML = '<div class="w-full h-24 bg-red-100 flex items-center justify-center text-red-500 text-sm">Lỗi tải ảnh</div>';
                };
                reader.readAsDataURL(file);
                return;
            }

            createImageElement();

            function createImageElement() {
                const img = document.createElement('img');
                img.src = imageSrc;
                img.className = 'w-full h-32 object-contain';

                const removeBtn = document.createElement('button');
                removeBtn.type = 'button';
                removeBtn.className = 'absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs';
                removeBtn.innerHTML = '×';
                removeBtn.onclick = function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    // Clean up object URL
                    if (imageSrc.startsWith('blob:')) {
                        URL.revokeObjectURL(imageSrc);
                    }
                    allSelectedFiles.splice(index, 1);
                    updatePreview();
                    createHiddenInputs();
                };

                const badge = document.createElement('div');
                badge.className = 'absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded font-medium';
                badge.textContent = index === 0 ? 'Chính' : index + 1;

                // Clear loading and add elements
                div.innerHTML = '';
                div.className = 'relative bg-gray-50 rounded-lg border border-gray-200 overflow-hidden';

                div.appendChild(img);
                div.appendChild(removeBtn);
                div.appendChild(badge);
            }
        });
    }

    // Category search functionality
    const categorySearch = document.getElementById('category_search');
    const categoryId = document.getElementById('category_id');
    const dropdown = document.getElementById('category_dropdown');

    // Set initial value if category is selected (for old input)
    const currentCategoryId = '{{ old("category_id") }}';
    if (currentCategoryId) {
        categoryId.value = currentCategoryId;
        // Find and set the category name from the categories passed to view
        const categories = @json($categories);
        const selectedCategory = categories.find(cat => cat.id == currentCategoryId);
        if (selectedCategory) {
            categorySearch.value = selectedCategory.name;
        }
    }

    let searchTimeout;

    categorySearch.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                searchCategories(query);
            }, 300);
        } else {
            hideDropdown();
            if (query.length === 0) {
                categoryId.value = '';
            }
        }
    });

    categorySearch.addEventListener('focus', function() {
        if (this.value.length >= 2) {
            searchCategories(this.value);
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!categorySearch.contains(e.target) && !dropdown.contains(e.target)) {
            hideDropdown();
        }
    });

    function searchCategories(query) {
        fetch(`{{ route('api.admin.search.categories') }}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                showDropdown(data);
            })
            .catch(error => {
                console.error('Error:', error);
                hideDropdown();
            });
    }

    function showDropdown(categories) {
        dropdown.innerHTML = '';

        if (categories.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'px-3 py-2 text-sm text-gray-500';
            noResults.textContent = 'Không tìm thấy danh mục';
            dropdown.appendChild(noResults);
        } else {
            categories.forEach(category => {
                const item = document.createElement('div');
                item.className = 'px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer';
                item.textContent = category.name;
                item.addEventListener('click', function() {
                    categorySearch.value = category.name;
                    categoryId.value = category.id;
                    hideDropdown();
                });
                dropdown.appendChild(item);
            });
        }

        dropdown.classList.remove('hidden');
    }

    function hideDropdown() {
        dropdown.classList.add('hidden');
    }
});
</script>
@endpush
@endsection
