<!-- Site Announcement Popup -->
<div id="siteAnnouncementPopup" class="fixed inset-0 z-50 hidden flex items-start justify-center pt-16 p-4" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="bg-white rounded-lg shadow-xl max-w-xl w-full mx-4 relative">
        <!-- Close Button -->
        <button onclick="closeSiteAnnouncementPopup()"
                class="absolute top-2 right-2 text-gray-400 hover:text-gray-600 text-2xl font-bold w-8 h-8 flex items-center justify-center leading-none z-10">
            ×
        </button>

        <!-- Header -->
        <div class="text-center py-3 px-6 border-b border-gray-200">
            <h2 class="text-lg font-bold text-red-600 uppercase">THÔNG BÁO</h2>
        </div>

        <!-- Content -->
        <div class="px-6 pb-4">
            <div class="space-y-3 text-sm">
                <!-- Bullet point 1 -->
                <div class="flex items-start space-x-2 mt-2">
                    <span class="text-pink-500 mt-0.5">💖</span>
                    <div class="flex-1">
                        <span class="text-gray-700">Quý khách nếu chưa biết về tài khoản, có thể vào mục </span>
                        <span class="text-blue-500 font-medium">HƯỚNG DẪN</span>
                        <span class="text-gray-700"> trên web để xem, hoặc liên hệ qua trang fanpage</span>
                        <span class="text-gray-700"> để được hỗ trợ.</span>
                    </div>
                </div>

                <!-- Bullet point 2 -->
                <div class="flex items-start space-x-2">
                    <span class="text-green-500 mt-0.5">🟢</span>
                    <div class="flex-1">
                        <span class="font-bold text-green-600">KHI MUA ACC MASTER DUEL</span>
                        <span class="font-bold text-red-500"> BẤT KỲ</span>
                        <span class="text-gray-700"> sẽ được tặng voucher giảm giá 5k khi nhập code thường,</span>
                        <span class="font-bold text-orange-500"> chỉ còn 5k.</span>
                        <span class="text-gray-700"> Ấn vào nút 3 gạch khi đặt để sử dụng voucher được tặng.</span>

                    </div>
                </div>

                <!-- Orange warning box -->
                <div class="bg-yellow-200 rounded p-3">
                    <div class="flex items-start space-x-2">
                        <span class="text-red-500 mt-0.5">⚠️</span>
                        <div class="flex-1">
                            <span class="font-bold text-red-700 underline">LƯU Ý QUAN TRỌNG CHO MASTER DUEL</span>
                            <br>
                            <span class="text-gray-800"> Hãy tìm trận trước khi tiêu gem để kiểm tra có bị shadow ban không.</span>
                            <span class="text-gray-800">Nếu không tìm được trận, vui lòng liên hệ qua fanpage để được </span>
                            <span class="font-bold text-green-600">đổi trả.</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Buttons -->
        <div class="p-6 pt-4 flex gap-2 justify-end border-t border-gray-200">
            <button onclick="dismissAnnouncement(true)"
                    class="px-3 py-1.5 bg-white border-2 border-cyan-400 text-cyan-500 text-xs font-medium rounded hover:bg-cyan-50 transition-colors">
                KHÔNG HIỆN TRONG 3H
            </button>
            <button onclick="dismissAnnouncement(false)"
                    class="px-4 py-1.5 bg-white border-2 border-cyan-400 text-cyan-500 text-xs font-medium rounded hover:bg-cyan-50 transition-colors">
                ĐÓNG
            </button>
        </div>
    </div>
</div>

<script>
// Check if user has dismissed the announcement
function checkSiteAnnouncement() {
    // Only show on homepage
    if (window.location.pathname !== '/') {
        return;
    }

    const dismissed = localStorage.getItem('siteAnnouncementDismissed');
    const dismissedTime = localStorage.getItem('siteAnnouncementDismissedTime');

    if (dismissed === 'permanent') {
        return;
    }

    if (dismissed === 'temporary' && dismissedTime) {
        const dismissTime = new Date(dismissedTime);
        const now = new Date();
        const threeHours = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

        if (now - dismissTime < threeHours) {
            return;
        }
    }

    // Show static announcement
    showSiteAnnouncement();
}

function showSiteAnnouncement() {
    // Show popup (content is already in HTML)
    const popup = document.getElementById('siteAnnouncementPopup');
    popup.classList.remove('hidden');
}

function dismissAnnouncement(temporary = false) {
    if (temporary) {
        localStorage.setItem('siteAnnouncementDismissed', 'temporary');
        localStorage.setItem('siteAnnouncementDismissedTime', new Date().toISOString());
    } else {
        // Just close for current session, don't save to localStorage
        // So it will show again when user visits homepage again
    }

    closeSiteAnnouncementPopup();
}

function closeSiteAnnouncementPopup() {
    const popup = document.getElementById('siteAnnouncementPopup');
    popup.classList.add('hidden');
}

// Close popup when clicking outside
document.getElementById('siteAnnouncementPopup').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSiteAnnouncementPopup();
    }
});

// Check for announcement when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure page is fully loaded
    setTimeout(checkSiteAnnouncement, 200);
});

// Close popup with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('siteAnnouncementPopup').classList.contains('hidden')) {
        closeSiteAnnouncementPopup();
    }
});
</script>
<?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/components/site-announcement-modal.blade.php ENDPATH**/ ?>