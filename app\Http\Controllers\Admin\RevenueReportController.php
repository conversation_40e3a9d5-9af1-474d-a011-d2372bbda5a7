<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\RevenueService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class RevenueReportController extends Controller
{
    protected $revenueService;

    public function __construct(RevenueService $revenueService)
    {
        $this->revenueService = $revenueService;
    }

    public function index(Request $request)
    {
        // Xử lý filter dates - mặc định hiển thị ngày hiện tại
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::today();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::today();
        
        // Đảm bảo end_date không vượt quá hôm nay
        if ($endDate->gt(Carbon::today())) {
            $endDate = Carbon::today();
        }

        // Lấy dữ liệu báo cáo
        $revenueOverview = $this->revenueService->getRevenueOverview($startDate, $endDate);
        $dailyRevenue = $this->revenueService->getDailyRevenue($startDate, $endDate);
        $topProducts = $this->revenueService->getTopProductsByRevenue(10, $startDate, $endDate);
        $topServices = $this->revenueService->getTopServicesByRevenue(10, $startDate, $endDate);
        $topupMethodStats = $this->revenueService->getTopupMethodStats($startDate, $endDate);

        // Chuẩn bị dữ liệu cho chart
        $chartData = [
            'labels' => array_column($dailyRevenue, 'date_formatted'),
            'topup_data' => array_column($dailyRevenue, 'topup_revenue'),
            'product_data' => array_column($dailyRevenue, 'product_revenue'),
            'service_data' => array_column($dailyRevenue, 'service_revenue'),
            'total_data' => array_column($dailyRevenue, 'total_revenue'),
        ];

        return view('admin.revenue-report', compact(
            'revenueOverview',
            'dailyRevenue',
            'topProducts',
            'topServices',
            'topupMethodStats',
            'chartData',
            'startDate',
            'endDate'
        ));
    }

    public function export(Request $request)
    {
        // TODO: Implement export functionality (Excel/PDF)
        return response()->json(['message' => 'Export functionality coming soon']);
    }
}
