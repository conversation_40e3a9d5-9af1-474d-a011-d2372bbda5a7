<?php $__env->startSection('title', $guide->title . ' - Hướng dẫn - AccReroll'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center gap-2">
             <li class="flex items-center">
                <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 px-2 py-1 rounded">
                    <i class="fas fa-home mr-1 md:mr-2"></i>
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
           <li class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                <a href="<?php echo e(route('guides.index')); ?>" class="text-sm font-medium text-gray-700 hover:text-blue-600">
                    Hướng dẫn
                </a>
            </li>
            <li aria-current="page" class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                <span class="text-sm font-medium text-gray-500 px-2 py-1 rounded max-w-[120px] sm:max-w-[200px] lg:max-w-none truncate" title="<?php echo e($guide->title); ?>">
                    <?php echo e($guide->title); ?>

                </span>
            </li>
        </ol>
    </nav>

    <!-- Article Header -->
    <header class="mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-book-open text-blue-600 text-xl"></i>
                </div>
                <div class="flex-1">
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900"><?php echo e($guide->title); ?></h1>
                </div>
            </div>

            <!-- Meta Info -->
            <div class="border-t border-gray-100 pt-4">
                <div class="flex items-center gap-6 text-sm text-gray-600">
                    <div class="flex items-center">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        <span><?php echo e($guide->author->display_name); ?></span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-calendar mr-2 text-blue-600"></i>
                        <span><?php echo e($guide->formatted_created_at); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Article Content -->
    <main class="mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div class="prose prose-lg max-w-none">
                <?php echo nl2br(strip_tags($guide->content, '<p><br><strong><em><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img><iframe><blockquote><code><pre>')); ?>

            </div>
        </div>
    </main>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Prose styling for content */
.prose {
    color: #374151;
    line-height: 1.75;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #111827;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.prose h1 { font-size: 2.25rem; }
.prose h2 { font-size: 1.875rem; }
.prose h3 { font-size: 1.5rem; }
.prose h4 { font-size: 1.25rem; }

.prose p {
    margin-bottom: 1.25rem;
}

.prose img {
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prose ul, .prose ol {
    margin: 1.25rem 0;
    padding-left: 1.5rem;
}

.prose li {
    margin: 0.5rem 0;
}

.prose blockquote {
    border-left: 4px solid #3B82F6;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #F8FAFC;
    padding: 1rem;
    border-radius: 0.5rem;
}

.prose code {
    background-color: #F1F5F9;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.prose pre {
    background-color: #1E293B;
    color: #F1F5F9;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.prose a {
    color: #3B82F6;
    text-decoration: underline;
}

.prose a:hover {
    color: #1D4ED8;
}

/* YouTube embed responsive */
.prose iframe {
    max-width: 100%;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

/* Default size if no width/height specified */
.prose iframe:not([width]):not([height]) {
    width: 100%;
    height: 315px;
}

@media (max-width: 768px) {
    .prose iframe:not([width]):not([height]) {
        height: 200px;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/guides/show.blade.php ENDPATH**/ ?>