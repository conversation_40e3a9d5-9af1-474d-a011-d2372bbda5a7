<?php if($services->count() > 0): ?>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                <!-- Service Image -->
                <div class="relative">
                    <?php if($service->first_image): ?>
                        <img src="<?php echo e($service->first_image); ?>"
                             alt="<?php echo e($service->name); ?>"
                             class="w-full h-40 object-contain">
                    <?php else: ?>
                        <div class="w-full h-40 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                            <i class="fas fa-tools text-white text-3xl"></i>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Service Info -->
                <div class="p-4 flex flex-col flex-1">
                    <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2"><?php echo e($service->name); ?></h3>
                    <p class="text-sm text-gray-500 mb-3"><?php echo e($service->category->name); ?></p>

                    <!-- Bottom section - always at bottom -->
                    <div class="mt-auto">
                        <div class="text-orange-600 font-bold text-xl"><?php echo e($service->formatted_price); ?></div>
                    </div>
                </div>

                <!-- Clickable overlay -->
                <a href="<?php echo e(route('services.show', $service)); ?>" class="absolute inset-0 z-10"></a>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Services Pagination -->
    <?php if($services->hasPages()): ?>
        <div class="mt-8">
            <?php echo e($services->appends(request()->query())->links('pagination.custom')); ?>

        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
            <i class="fas fa-tools"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Chưa có dịch vụ nào</h3>
        <p class="text-gray-500">Các dịch vụ sẽ được cập nhật sớm</p>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/partials/services-grid.blade.php ENDPATH**/ ?>