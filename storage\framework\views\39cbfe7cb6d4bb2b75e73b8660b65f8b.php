

<?php $__env->startSection('title', '<PERSON>h mục'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900"><PERSON>h mục</h1>
            <p class="text-gray-600 mt-1">Quản lý danh mục sản phẩm và dịch vụ</p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo e(route('admin.categories.create')); ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Thêm mới
            </a>
        </div>
    </div>

    <!-- Search -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <form method="GET" class="flex gap-3">
            <div class="flex-1">
                <input type="text" name="search" value="<?php echo e(request('search')); ?>"
                       placeholder="Tìm kiếm theo tên danh mục..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                <i class="fas fa-search"></i>
            </button>
            <?php if(request('search')): ?>
                <a href="<?php echo e(route('admin.categories.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-times"></i>
                </a>
            <?php endif; ?>
        </form>
    </div>

    <!-- Categories Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Danh mục
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Publisher
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Số sản phẩm
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Trạng thái
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Ngày tạo
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Thao tác
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $categories ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($category->id ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <?php if(isset($category->image) && $category->image): ?>
                                            <img class="w-12 h-12 rounded-lg object-cover" src="<?php echo e(storage_url($category->image)); ?>" alt="<?php echo e($category->name ?? 'N/A'); ?>">
                                        <?php else: ?>
                                            <i class="fas fa-gamepad text-gray-400"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($category->name ?? 'N/A'); ?></div>
                                        <div class="text-sm text-gray-500 truncate max-w-xs" title="<?php echo e($category->slug ?? 'N/A'); ?>">
                                            <?php echo e($category->slug ?? 'N/A'); ?>

                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($category->publisher ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo e($category->products_count ?? 0); ?> sản phẩm
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php if(($category->status ?? 'active') == 'active'): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-eye mr-1"></i>Hiển thị
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-eye-slash mr-1"></i>Ẩn
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e(isset($category->created_at) ? $category->created_at->format('d/m/Y') : 'N/A'); ?>

                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.categories.edit', $category->id ?? 0)); ?>"
                                       class="text-blue-600 hover:text-blue-900 transition-colors" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="confirmDelete(<?php echo e($category->id ?? 0); ?>)"
                                            class="text-red-600 hover:text-red-900 transition-colors" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-4 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-list text-4xl mb-4 block"></i>
                                    <p class="text-lg">Chưa có danh mục nào</p>
                                    <p class="text-sm">Hãy thêm danh mục đầu tiên</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if(isset($categories) && $categories->hasPages()): ?>
            <div class="px-4 py-3 border-t border-gray-200">
                <?php echo e($categories->withQueryString()->links('pagination.custom')); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmDelete(categoryId) {
    if (confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
        // Create form to delete
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo e(url(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin/categories')); ?>/${categoryId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>