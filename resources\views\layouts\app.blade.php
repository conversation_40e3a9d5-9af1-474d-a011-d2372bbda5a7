<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Favicon với nhiều kích thước -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ asset('favicon-96x96.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ asset('favicon.ico') }}">

    <title>@yield('title', 'AccReroll | Shop tài khoản Yu-gi-oh! Master Duel, FGO, Genshin Impact')</title>
    <meta name="description" content="@yield('description', 'Shop bán tài khoản reroll các tựa game gacha')">

    <!-- Facebook Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="@yield('title', 'AccReroll | Shop tài khoản Yu-gi-oh! Master Duel, FGO, Genshin Impact')">
    <meta property="og:description" content="@yield('description', 'Shop bán tài khoản reroll các tựa game gacha')">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="AccReroll">

    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    @stack('styles')
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- Facebook SDK -->
    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v18.0"></script>

    <!-- Header -->
    @include('layouts.header')

    <!-- Main Content -->
    <main class="flex-1">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('layouts.footer')

    <!-- Site Announcement Modal -->
    @include('components.site-announcement-modal')

    <!-- Search Autocomplete -->
    <script src="{{ asset('js/search-autocomplete.js') }}"></script>

    @stack('scripts')
</body>
</html>
