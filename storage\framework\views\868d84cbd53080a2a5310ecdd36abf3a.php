<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Dashboard</h1>
            <p class="text-gray-600 mt-1">Tổng quan hoạt động hệ thống</p>
        </div>
    </div>

    <?php if(session('error')): ?>
        <div class="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Lỗi:</strong> <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Revenue -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Tổng doanh thu</p>
                    <p class="text-3xl font-bold text-purple-600 mt-2"><?php echo e(format_money($revenueStats['topup_revenue'])); ?></p>
                    <p class="text-gray-500 text-xs mt-1">Tiền nạp vào hệ thống</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-coins text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Users -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Tổng Users</p>
                    <p class="text-3xl font-bold text-blue-600 mt-2"><?php echo e(format_money($stats['total_users'], false)); ?></p>
                    <p class="text-gray-500 text-xs mt-1">Tài khoản đã đăng ký</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Products -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Sản phẩm</p>
                    <p class="text-3xl font-bold text-green-600 mt-2"><?php echo e(format_money($stats['total_products'], false)); ?></p>
                    <p class="text-gray-500 text-xs mt-1"><?php echo e($stats['products_in_stock']); ?> còn hàng</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-box text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Orders -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Đơn hàng</p>
                    <p class="text-3xl font-bold text-orange-600 mt-2"><?php echo e(format_money($stats['total_orders'], false)); ?></p>
                    <p class="text-gray-500 text-xs mt-1">Tổng đơn đã tạo</p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>





    <!-- Today Stats & Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Today Performance -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Hoạt động hôm nay</h3>
                <div class="text-sm text-gray-500"><?php echo e(now()->format('d/m/Y')); ?></div>
            </div>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Doanh thu:</span>
                    <span class="font-semibold text-green-600"><?php echo e(format_money($timeStats['today']['topup_revenue'])); ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Đơn hàng mới:</span>
                    <span class="font-semibold text-blue-600"><?php echo e($timeStats['today']['orders'] ?? 0); ?> đơn</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Users mới:</span>
                    <span class="font-semibold text-purple-600"><?php echo e($timeStats['today']['new_users'] ?? 0); ?> người</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">Giao dịch:</span>
                    <span class="font-semibold text-orange-600"><?php echo e($timeStats['today']['transactions'] ?? 0); ?> lượt</span>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Hoạt động gần đây</h3>
                <a href="<?php echo e(route('admin.transaction-logs.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm">Xem tất cả</a>
            </div>
            <div class="space-y-4 max-h-64 overflow-y-auto">
                <?php $__currentLoopData = $recentActivities['transactions']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center
                        <?php echo e($transaction->amount >= 0 ? 'bg-green-100' : 'bg-red-100'); ?>">
                        <i class="fas <?php echo e($transaction->amount >= 0 ? 'fa-arrow-up text-green-600' : 'fa-arrow-down text-red-600'); ?> text-xs"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                            <?php echo e($transaction->user->username ?? 'N/A'); ?>

                        </p>
                        <p class="text-xs text-gray-500">
                            <?php
                                $typeLabels = [
                                    'topup_atm' => 'nạp ATM',
                                    'topup_card' => 'nạp thẻ',
                                    'purchase' => 'mua hàng',
                                    'admin_add' => 'admin cộng tiền',
                                    'admin_subtract' => 'admin trừ tiền',
                                    'refund' => 'hoàn tiền'
                                ];
                            ?>
                            <?php echo e($typeLabels[$transaction->type] ?? $transaction->type); ?> • <?php echo e($transaction->created_at->diffForHumans()); ?>

                        </p>
                    </div>
                    <div class="text-sm font-medium <?php echo e($transaction->amount >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                        <?php echo e($transaction->formatted_amount); ?>

                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Top Products & Users -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Products -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Top sản phẩm (30 ngày)</h3>
                <a href="<?php echo e(route('admin.products.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm">Xem tất cả</a>
            </div>
            <div class="space-y-4">
                <?php $__empty_1 = true; $__currentLoopData = $topProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span class="text-blue-600 font-bold text-sm">#<?php echo e($index + 1); ?></span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($product->name); ?></p>
                        <p class="text-xs text-gray-500"><?php echo e($product->category->name ?? 'N/A'); ?></p>
                    </div>
                    <div class="text-sm font-medium text-gray-900">
                        <?php echo e($product->order_items_count); ?> lần mua
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="text-center py-4 text-gray-500">
                    <i class="fas fa-box text-2xl text-gray-300 mb-2"></i>
                    <p class="text-sm">Chưa có đơn hàng nào</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Top Users -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Top khách hàng (30 ngày)</h3>
                <a href="<?php echo e(route('admin.users.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm">Xem tất cả</a>
            </div>
            <div class="space-y-4">
                <?php $__empty_1 = true; $__currentLoopData = $topUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                        <span class="text-green-600 font-bold text-sm">#<?php echo e($index + 1); ?></span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($user->username); ?></p>
                        <p class="text-xs text-gray-500"><?php echo e($user->orders_count); ?> đơn hàng</p>
                    </div>
                    <div class="text-sm font-medium text-green-600">
                        <?php echo e(format_money($user->total_spent ?? 0)); ?>

                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="text-center py-4 text-gray-500">
                    <i class="fas fa-users text-2xl text-gray-300 mb-2"></i>
                    <p class="text-sm">Chưa có khách hàng nào</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/dashboard.blade.php ENDPATH**/ ?>