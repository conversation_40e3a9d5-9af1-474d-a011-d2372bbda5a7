@extends('layouts.app')

@section('title', 'AccReroll | Shop tài khoản Yu-gi-oh! Master <PERSON>, <PERSON><PERSON><PERSON>, Genshin Impact')

@section('content')


<!-- Game Categories Section -->
<section class="py-8">
    <div class="max-w-7xl mx-auto px-4">
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-0">Danh <PERSON>ch Game</h2>
        </div>

        @if($categories->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($categories as $category)
                    <div>
                        <div class="relative bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:border-blue-600">
                            <div class="flex items-center p-4 md:p-4">
                                <!-- Game Info -->
                                <div class="flex-grow">
                                    <h5 class="text-base md:text-base font-semibold text-gray-800 mb-1">{{ $category->name }}</h5>
                                    <p class="text-xs md:text-sm text-gray-600 mb-0">{{ $category->publisher ?: 'Game Publisher' }}</p>
                                </div>

                                <!-- Game Image -->
                                <div class="ml-3 flex-shrink-0">
                                    @if($category->image)
                                        <img src="{{ storage_url($category->image) }}"
                                             alt="{{ $category->name }}"
                                             class="w-16 h-16 md:w-14 md:h-14 object-contain border border-gray-100 rounded">
                                    @else
                                        <div class="w-16 h-16 md:w-14 md:h-14 bg-gradient-to-br from-indigo-500 to-purple-600 text-white text-2xl md:text-xl flex items-center justify-center border border-gray-100 rounded">
                                            <i class="fas fa-gamepad"></i>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Clickable overlay -->
                            <a href="{{ route('category.show', $category->slug) }}"
                               class="absolute inset-0 z-10"></a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-20">
                <i class="fas fa-gamepad text-gray-400 text-5xl mb-3"></i>
                <h4 class="text-gray-500 text-xl">Chưa có danh mục game nào</h4>
                <p class="text-gray-500">Vui lòng quay lại sau!</p>
            </div>
        @endif
    </div>
</section>

<!-- Products Section -->
<section class="py-8">
    <div class="max-w-7xl mx-auto px-4">
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">Sản Phẩm Mới</h2>
        </div>

        <div id="products-container">
            @include('partials.products-grid', ['latestProducts' => $latestProducts])
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-8 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4">
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">Dịch Vụ</h2>
        </div>

        <div id="services-container">
            @include('partials.services-grid', ['services' => $services])
        </div>
    </div>
</section>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle pagination clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('nav[aria-label="Pagination Navigation"] a')) {
            e.preventDefault();
            const link = e.target.closest('a');
            const url = link.href;

            // Determine if this is services or products pagination
            const isServicesPage = url.includes('services_page=');
            const container = isServicesPage ?
                document.getElementById('services-container') :
                document.getElementById('products-container');

            const targetSection = isServicesPage ?
                document.querySelector('section.bg-gray-50') :
                document.querySelector('section.py-8:not(.bg-gray-50)');

            // Show loading state
            container.style.opacity = '0.5';

            // Fetch new content
            fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Update the container with new content
                container.innerHTML = html;
                container.style.opacity = '1';

                // Scroll to appropriate section
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                container.style.opacity = '1';
            });
        }
    });
});
</script>
@endpush
