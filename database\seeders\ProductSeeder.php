<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mlCategory = Category::where('slug', 'mobile-legends')->first();
        $ffCategory = Category::where('slug', 'free-fire')->first();
        $pubgCategory = Category::where('slug', 'pubg-mobile')->first();

        $products = [
            [
                'category_id' => $mlCategory->id,
                'name' => 'Tài khoản Mobile Legends VIP',
                'slug' => 'tai-khoan-mobile-legends-vip',
                'description' => 'Tài khoản Mobile Legends có nhiều skin và hero',
                'price' => 50000,
                'images' => [],
                'note' => 'Tài khoản đã bind Gmail, có thể đổi mật khẩu',
                'status' => 'active',
                'featured' => true,
            ],
            [
                'category_id' => $ffCategory->id,
                'name' => 'Tài khoản Free Fire Random',
                'slug' => 'tai-khoan-free-fire-random',
                'description' => 'Tài khoản Free Fire ngẫu nhiên với kim cương',
                'price' => 30000,
                'images' => [],
                'note' => 'Random skin và nhân vật',
                'status' => 'active',
                'featured' => false,
            ],
            [
                'category_id' => $pubgCategory->id,
                'name' => 'Tài khoản PUBG Mobile UC',
                'slug' => 'tai-khoan-pubg-mobile-uc',
                'description' => 'Tài khoản PUBG Mobile có UC và outfit',
                'price' => 80000,
                'images' => [],
                'note' => 'Có sẵn UC và một số outfit đẹp',
                'status' => 'active',
                'featured' => true,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
