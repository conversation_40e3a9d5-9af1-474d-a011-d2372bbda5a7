<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $fillable = [
        'name',
        'publisher',
        'slug',
        'image',
        'status',
        'sort_order',
    ];

    // Relationships
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function services()
    {
        return $this->hasMany(Service::class);
    }

    public function vouchers()
    {
        return $this->hasMany(Voucher::class);
    }
}
