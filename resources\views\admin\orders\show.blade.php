@extends('layouts.admin')

@section('title', 'Đơn hàng #' . $order->order_number)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.orders.index') }}" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900">Đơn hàng {{ $order->order_number }}</h1>
        </div>
    </div>

    <!-- Order Summary -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Order Info -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin đơn hàng</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">ID đơn hàng:</span>
                        <span class="text-sm font-medium text-gray-900">#{{ $order->id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Mã đơn hàng:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $order->order_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Ngày đặt:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $order->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Trạng thái:</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check mr-1"></i>Hoàn thành
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Voucher sử dụng:</span>
                        @if($order->usedVoucher && $order->usedVoucher->voucher)
                            <span class="text-sm text-green-600">
                                <i class="fas fa-ticket-alt mr-1"></i>{{ $order->usedVoucher->voucher->code }}
                                <span class="text-xs text-gray-500">
                                    @if($order->usedVoucher->voucher->discount_type === 'fixed')
                                        (Giảm {{ format_money($order->usedVoucher->voucher->discount_value, false) }}đ)
                                    @else
                                        (Giảm {{ $order->usedVoucher->voucher->discount_value }}%)
                                    @endif
                                </span>
                            </span>
                        @else
                            <span class="text-sm text-gray-400">Không sử dụng</span>
                        @endif
                    </div>
                    <div class="flex justify-between pt-2 border-t border-gray-200">
                        <span class="text-base font-medium text-gray-900">Tổng tiền:</span>
                        <span class="text-lg font-bold text-red-600">{{ format_money($order->total_amount) }}</span>
                    </div>
                </div>
            </div>

            <!-- Customer Info -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin khách hàng</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Username:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $order->user->username }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Email:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $order->user->email }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Số dư hiện tại:</span>
                        <span class="text-sm font-medium text-green-600">{{ format_money($order->user->balance) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Sản phẩm đã mua</h3>

        <div class="space-y-4">
            @php
                $groupedItems = $order->orderItems->groupBy('product_id');
            @endphp

            @foreach($groupedItems as $productId => $items)
            @php
                $firstItem = $items->first();
                $totalQuantity = $items->sum('quantity');
                $totalPrice = $items->sum(function($item) { return $item->price * $item->quantity; });
            @endphp
            <div class="border border-gray-200 rounded-lg p-4">
                <!-- Product Info -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                            @if($firstItem->product->main_image)
                                <img src="{{ storage_url($firstItem->product->main_image) }}"
                                     alt="{{ $firstItem->product->name }}"
                                     class="w-full h-full object-cover rounded-lg">
                            @else
                                <i class="fas fa-gamepad text-gray-400 text-xl"></i>
                            @endif
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ $firstItem->product->name }}</h4>
                            <p class="text-sm text-gray-500">{{ $firstItem->product->category->name }}</p>
                            <p class="text-sm text-gray-500">Số lượng: {{ $totalQuantity }} tài khoản</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-red-600">{{ format_money($totalPrice) }}</div>
                        <div class="text-sm text-gray-500">{{ format_money($firstItem->price) }} x{{ $totalQuantity }}</div>
                    </div>
                </div>

                <!-- Account Details -->
                @if($totalQuantity > 0)
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between mb-3">
                        <h5 class="font-medium text-gray-900">Tài khoản game đã giao</h5>
                        <span class="text-sm text-gray-500">{{ $totalQuantity }} tài khoản</span>
                    </div>

                    <div class="space-y-3">
                        @foreach($items as $index => $item)
                        @if($item->productAccount)
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div class="mb-3">
                                <span class="text-sm font-medium text-gray-700">
                                    Tài khoản #{{ $index + 1 }}
                                </span>
                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>Đã giao
                                </span>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div class="bg-white p-3 rounded border">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-xs text-gray-500">Username</div>
                                        <button onclick="copyText('{{ $item->productAccount->username }}', 'username-{{ $index + 1 }}')"
                                                class="text-blue-600 hover:text-blue-800 text-xs" id="username-{{ $index + 1 }}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <div class="text-sm font-mono text-gray-900 select-all">{{ $item->productAccount->username }}</div>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-xs text-gray-500">Password</div>
                                        <button onclick="copyText('{{ $item->productAccount->password }}', 'password-{{ $index + 1 }}')"
                                                class="text-blue-600 hover:text-blue-800 text-xs" id="password-{{ $index + 1 }}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <div class="text-sm font-mono text-gray-900 select-all">{{ $item->productAccount->password }}</div>
                                </div>
                            </div>
                        </div>
                        @else
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                            <div class="mb-3">
                                <span class="text-sm font-medium text-gray-700">
                                    Tài khoản #{{ $index + 1 }}
                                </span>
                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    <i class="fas fa-clock mr-1"></i>Đang chuẩn bị
                                </span>
                            </div>
                            <div class="text-sm text-orange-700">
                                <i class="fas fa-info-circle mr-2"></i>
                                Tài khoản này đang được chuẩn bị. Admin sẽ thêm tài khoản sớm nhất có thể.
                            </div>
                        </div>
                        @endif
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            @endforeach
        </div>
    </div>
</div>

@push('scripts')
<script>


function copyText(text, buttonId) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('text-blue-600');
        button.classList.add('text-green-600');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-green-600');
            button.classList.add('text-blue-600');
        }, 1500);
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show success message
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('text-blue-600');
        button.classList.add('text-green-600');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-green-600');
            button.classList.add('text-blue-600');
        }, 1500);
    });
}
</script>
@endpush

@endsection
