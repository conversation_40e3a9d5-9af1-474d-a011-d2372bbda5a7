<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductAccount;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Hiển thị danh sách products
     */
    public function index(Request $request)
    {
        $query = Product::with('category')
            ->withCount(['productAccounts', 'productAccounts as available_accounts_count' => function ($query) {
                $query->where('status', 'available');
            }]); // Chỉ đếm số lượng thay vì load tất cả accounts

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            if ($request->stock_status === 'in_stock') {
                $query->whereHas('productAccounts', function($q) {
                    $q->where('status', 'available');
                });
            } elseif ($request->stock_status === 'preorder') {
                $query->where('allow_preorder', true)
                     ->whereDoesntHave('productAccounts', function($q) {
                         $q->where('status', 'available');
                     });
            } elseif ($request->stock_status === 'out_of_stock') {
                $query->where('allow_preorder', false)
                     ->whereDoesntHave('productAccounts', function($q) {
                         $q->where('status', 'available');
                     });
            }
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by stock
        if ($request->filled('stock')) {
            if ($request->stock === 'in_stock') {
                $query->whereHas('productAccounts', function($q) {
                    $q->where('status', 'available');
                });
            } elseif ($request->stock === 'out_of_stock') {
                $query->whereDoesntHave('productAccounts', function($q) {
                    $q->where('status', 'available');
                });
            }
        }

        $products = $query->orderBy('id', 'desc')->paginate(15);

        // Stats
        $totalProducts = Product::count();
        $inStockProducts = Product::whereHas('productAccounts', function($q) {
            $q->where('status', 'available');
        })->count();
        $outOfStockProducts = Product::whereDoesntHave('productAccounts', function($q) {
            $q->where('status', 'available');
        })->count();
        $hiddenProducts = Product::where('status', 'inactive')->count();

        // Get categories for filter
        $categories = Category::where('status', 'active')->orderBy('name')->get();

        return view('admin.products.index', compact('products', 'categories', 'totalProducts', 'inStockProducts', 'outOfStockProducts', 'hiddenProducts'));
    }

    /**
     * Hiển thị form tạo product mới
     */
    public function create()
    {
        $categories = Category::where('status', 'active')->orderBy('name')->get();
        return view('admin.products.create', compact('categories'));
    }

    /**
     * Lưu product mới
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'note' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
            'featured' => 'boolean',
            'allow_preorder' => 'boolean',
        ]);

        $slug = $request->slug ?: Str::slug($request->name);

        // Xử lý upload images
        $images = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('products', $imageName, 'public');
                $images[] = $imagePath;
            }
        }

        Product::create([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'price' => $request->price,
            'images' => $images,
            'note' => $request->note,
            'status' => $request->status ?? 'active',
            'featured' => $request->has('featured'),
            'allow_preorder' => $request->has('allow_preorder'),
        ]);

        return redirect()->route('admin.products.index')
            ->with('success', 'Tạo sản phẩm thành công!');
    }

    /**
     * Hiển thị chi tiết product
     */
    public function show(Product $product)
    {
        $product->load('category', 'productAccounts');

        return view('admin.products.show', compact('product'));
    }

    /**
     * Hiển thị form chỉnh sửa product
     */
    public function edit(Product $product)
    {
        $categories = Category::where('status', 'active')->orderBy('name')->get();
        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Cập nhật product
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:products,slug,' . $product->id,
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'note' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
            'featured' => 'boolean',
            'allow_preorder' => 'boolean',
        ]);

        $slug = $request->slug ?: Str::slug($request->name);

        // Xử lý upload images mới
        $images = $product->images ?? []; // Giữ lại images cũ

        if ($request->hasFile('images')) {
            // Xóa ảnh cũ nếu có upload ảnh mới
            if (!empty($product->images)) {
                foreach ($product->images_array as $oldImage) {
                    if (Storage::disk('public')->exists($oldImage)) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }
            }

            // Upload ảnh mới
            $images = [];
            foreach ($request->file('images') as $image) {
                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('products', $imageName, 'public');
                $images[] = $imagePath;
            }
        }

        $product->update([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'price' => $request->price,
            'images' => $images,
            'note' => $request->note,
            'status' => $request->status ?? 'active',
            'featured' => $request->has('featured'),
            'allow_preorder' => $request->has('allow_preorder'),
        ]);

        return redirect()->route('admin.products.index')
            ->with('success', 'Cập nhật sản phẩm thành công!');
    }

    /**
     * Xóa product
     */
    public function destroy(Product $product)
    {
        // Check if product has orders
        if ($product->orderItems()->count() > 0) {
            return redirect()->route('admin.products.index')
                ->with('error', 'Không thể xóa sản phẩm đã có đơn hàng!');
        }

        // Delete images from storage
        if (!empty($product->images)) {
            foreach ($product->images_array as $image) {
                if (Storage::disk('public')->exists($image)) {
                    Storage::disk('public')->delete($image);
                }
            }
        }

        // Delete related product accounts
        $product->productAccounts()->delete();

        $product->delete();

        return redirect()->route('admin.products.index')
            ->with('success', 'Xóa sản phẩm thành công!');
    }

    /**
     * Toggle trạng thái active/inactive
     */
    public function toggleStatus(Product $product)
    {
        $product->update([
            'is_active' => !$product->is_active
        ]);

        $status = $product->is_active ? 'kích hoạt' : 'vô hiệu hóa';

        return redirect()->back()
            ->with('success', "Đã {$status} sản phẩm thành công!");
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'selected_products' => 'required|array',
            'selected_products.*' => 'exists:products,id',
        ]);

        $products = Product::whereIn('id', $request->selected_products);

        switch ($request->action) {
            case 'activate':
                $products->update(['is_active' => true]);
                $message = 'Đã kích hoạt các sản phẩm được chọn!';
                break;
            case 'deactivate':
                $products->update(['is_active' => false]);
                $message = 'Đã vô hiệu hóa các sản phẩm được chọn!';
                break;
            case 'delete':
                // Check if any product has orders
                $hasOrders = $products->whereHas('orderItems')->exists();
                if ($hasOrders) {
                    return redirect()->back()
                        ->with('error', 'Không thể xóa sản phẩm đã có đơn hàng!');
                }

                // Delete images from storage
                $productsToDelete = $products->get();
                foreach ($productsToDelete as $product) {
                    if (!empty($product->images)) {
                        foreach ($product->images_array as $image) {
                            if (Storage::disk('public')->exists($image)) {
                                Storage::disk('public')->delete($image);
                            }
                        }
                    }
                }

                $products->delete();
                $message = 'Đã xóa các sản phẩm được chọn!';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Quản lý tài khoản của sản phẩm
     */
    public function accounts(Request $request, Product $product)
    {
        $query = $product->productAccounts();

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('username', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $accounts = $query->latest()->paginate(15);

        // Stats
        $totalAccounts = $product->productAccounts()->count();
        $availableAccounts = $product->productAccounts()->where('status', 'available')->count();
        $soldAccounts = $product->productAccounts()->where('status', 'sold')->count();
        $soldPercentage = $totalAccounts > 0 ? round(($soldAccounts / $totalAccounts) * 100) : 0;

        return view('admin.products.accounts', compact('product', 'accounts', 'totalAccounts', 'availableAccounts', 'soldAccounts', 'soldPercentage'));
    }

    /**
     * Thêm tài khoản mới cho sản phẩm
     */
    public function storeAccount(Request $request, Product $product)
    {
        $request->validate([
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
        ]);

        // Tạo tài khoản mới
        $account = $product->productAccounts()->create([
            'username' => $request->username,
            'password' => $request->password,
            'status' => 'available',
        ]);

        // Auto-fulfill preorder nếu có
        $pendingPreorder = \App\Models\OrderItem::where('product_id', $product->id)
            ->whereNull('product_account_id')
            ->oldest()
            ->first();

        if ($pendingPreorder) {
            // Gán tài khoản cho preorder
            $pendingPreorder->update(['product_account_id' => $account->id]);

            // Cập nhật trạng thái tài khoản
            $account->update([
                'status' => 'sold',
                'sold_at' => now()
            ]);

            $message = 'Tài khoản đã được thêm và tự động gán cho đơn đặt hàng #' . $pendingPreorder->order->order_number;
        } else {
            $message = 'Tài khoản đã được thêm thành công!';
        }

        return redirect()
            ->route('admin.products.accounts', $product)
            ->with('success', $message);
    }

    /**
     * Cập nhật tài khoản
     */
    public function updateAccount(Request $request, Product $product, ProductAccount $account)
    {
        $request->validate([
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
        ]);

        $account->update([
            'username' => $request->username,
            'password' => $request->password,
        ]);

        return redirect()
            ->route('admin.products.accounts', $product)
            ->with('success', 'Tài khoản đã được cập nhật thành công!');
    }

    /**
     * Xóa tài khoản
     */
    public function destroyAccount(Product $product, ProductAccount $account)
    {
        $account->delete();

        return redirect()
            ->route('admin.products.accounts', $product)
            ->with('success', 'Tài khoản đã được xóa thành công!');
    }

    /**
     * Import tài khoản hàng loạt
     */
    public function bulkImportAccounts(Request $request, Product $product)
    {
        $request->validate([
            'accounts_data' => 'required|string',
        ]);

        $lines = explode("\n", trim($request->accounts_data));
        $imported = 0;
        $fulfilled = 0;
        $errors = [];
        $createdAccounts = [];

        foreach ($lines as $lineNum => $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $parts = explode('|', $line, 2);
            if (count($parts) !== 2) {
                $errors[] = "Dòng " . ($lineNum + 1) . ": Định dạng không đúng (cần username|password)";
                continue;
            }

            $username = trim($parts[0]);
            $password = trim($parts[1]);

            if (empty($username) || empty($password)) {
                $errors[] = "Dòng " . ($lineNum + 1) . ": Username hoặc password không được để trống";
                continue;
            }

            // Check if username already exists for this product
            if ($product->productAccounts()->where('username', $username)->exists()) {
                $errors[] = "Dòng " . ($lineNum + 1) . ": Username '{$username}' đã tồn tại";
                continue;
            }

            $account = $product->productAccounts()->create([
                'username' => $username,
                'password' => $password,
                'status' => 'available',
            ]);

            $createdAccounts[] = $account;
            $imported++;
        }

        // Auto-fulfill preorders với các tài khoản đã tạo
        foreach ($createdAccounts as $account) {
            $pendingPreorder = \App\Models\OrderItem::where('product_id', $product->id)
                ->whereNull('product_account_id')
                ->oldest()
                ->first();

            if ($pendingPreorder) {
                // Gán tài khoản cho preorder
                $pendingPreorder->update(['product_account_id' => $account->id]);

                // Cập nhật trạng thái tài khoản
                $account->update([
                    'status' => 'sold',
                    'sold_at' => now()
                ]);

                $fulfilled++;
            }
        }

        // Tạo message thông minh
        $message = "Đã import thành công {$imported} tài khoản.";
        if ($fulfilled > 0) {
            $message .= " Tự động gán {$fulfilled} tài khoản cho đơn đặt hàng.";
        }
        if (!empty($errors)) {
            $message .= " Có " . count($errors) . " lỗi:\n" . implode("\n", $errors);
        }

        return redirect()
            ->route('admin.products.accounts', $product)
            ->with($imported > 0 ? 'success' : 'error', $message);
    }

    /**
     * Bulk action cho tài khoản
     */
    public function bulkActionAccounts(Request $request, Product $product)
    {
        $request->validate([
            'action' => 'required|in:delete',
            'account_ids' => 'required|json',
        ]);

        $accountIds = json_decode($request->account_ids);
        $accounts = $product->productAccounts()->whereIn('id', $accountIds);

        switch ($request->action) {
            case 'delete':
                $accounts->delete();
                $message = 'Đã xóa ' . count($accountIds) . ' tài khoản!';
                break;
        }

        return redirect()
            ->route('admin.products.accounts', $product)
            ->with('success', $message);
    }

    /**
     * API search categories for autocomplete
     */
    public function searchCategories(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $categories = Category::where('status', 'active')
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('publisher', 'like', "%{$query}%");
            })
            ->select('id', 'name', 'publisher')
            ->limit(10)
            ->get();

        return response()->json($categories->map(function($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'publisher' => $category->publisher,
                'display' => $category->name . ' (' . $category->publisher . ')'
            ];
        }));
    }
}
