@if($services->count() > 0)
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        @foreach($services as $service)
            <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                <!-- Service Image -->
                <div class="relative">
                    @if($service->first_image)
                        <img src="{{ $service->first_image }}"
                             alt="{{ $service->name }}"
                             class="w-full h-40 object-contain">
                    @else
                        <div class="w-full h-40 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                            <i class="fas fa-tools text-white text-3xl"></i>
                        </div>
                    @endif
                </div>

                <!-- Service Info -->
                <div class="p-4 flex flex-col flex-1">
                    <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2">{{ $service->name }}</h3>
                    <p class="text-sm text-gray-500 mb-3">{{ $service->category->name }}</p>

                    <!-- Bottom section - always at bottom -->
                    <div class="mt-auto">
                        <div class="text-orange-600 font-bold text-xl">{{ $service->formatted_price }}</div>
                    </div>
                </div>

                <!-- Clickable overlay -->
                <a href="{{ route('services.show', $service) }}" class="absolute inset-0 z-10"></a>
            </div>
        @endforeach
    </div>

    <!-- Services Pagination -->
    @if($services->hasPages())
        <div class="mt-8">
            {{ $services->appends(request()->query())->links('pagination.custom') }}
        </div>
    @endif
@else
    <div class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
            <i class="fas fa-tools"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Chưa có dịch vụ nào</h3>
        <p class="text-gray-500">Các dịch vụ sẽ được cập nhật sớm</p>
    </div>
@endif
