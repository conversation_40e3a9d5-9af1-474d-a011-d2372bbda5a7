<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserVoucher extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'voucher_id',
        'granted_at',
        'used_at',
        'order_id',
        'service_order_id',
        'granted_reason',
        'reference_order_id',
        'expires_at',
    ];

    protected $casts = [
        'granted_at' => 'datetime',
        'used_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function voucher()
    {
        return $this->belongsTo(Voucher::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function serviceOrder()
    {
        return $this->belongsTo(ServiceOrder::class);
    }

    public function referenceOrder()
    {
        return $this->belongsTo(Order::class, 'reference_order_id');
    }

    // Scopes
    public function scopeUsed($query)
    {
        return $query->whereNotNull('used_at');
    }

    public function scopeUnused($query)
    {
        return $query->whereNull('used_at');
    }

    public function scopeGrantedManually($query)
    {
        return $query->where('granted_reason', 'manual');
    }

    public function scopeGrantedFromPurchase($query)
    {
        return $query->where('granted_reason', 'product_purchase');
    }

    // Helper methods
    public function isUsed()
    {
        return $this->used_at !== null;
    }

    public function isExpired()
    {
        // Kiểm tra user voucher expiry trước (không cần query)
        if ($this->expires_at && $this->expires_at < now()) {
            return true;
        }

        // Kiểm tra voucher expiry - đảm bảo relationship đã được load
        if (!$this->relationLoaded('voucher')) {
            $this->load('voucher');
        }

        if ($this->voucher->isExpired()) {
            return true;
        }

        return false;
    }

    public function canBeUsed()
    {
        // Kiểm tra các điều kiện không cần query trước
        if ($this->isUsed()) {
            return false;
        }

        if ($this->isExpired()) {
            return false;
        }

        // Đảm bảo voucher relationship đã được load trước khi truy cập
        if (!$this->relationLoaded('voucher')) {
            $this->load('voucher');
        }

        return $this->voucher->is_active;
    }

    public function markAsUsed($orderId = null, $serviceOrderId = null)
    {
        $this->update([
            'used_at' => now(),
            'order_id' => $orderId,
            'service_order_id' => $serviceOrderId,
        ]);

        // Invalidate cache khi voucher được sử dụng
        $this->clearVoucherCache();
    }

    /**
     * Clear cache liên quan đến voucher này
     * Giữ lại method này để backward compatibility nhưng không làm gì
     * vì đã chuyển sang sử dụng direct database queries
     */
    public function clearVoucherCache()
    {
        // No longer needed since we use direct database queries
        // but keeping for backward compatibility
    }
}
