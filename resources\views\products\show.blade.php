@extends('layouts.app')

@section('title', $product->name . ' - AccReroll')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-6 lg:mb-8" aria-label="Breadcrumb">
        <ol class="flex flex-wrap items-center gap-1 md:gap-2">
            <li class="flex items-center">
                <a href="{{ route('home') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 px-2 py-1 rounded">
                    <i class="fas fa-home mr-1 md:mr-2"></i>
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
            <li class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                <a href="{{ route('category.show', $product->category->slug) }}" class="text-sm font-medium text-gray-700 hover:text-blue-600 px-2 py-1 rounded">
                    <span class="hidden sm:inline">{{ $product->category->name }}</span>
                    <span class="sm:hidden">{{ Str::limit($product->category->name, 10) }}</span>
                </a>
            </li>
            <li aria-current="page" class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                <span class="text-sm font-medium text-gray-500 px-2 py-1 rounded max-w-[120px] sm:max-w-[200px] lg:max-w-none truncate" title="{{ $product->name }}">
                    {{ $product->name }}
                </span>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        <!-- Product Images -->
        <div class="space-y-3 lg:space-y-4">
            <!-- Main Image -->
            <div class="aspect-[5/3] bg-gray-100 rounded-lg overflow-hidden">
                @if($product->main_image)
                    <img id="main-image" src="{{ storage_url($product->main_image) }}" alt="{{ $product->name }}"
                         class="w-full h-full object-contain">
                @else
                    <div class="w-full h-full flex items-center justify-center">
                        <i class="fas fa-image text-gray-400 text-4xl lg:text-6xl"></i>
                    </div>
                @endif
            </div>

            <!-- Thumbnail Images -->
            @if($product->hasGuideImages())
            <div class="relative">
                <div class="flex overflow-x-auto gap-2 lg:gap-3 pb-2 scrollbar-hide" id="thumbnail-container">
                    @foreach($product->images_array as $index => $image)
                    <button onclick="changeMainImage('{{ storage_url($image) }}')"
                            class="flex-shrink-0 w-25 h-25 bg-gray-100 rounded-lg overflow-hidden border hover:border-blue-500 transition-colors {{ $index === 0 ? 'border-blue-500' : 'border-gray-200' }}">
                        <img src="{{ storage_url($image) }}" alt="Ảnh {{ $index + 1 }}"
                             class="w-full h-full object-contain">
                    </button>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Product Info -->
        <div class="space-y-4 lg:space-y-6">
            <!-- Product Title & Category -->
            <div>
                <div class="flex items-center gap-2 mb-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ $product->category->name }}
                    </span>
                    @if($product->featured)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-star mr-1"></i>Nổi bật
                    </span>
                    @endif
                </div>
                <h1 class="text-2xl lg:text-3xl font-bold text-gray-900">{{ $product->name }}</h1>
            </div>

            <!-- Price -->
            <div class="border-t border-b border-gray-200 py-3 lg:py-4">
                <div class="flex items-center justify-between">
                    <span class="text-2xl lg:text-3xl font-bold text-red-600">{{ format_money($product->price) }}</span>
                    <div class="text-right">
                        @if($product->availableAccounts->count() > 0)
                            <div class="text-base lg:text-lg font-semibold text-green-600">
                                Số lượng: {{ $product->availableAccounts->count() }}
                            </div>
                        @elseif($product->allow_preorder)
                            <div class="text-base lg:text-lg font-semibold" style="color: #0ea5e9;">
                                Còn hàng (nhận mã đơn)
                            </div>
                        @else
                            <div class="text-base lg:text-lg font-semibold text-red-600">
                                Hết hàng
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- THÔNG BÁO ĐẶT HÀNG -->
            @if($product->availableAccounts->count() == 0 && $product->allow_preorder)
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
                <div class="flex items-start">
                    <div class="ml-3">
                        <div class="mt-2 text-sm text-red-700">
                            <p><strong>Bạn sẽ nhận được 1 mã đơn hàng sau khi mua thành công</strong>, vui lòng lưu lại mã và liên hệ qua fanpage Facebook để nhận tài khoản.</p>
                            <p class="mt-1">Shop sẽ kiểm tra số lượng, xác nhận đơn hàng và tiến hành giao tài khoản trong thời gian sớm nhất.</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Stock Status -->
            <div class="flex items-center gap-2 lg:hidden">
                @if($product->availableAccounts->count() > 0)
                    <div class="flex items-center text-green-600">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="font-medium">Còn hàng</span>
                    </div>
                @elseif($product->allow_preorder)
                    <div class="font-medium" style="color: #0ea5e9;">
                        Còn hàng (nhận mã đơn)
                    </div>
                @else
                    <div class="font-medium text-red-600">
                        Hết hàng
                    </div>
                @endif
            </div>

            <!-- Desktop Purchase Section -->
            <div class="hidden lg:block w-full">
                @if($product->availableAccounts->count() > 0 || $product->allow_preorder)
                    <!-- Quantity & Purchase Row -->
                    <div class="flex items-center gap-4">
                        <!-- Quantity Selector -->
                        <div class="flex items-center">
                            <label for="quantity-desktop" class="text-sm font-medium text-gray-700 mr-3">Số lượng:</label>
                            <div class="flex items-center border border-gray-300 rounded-lg">
                                <button type="button" onclick="decreaseQuantity('desktop')"
                                        class="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-l-lg transition-colors">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="quantity-desktop" name="quantity" value="1" min="1"
                                       max="{{ $product->availableAccounts->count() > 0 ? $product->availableAccounts->count() : 10 }}"
                                       class="w-16 px-3 py-2 text-center border-0 focus:ring-0 focus:outline-none">
                                <button type="button" onclick="increaseQuantity('desktop')"
                                        class="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-r-lg transition-colors">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Purchase Button -->
                        @if($product->availableAccounts->count() > 0)
                            @auth
                                <button onclick="showPurchaseModal('desktop')" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    Mua ngay
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold py-3 px-6 rounded-lg text-center shadow-md">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Đăng nhập
                                </a>
                            @endauth
                        @else
                            @auth
                                <button onclick="showPurchaseModal('desktop')" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold py-3 px-6 rounded-lg shadow-md">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    Mua hàng
                                </button>
                            @else
                                <a href="{{ route('login') }}" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold py-3 px-6 rounded-lg text-center shadow-md">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Đăng nhập để mua hàng
                                </a>
                            @endauth
                        @endif
                    </div>
                @else
                    <!-- Out of stock - Contact button -->
                    <a href="https://www.facebook.com/profile.php?id=61578528443379" target="_blank" class="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold py-3 px-6 rounded-lg text-center shadow-md block">
                        <i class="fab fa-facebook-messenger mr-2"></i>
                        Liên hệ
                    </a>
                @endif
            </div>

            <!-- Product Description -->
            @if($product->description)
            <div class="mt-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-white text-sm"></i>
                        </div>
                        <h3 class="text-xl font-bold text-blue-900">Mô tả sản phẩm</h3>
                    </div>
                    <div class="prose prose-sm max-w-none text-gray-800 leading-relaxed">
                        {!! nl2br(e($product->description)) !!}
                    </div>
                </div>
            </div>
            @endif

            <!-- Product Note -->
            @if($product->note)
            <div class="mt-6">
                <div class="bg-amber-50 border border-amber-200 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                        </div>
                        <h3 class="text-xl font-bold text-amber-900">Lưu ý quan trọng</h3>
                    </div>
                    <div class="prose prose-sm max-w-none text-gray-800 leading-relaxed">
                        {!! nl2br(e($product->note)) !!}
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Related Products -->
    @if($relatedProducts->count() > 0)
    <div class="mt-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Sản phẩm liên quan</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            @foreach($relatedProducts as $relatedProduct)
            <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                <!-- Product Image -->
                <div class="relative">
                    @if($relatedProduct->main_image)
                        <img src="{{ storage_url($relatedProduct->main_image) }}" alt="{{ $relatedProduct->name }}"
                             class="w-full h-40 object-contain">
                    @else
                        <div class="w-full h-40 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-gamepad text-white text-3xl"></i>
                        </div>
                    @endif
                </div>

                <!-- Product Info -->
                <div class="p-4 flex flex-col flex-1">
                    <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2">{{ $relatedProduct->name }}</h3>
                    <p class="text-sm text-gray-500 mb-3">{{ $relatedProduct->category->name }}</p>

                    <!-- Bottom section - always at bottom -->
                    <div class="mt-auto">
                        @if($relatedProduct->availableAccounts->count() > 0)
                            <div class="text-green-600 font-semibold text-sm mb-2">Sẵn Có: {{ $relatedProduct->availableAccounts->count() }}</div>
                        @elseif($relatedProduct->allow_preorder)
                            <div class="font-semibold text-sm mb-2" style="color: #0ea5e9;">Còn hàng (nhận mã đơn)</div>
                        @else
                            <div class="text-red-600 font-semibold text-sm mb-2">Hết hàng</div>
                        @endif
                        <div class="text-orange-600 font-bold text-xl">{{ number_format($relatedProduct->price, 0, ',', '.') }} đ</div>
                    </div>
                </div>

                <!-- Clickable overlay -->
                <a href="{{ route('product.show', $relatedProduct->slug) }}" class="absolute inset-0 z-10"></a>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>

<!-- Mobile Sticky Bottom Bar -->
<div class="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
    @if($product->availableAccounts->count() > 0 || $product->allow_preorder)
        <div class="flex items-center gap-3">
            <!-- Quantity Selector -->
            <div class="flex items-center border border-gray-300 rounded-lg">
                <button type="button" onclick="decreaseQuantity('mobile')"
                        class="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-l-lg transition-colors">
                    <i class="fas fa-minus"></i>
                </button>
                <input type="number" id="quantity-mobile" name="quantity" value="1" min="1"
                       max="{{ $product->availableAccounts->count() > 0 ? $product->availableAccounts->count() : 10 }}"
                       class="w-12 px-2 py-2 text-center border-0 focus:ring-0 focus:outline-none text-sm">
                <button type="button" onclick="increaseQuantity('mobile')"
                        class="px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-r-lg transition-colors">
                    <i class="fas fa-plus"></i>
                </button>
            </div>

            <!-- Purchase Button -->
            @if($product->availableAccounts->count() > 0)
                @auth
                    <button onclick="showPurchaseModal('mobile')" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold py-3 px-4 rounded-lg shadow-md">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Mua ngay
                    </button>
                @else
                    <a href="{{ route('login') }}" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold py-3 px-4 rounded-lg text-center shadow-md">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Đăng nhập
                    </a>
                @endauth
            @else
                @auth
                    <button onclick="showPurchaseModal('mobile')" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Mua hàng
                    </button>
                @else
                    <a href="{{ route('login') }}" class="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold py-3 px-4 rounded-lg text-center shadow-md">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Đăng nhập để mua hàng
                    </a>
                @endauth
            @endif
        </div>
    @else
        <!-- Out of stock - Contact button for mobile -->
        <a href="https://www.facebook.com/profile.php?id=61578528443379" target="_blank" class="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold py-3 px-4 rounded-lg text-center shadow-md block">
            <i class="fab fa-facebook mr-2"></i>
            Liên hệ
        </a>
    @endif
</div>

<!-- Mobile Bottom Padding -->
<div class="lg:hidden h-20"></div>

<style>
.scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
}
</style>

<script>
function changeMainImage(imageSrc) {
    document.getElementById('main-image').src = imageSrc;

    // Update active thumbnail
    const thumbnails = document.querySelectorAll('[onclick^="changeMainImage"]');
    thumbnails.forEach(thumb => {
        thumb.classList.remove('border-blue-500');
        thumb.classList.add('border-gray-200');
    });

    event.target.closest('button').classList.remove('border-gray-200');
    event.target.closest('button').classList.add('border-blue-500');
}

function scrollThumbnails(direction) {
    const container = document.getElementById('thumbnail-container');

    // Calculate scroll amount based on screen size
    const isDesktop = window.innerWidth >= 1024; // lg breakpoint
    const thumbnailSize = isDesktop ? 80 : 64; // lg:w-20 vs w-16
    const gap = isDesktop ? 12 : 8; // lg:gap-3 vs gap-2
    const scrollCount = isDesktop ? 3 : 2; // Scroll 3 on desktop, 2 on mobile
    const scrollAmount = (thumbnailSize + gap) * scrollCount;

    if (direction === 'left') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }

    // Update button visibility
    setTimeout(() => {
        updateScrollButtons();
    }, 300);
}

function updateScrollButtons() {
    const container = document.getElementById('thumbnail-container');
    const leftBtn = document.getElementById('scroll-left');
    const rightBtn = document.getElementById('scroll-right');

    if (!leftBtn || !rightBtn || !container) return;

    // Only show buttons on desktop
    const isDesktop = window.innerWidth >= 1024;
    if (!isDesktop) {
        leftBtn.style.display = 'none';
        rightBtn.style.display = 'none';
        return;
    }

    leftBtn.style.display = 'flex';
    rightBtn.style.display = 'flex';

    // Check if can scroll left
    if (container.scrollLeft <= 0) {
        leftBtn.style.opacity = '0.3';
        leftBtn.style.pointerEvents = 'none';
    } else {
        leftBtn.style.opacity = '0.5';
        leftBtn.style.pointerEvents = 'auto';
    }

    // Check if can scroll right
    if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
        rightBtn.style.opacity = '0.3';
        rightBtn.style.pointerEvents = 'none';
    } else {
        rightBtn.style.opacity = '0.5';
        rightBtn.style.pointerEvents = 'auto';
    }
}

// Initialize scroll buttons on page load
document.addEventListener('DOMContentLoaded', function() {
    updateScrollButtons();

    // Update buttons on scroll and resize
    const container = document.getElementById('thumbnail-container');
    if (container) {
        container.addEventListener('scroll', updateScrollButtons);
        window.addEventListener('resize', updateScrollButtons);
    }
});

// Quantity functions
function increaseQuantity(device = 'desktop') {
    const quantityInput = document.getElementById(`quantity-${device}`);
    const currentValue = parseInt(quantityInput.value);
    const maxValue = parseInt(quantityInput.getAttribute('max'));

    if (currentValue < maxValue) {
        quantityInput.value = currentValue + 1;
        syncQuantity(device, currentValue + 1);
    }
}

function decreaseQuantity(device = 'desktop') {
    const quantityInput = document.getElementById(`quantity-${device}`);
    const currentValue = parseInt(quantityInput.value);
    const minValue = parseInt(quantityInput.getAttribute('min'));

    if (currentValue > minValue) {
        quantityInput.value = currentValue - 1;
        syncQuantity(device, currentValue - 1);
    }
}

// Sync quantity between desktop and mobile
function syncQuantity(sourceDevice, value) {
    const targetDevice = sourceDevice === 'desktop' ? 'mobile' : 'desktop';
    const targetInput = document.getElementById(`quantity-${targetDevice}`);
    if (targetInput) {
        targetInput.value = value;
    }
}



// Validate quantity input for both desktop and mobile
document.addEventListener('DOMContentLoaded', function() {
    ['desktop', 'mobile'].forEach(device => {
        const quantityInput = document.getElementById(`quantity-${device}`);
        if (quantityInput) {
            quantityInput.addEventListener('input', function() {
                const value = parseInt(this.value);
                const min = parseInt(this.getAttribute('min'));
                const max = parseInt(this.getAttribute('max'));

                if (value < min) {
                    this.value = min;
                } else if (value > max) {
                    this.value = max;
                }

                // Sync with other device
                syncQuantity(device, this.value);
            });
        }
    });
});

// Purchase Modal Functions
let currentDevice = 'desktop';

function showPurchaseModal(device = 'desktop') {
    currentDevice = device;
    const quantity = document.getElementById(`quantity-${device}`).value;

    // Update modal content
    document.getElementById('modalQuantity').textContent = quantity;

    // Reset voucher when opening modal
    removeProductVoucher();

    // Hide vouchers list when opening modal
    document.getElementById('productAvailableVouchers').classList.add('hidden');

    // Update total
    updateProductTotal();

    // Show modal
    document.getElementById('purchaseModal').classList.remove('hidden');
}

function closePurchaseModal() {
    document.getElementById('purchaseModal').classList.add('hidden');
}

function confirmPurchase() {
    const quantity = document.getElementById(`quantity-${currentDevice}`).value;
    const productId = {{ $product->id }};
    const voucherCode = document.getElementById('product_voucher_code').value.trim();

    // Disable confirm button
    const confirmBtn = document.getElementById('confirmPurchaseBtn');
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang xử lý...';

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("purchase") }}';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // Add product_id
    const productIdInput = document.createElement('input');
    productIdInput.type = 'hidden';
    productIdInput.name = 'product_id';
    productIdInput.value = productId;
    form.appendChild(productIdInput);

    // Add quantity
    const quantityInput = document.createElement('input');
    quantityInput.type = 'hidden';
    quantityInput.name = 'quantity';
    quantityInput.value = quantity;
    form.appendChild(quantityInput);

    // Add voucher code if present
    if (voucherCode) {
        const voucherInput = document.createElement('input');
        voucherInput.type = 'hidden';
        voucherInput.name = 'voucher_code';
        voucherInput.value = voucherCode;
        form.appendChild(voucherInput);
    }

    // Submit form
    document.body.appendChild(form);
    form.submit();
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('purchaseModal');
    if (e.target === modal) {
        closePurchaseModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePurchaseModal();
    }
});

// Product voucher functionality
let productVoucher = null;
let productAvailableVouchers = [];
let productOriginalTotal = {{ $product->price }};
const productUserBalance = {{ Auth::user()->balance ?? 0 }};

// Toggle vouchers list visibility
function toggleProductVouchersList() {
    const vouchersContainer = document.getElementById('productAvailableVouchers');

    if (vouchersContainer.classList.contains('hidden')) {
        loadProductAvailableVouchers();
    } else {
        vouchersContainer.classList.add('hidden');
    }
}

// Load available vouchers
function loadProductAvailableVouchers() {
    const quantity = document.getElementById(`quantity-${currentDevice}`).value;
    const totalAmount = productOriginalTotal * quantity;

    fetch(`/vouchers/available?order_type=product&category_id={{ $product->category_id }}&total_amount=${totalAmount}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            productAvailableVouchers = data.vouchers;
            displayProductVouchersList();
        })
        .catch(error => {
            console.error('Error loading product vouchers:', error);
            alert('Có lỗi xảy ra khi tải voucher: ' + error.message);
        });
}

// Display vouchers list
function displayProductVouchersList() {
    const vouchersContainer = document.getElementById('productAvailableVouchers');
    const vouchersList = document.getElementById('productVouchersList');

    if (productAvailableVouchers.length === 0) {
        vouchersList.innerHTML = '<div class="text-gray-500 text-xs p-2">Không có voucher khả dụng cho sản phẩm này</div>';
    } else {
        vouchersList.innerHTML = productAvailableVouchers.map(voucher => {
            return `
                <div class="voucher-item p-2 border border-gray-200 rounded cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                     onclick="selectProductVoucher('${voucher.code}')">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="font-medium text-sm text-gray-900">${voucher.name}</div>
                            <div class="text-xs text-gray-600">Mã: ${voucher.code}</div>
                            <div class="text-xs text-green-600">
                                ${voucher.discount_type === 'fixed' ?
                                    `Giảm ${new Intl.NumberFormat('de-DE').format(voucher.discount_value)}đ` :
                                    `Giảm ${voucher.discount_value}%`
                                }
                            </div>
                            ${voucher.min_order_amount ? `<div class="text-xs text-gray-500">Đơn tối thiểu: ${new Intl.NumberFormat('de-DE').format(voucher.min_order_amount)}đ</div>` : ''}
                        </div>
                        <button type="button" class="text-blue-600 hover:text-blue-800 text-xs">
                            Chọn
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    vouchersContainer.classList.remove('hidden');
}

// Toggle apply button state based on voucher input
function toggleProductVoucherApplyButton() {
    const voucherInput = document.getElementById('product_voucher_code');
    const applyBtn = document.getElementById('applyProductVoucherBtn');
    const hasValue = voucherInput.value.trim().length > 0;

    if (hasValue) {
        applyBtn.disabled = false;
        applyBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm';
    } else {
        applyBtn.disabled = true;
        applyBtn.className = 'px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200 text-sm';
    }
}

// Select voucher from list
function selectProductVoucher(code) {
    document.getElementById('product_voucher_code').value = code;
    document.getElementById('productAvailableVouchers').classList.add('hidden');
    toggleProductVoucherApplyButton(); // Update button state
    validateProductVoucher();
}

function validateProductVoucher() {
    const voucherCode = document.getElementById('product_voucher_code').value.trim();
    const messageDiv = document.getElementById('product-voucher-message');
    const applyBtn = document.getElementById('applyProductVoucherBtn');

    // Prevent validation if button is disabled
    if (applyBtn.disabled) {
        return;
    }

    if (!voucherCode) {
        removeProductVoucher();
        return;
    }

    const quantity = document.getElementById(`quantity-${currentDevice}`).value;
    const totalAmount = productOriginalTotal * quantity;

    fetch('/vouchers/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            voucher_code: voucherCode,
            order_type: 'product',
            category_id: '{{ $product->category_id }}',
            total_amount: totalAmount,
            item_ids: [{{ $product->id }}]
        })
    })
    .then(response => {
        if (response.status === 429) {
            // Handle rate limiting
            return response.json().then(data => {
                throw new Error(data.message || 'Quá nhiều yêu cầu. Vui lòng thử lại sau.');
            });
        }
        return response.json();
    })
    .then(data => {
        messageDiv.classList.remove('hidden');

        if (data.valid) {
            messageDiv.className = 'mt-2 text-sm text-green-600';
            messageDiv.textContent = 'Voucher hợp lệ!';

            productVoucher = data;
            showProductAppliedVoucher(data);
            updateProductTotal(); // Sẽ kiểm tra lại số dư với giá mới
        } else {
            messageDiv.className = 'mt-2 text-sm text-red-600';
            messageDiv.textContent = data.message;
            // Không reset input, chỉ clear voucher data và applied voucher display
            productVoucher = null;
            document.getElementById('product-applied-voucher').classList.add('hidden');
            updateProductTotal();
        }
    })
    .catch(error => {
        messageDiv.classList.remove('hidden');
        messageDiv.className = 'mt-2 text-sm text-red-600';

        // Handle different error types
        if (error.message.includes('Quá nhiều yêu cầu')) {
            messageDiv.textContent = error.message;
        } else {
            messageDiv.textContent = 'Có lỗi xảy ra khi kiểm tra voucher';
        }

        // Không reset input khi có lỗi network
        productVoucher = null;
        document.getElementById('product-applied-voucher').classList.add('hidden');
        updateProductTotal();
    });
}

function showProductAppliedVoucher(voucherData) {
    const appliedDiv = document.getElementById('product-applied-voucher');
    const infoSpan = document.getElementById('product-voucher-info');

    const discountText = voucherData.voucher.discount_type === 'fixed' ?
        `-${new Intl.NumberFormat('de-DE').format(voucherData.voucher.discount_value)}đ` :
        `-${voucherData.voucher.discount_value}%`;

    infoSpan.textContent = `${voucherData.voucher.name} (${discountText})`;
    appliedDiv.classList.remove('hidden');
}

function removeProductVoucher() {
    productVoucher = null;
    document.getElementById('product_voucher_code').value = '';
    document.getElementById('product-voucher-message').classList.add('hidden');
    document.getElementById('product-applied-voucher').classList.add('hidden');
    toggleProductVoucherApplyButton(); // Update button state
    updateProductTotal(); // Sẽ kiểm tra lại số dư với giá gốc
}

function updateProductTotal() {
    const quantity = document.getElementById(`quantity-${currentDevice}`).value;
    let total = productOriginalTotal * quantity;

    if (productVoucher) {
        total -= productVoucher.discount_amount;
    }

    document.getElementById('modalTotal').textContent = new Intl.NumberFormat('de-DE').format(Math.max(0, total)) + 'đ';

    // Kiểm tra số dư
    checkProductBalance(total);
}

function checkProductBalance(totalAmount) {
    const balanceWarning = document.getElementById('productBalanceWarning');
    const balanceWarningText = document.getElementById('productBalanceWarningText');
    const confirmButton = document.getElementById('confirmPurchaseBtn');

    if (productUserBalance < totalAmount) {
        const needAmount = totalAmount - productUserBalance;
        balanceWarningText.textContent = `Số tiền cần nạp thêm: ${new Intl.NumberFormat('de-DE').format(needAmount)} đ`;
        balanceWarning.classList.remove('hidden');

        // Disable button và đổi màu
        confirmButton.disabled = true;
        confirmButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        confirmButton.classList.add('bg-gray-400', 'cursor-not-allowed');
        confirmButton.innerHTML = '<i class="fas fa-shopping-cart mr-2"></i>Mua hàng';
    } else {
        balanceWarning.classList.add('hidden');

        // Enable button và khôi phục màu
        confirmButton.disabled = false;
        confirmButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
        confirmButton.classList.add('bg-blue-600', 'hover:bg-blue-700');
        confirmButton.innerHTML = '<i class="fas fa-shopping-cart mr-2"></i>Xác nhận';
    }
}
</script>

<!-- Purchase Confirmation Modal -->
<div id="purchaseModal" class="fixed inset-0 hidden z-50 flex items-center justify-center p-4" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6">
            <h3 class="text-lg font-semibold text-gray-900">Xác nhận mua hàng</h3>
            <button onclick="closePurchaseModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Product Image -->
                <div class="flex-shrink-0">
                    @if($product->main_image)
                        <img src="{{ storage_url($product->main_image) }}" alt="{{ $product->name }}"
                             class="w-16 h-16 object-cover rounded-lg">
                    @else
                        <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                            <i class="fas fa-image text-gray-400"></i>
                        </div>
                    @endif
                </div>

                <!-- Product Info -->
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-900 mb-1">{{ $product->name }}</h4>
                    <p class="text-sm text-gray-600 mb-2">{{ $product->category->name }}</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-red-600">{{ format_money($product->price) }}</span>
                        <span class="text-sm text-gray-500">Số lượng: <span id="modalQuantity">1</span></span>
                    </div>
                </div>
            </div>

            <!-- Voucher Section -->
            <div class="mt-4 pt-4 border-t border-gray-200">
                <label class="text-sm font-medium text-gray-700 mb-3 block">Mã giảm giá</label>

                <!-- Available Vouchers List -->
                <div id="productAvailableVouchers" class="hidden mb-3">
                    <div class="text-xs text-gray-600 mb-2">Voucher khả dụng:</div>
                    <div id="productVouchersList" class="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2 bg-gray-50"></div>
                </div>

                <!-- Voucher Input -->
                <div class="flex space-x-2">
                    <div class="flex-1">
                        <input type="text"
                               id="product_voucher_code"
                               name="voucher_code"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                               placeholder="Nhập mã voucher hoặc chọn từ danh sách"
                               oninput="toggleProductVoucherApplyButton()">
                    </div>
                    <button type="button"
                            onclick="toggleProductVouchersList()"
                            class="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                            title="Hiển thị voucher khả dụng">
                        <i class="fas fa-list"></i>
                    </button>
                    <button type="button"
                            id="applyProductVoucherBtn"
                            onclick="validateProductVoucher()"
                            disabled
                            class="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200 text-sm">
                        Áp dụng
                    </button>
                </div>

                <!-- Voucher validation message -->
                <div id="product-voucher-message" class="mt-2 text-sm hidden"></div>

                <!-- Applied voucher display -->
                <div id="product-applied-voucher" class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md text-sm text-green-800 hidden">
                    <div class="flex items-center justify-between">
                        <span id="product-voucher-info"></span>
                        <button type="button" onclick="removeProductVoucher()" class="text-green-600 hover:text-green-800">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Total -->
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <span class="text-base font-medium text-gray-900">Tổng thanh toán:</span>
                    <span class="text-xl font-bold text-red-600" id="modalTotal">{{ format_money($product->price) }}</span>
                </div>
            </div>

            <!-- Balance Check Warning -->
            <div id="productBalanceWarning" class="hidden mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-red-800 font-medium text-sm">Số dư không đủ</p>
                        <p class="text-red-600 text-sm" id="productBalanceWarningText">Số tiền cần nạp thêm: 0 đ</p>
                    </div>
                    <a href="{{ route('topup.index') }}"
                       class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                        Nạp tiền
                    </a>
                </div>
            </div>

        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end space-x-3 p-6">
            <button onclick="closePurchaseModal()" class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                Hủy
            </button>
            <button onclick="confirmPurchase()" id="confirmPurchaseBtn" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                <i class="fas fa-shopping-cart mr-2"></i>
                Xác nhận
            </button>
        </div>
    </div>
</div>
@endsection
