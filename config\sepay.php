<?php

return [
    /*
    |--------------------------------------------------------------------------
    | SePay Configuration
    |--------------------------------------------------------------------------
    |
    | C<PERSON>u hình cho tích hợp SePay ATM topup qua webhook
    |
    */

    // API Key từ SePay dashboard
    'api_key' => env('SEPAY_API_KEY'),

    // Pattern cho nội dung chuyển khoản (ví dụ: guidang123 cho user ID 123)
    'pattern' => env('SEPAY_PATTERN', 'guidang'),

    // Thông tin ngân hàng nhận tiền
    'bank_info' => [
        'bank_name' => env('SEPAY_BANK_NAME', 'MBBank'),
        'account_number' => env('SEPAY_ACCOUNT_NUMBER', '*********'),
        'account_name' => env('SEPAY_ACCOUNT_NAME', 'DOAN QUOC DANG'),
    ],

    // QR Code settings
    'qr_settings' => [
        'template' => env('SEPAY_QR_TEMPLATE', 'compact'),
        'base_url' => env('SEPAY_QR_BASE_URL', 'https://qr.sepay.vn/img'),
    ],

    // Webhook settings
    'webhook' => [
        'enabled' => env('SEPAY_WEBHOOK_ENABLED', true),
        'verify_signature' => env('SEPAY_WEBHOOK_VERIFY_SIGNATURE', false),
    ],

    // Logging
    'logging' => [
        'enabled' => env('SEPAY_LOGGING_ENABLED', true),
        'channel' => env('SEPAY_LOG_CHANNEL', 'daily'),
    ],
];
