<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vouchers', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['manual', 'auto_product_purchase'])->default('manual');
            $table->enum('discount_type', ['percentage', 'fixed'])->default('percentage');
            $table->decimal('discount_value', 15, 2); // For both percentage and fixed amount
            $table->enum('applicable_to', ['products', 'services', 'both'])->default('both');
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('cascade');
            $table->decimal('min_order_amount', 15, 2)->nullable();
            $table->integer('max_uses_per_user')->default(1);
            $table->integer('total_uses_limit')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by_admin_id')->nullable()->constrained('users')->onDelete('set null');
            $table->json('auto_config')->nullable(); // config for auto vouchers
            $table->json('trigger_conditions')->nullable();
            $table->integer('user_expires_days')->nullable();
            $table->boolean('specific_items_only')->default(false);
            $table->softDeletes(); // deleted_at column
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vouchers');
    }
};
