<?php $__env->startSection('title', 'Đơn hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Đơn hàng</h1>
            <p class="text-gray-600 mt-1">Quản lý đơn hàng mua sản phẩm của khách hàng</p>
        </div>
    </div>

    <!-- Search & Filters -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <form method="GET" class="flex flex-wrap items-center gap-3">
            <div class="flex-1 min-w-0 sm:min-w-64">
                <input type="text" name="search" value="<?php echo e(request('search')); ?>"
                       placeholder="T<PERSON><PERSON> kiếm theo mã đơn hàng, tên kh<PERSON>ch hàng..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Order Type Filter -->
            <div class="w-40">
                <select name="order_type" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Tất cả đơn hàng</option>
                    <option value="normal" <?php echo e(request('order_type') == 'normal' ? 'selected' : ''); ?>>Đơn thường</option>
                    <option value="preorder" <?php echo e(request('order_type') == 'preorder' ? 'selected' : ''); ?>>Đơn đặt hàng</option>
                </select>
            </div>

            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                <i class="fas fa-search"></i>
            </button>
            <?php if(request()->hasAny(['search', 'order_type'])): ?>
                <a href="<?php echo e(route('admin.orders.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-times"></i>
                </a>
            <?php endif; ?>
        </form>
    </div>

    <!-- Orders Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Mã đơn hàng
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Khách hàng
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Sản phẩm
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Tổng tiền
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Loại
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Trạng thái
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Ngày tạo
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Thao tác
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $orders ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900"><?php echo e($order->id ?? 'N/A'); ?></div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900"><?php echo e($order->order_number ?? 'N/A'); ?></div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900"><?php echo e($order->user->username ?? 'N/A'); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($order->user->email ?? 'N/A'); ?></div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <?php echo e($order->orderItems->count() ?? 0); ?> sản phẩm
                            </div>
                            <?php if($order->orderItems && $order->orderItems->count() > 0): ?>
                                <div class="text-sm text-gray-500">
                                    <?php echo e($order->orderItems->first()->product->name ?? 'N/A'); ?>

                                    <?php if($order->orderItems->count() > 1): ?>
                                        và <?php echo e($order->orderItems->count() - 1); ?> sản phẩm khác
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900"><?php echo e(format_money($order->total_amount ?? 0)); ?></div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <?php
                                $hasPreorderItems = $order->orderItems && $order->orderItems->contains(function($item) {
                                    return is_null($item->product_account_id);
                                });
                            ?>
                            <?php if($hasPreorderItems): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    <i class="fas fa-clock mr-1"></i>Đặt hàng
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-shopping-cart mr-1"></i>Thường
                                </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>Hoàn thành
                            </span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo e(isset($order->created_at) ? $order->created_at->format('d/m/Y H:i') : 'N/A'); ?>

                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <a href="<?php echo e(route('admin.orders.show', $order->id ?? 0)); ?>"
                                   class="text-blue-600 hover:text-blue-900 transition-colors" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="9" class="px-4 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-shopping-cart text-4xl mb-4 block"></i>
                                    <p class="text-lg">Chưa có đơn hàng nào</p>
                                    <p class="text-sm">Các đơn hàng sẽ hiển thị ở đây khi có khách hàng đặt mua</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if(isset($orders) && $orders->hasPages()): ?>
            <div class="px-4 py-3 border-t border-gray-200">
                <?php echo e($orders->withQueryString()->links('pagination.custom')); ?>

            </div>
        <?php endif; ?>
    </div>
</div>



<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/admin/orders/index.blade.php ENDPATH**/ ?>