<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Models\Service;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        // L<PERSON>y tất cả categories active
        $categories = Category::where('status', 'active')
            ->withCount('products')
            ->orderBy('sort_order')
            ->get();

        // Lấy sản phẩm mới nhất với phân trang
        $latestProducts = Product::where('status', 'active')
            ->with('category')
            ->withCount('availableAccounts') // Chỉ đếm số lượng thay vì load tất cả accounts
            ->latest()
            ->paginate(10, ['*'], 'products_page');

        // Lấy dịch vụ active với phân trang
        $services = Service::where('status', 'active')
            ->with('category')
            ->latest()
            ->paginate(10, ['*'], 'services_page');

        // N<PERSON><PERSON> là AJAX request, tr<PERSON> về phần tương <PERSON>ng
        if ($request->ajax()) {
            if ($request->has('services_page')) {
                return view('partials.services-grid', compact('services'));
            }
            return view('partials.products-grid', compact('latestProducts'));
        }

        return view('home', compact('categories', 'latestProducts', 'services'));
    }
}
