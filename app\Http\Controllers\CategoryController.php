<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Models\Service;

class CategoryController extends Controller
{
    /**
     * Hi<PERSON>n thị sản phẩm theo danh mục
     */
    public function show($slug, Request $request)
    {
        // Tìm category theo slug
        $category = Category::where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();

        // Lấy sản phẩm thuộc category này
        $query = Product::where('category_id', $category->id)
            ->where('status', 'active')
            ->with('category')
            ->withCount('availableAccounts'); // Chỉ đếm số lượng accounts

        // Sắp xếp theo giá
        $sort = $request->get('sort', 'price_asc');
        switch ($sort) {
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            default:
                $query->orderBy('price', 'asc');
                break;
        }

        $products = $query->paginate(12)->appends($request->query());

        // Lấy dịch vụ thuộc category này
        $services = Service::where('category_id', $category->id)
            ->where('status', 'active')
            ->with('category')
            ->orderBy('price', 'asc')
            ->get();

        return view('category.show', compact('category', 'products', 'services'));
    }
}
