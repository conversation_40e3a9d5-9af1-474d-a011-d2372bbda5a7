<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Services\TurnstileService;

class TurnstileRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Skip validation if Turnstile is not fully configured
        if (!env('TURNSTILE_SECRET_KEY') || !env('TURNSTILE_SITE_KEY')) {
            return;
        }

        if (!TurnstileService::verify($value)) {
            $fail('Vui lòng xác thực CAPTCHA.');
        }
    }
}
