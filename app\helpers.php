<?php

if (!function_exists('storage_url')) {
    /**
     * Generate URL for storage files
     *
     * @param string|null $path
     * @return string|null
     */
    function storage_url($path)
    {
        if (empty($path)) {
            return null;
        }

        // Clean the path
        $path = trim($path, '/');

        // Extract folder and filename from path
        $pathParts = explode('/', $path);
        if (count($pathParts) >= 2) {
            $folder = $pathParts[0];
            $filename = implode('/', array_slice($pathParts, 1));

            try {
                return route('storage.file', ['folder' => $folder, 'filename' => $filename]);
            } catch (Exception $e) {
                // Fallback to null if route generation fails
                return null;
            }
        }

        return null;
    }
}

if (!function_exists('format_money')) {
    /**
     * Format số tiền theo chuẩn Việt Nam (dấu chấm)
     *
     * @param float|int $amount
     * @param bool $showCurrency
     * @return string
     */
    function format_money($amount, $showCurrency = true)
    {
        $formatted = number_format($amount, 0, '.', '.');
        return $showCurrency ? $formatted . 'đ' : $formatted;
    }
}

if (!function_exists('format_money_js')) {
    /**
     * Format số tiền cho JavaScript (dấu chấm)
     * Trả về config cho Intl.NumberFormat
     *
     * @return string
     */
    function format_money_js()
    {
        return "new Intl.NumberFormat('de-DE')"; // German locale uses dot as thousand separator
    }
}
