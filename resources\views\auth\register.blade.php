@extends('layouts.app')

@section('title', 'Đ<PERSON>ng ký - AccReroll')

@section('content')
<div class="flex items-center justify-center py-8 sm:py-12 px-4 sm:px-6 lg:px-8 min-h-[calc(100vh-120px)]">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-blue-600">
                    Đ<PERSON>ng ký
                </h2>
            </div>

            <!-- Form -->
            <form method="POST" action="{{ route('register') }}" class="space-y-6">
                @csrf

                <!-- Username -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-blue-600"></i>Tên đăng nhập
                    </label>
                    <input
                        type="text"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 @error('username') border-red-500 ring-2 ring-red-200 @enderror"
                        id="username"
                        name="username"
                        value="{{ old('username') }}"
                        required
                        autofocus
                        placeholder="Nhập username (6-20 ký tự)"
                    >
                    @error('username')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Display Name -->
                <div>
                    <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-id-card mr-2 text-blue-600"></i>Tên hiển thị
                    </label>
                    <input
                        type="text"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 @error('display_name') border-red-500 ring-2 ring-red-200 @enderror"
                        id="display_name"
                        name="display_name"
                        value="{{ old('display_name') }}"
                        placeholder="Nhập tên hiển thị (3-20 ký tự)"
                    >
                    @error('display_name')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-blue-600"></i>Email
                    </label>
                    <input
                        type="email"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 @error('email') border-red-500 ring-2 ring-red-200 @enderror"
                        id="email"
                        name="email"
                        value="{{ old('email') }}"
                        required
                        placeholder="Nhập email của bạn"
                    >
                    @error('email')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-blue-600"></i>Mật khẩu
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 @error('password') border-red-500 ring-2 ring-red-200 @enderror"
                            id="password"
                            name="password"
                            required
                            placeholder="Nhập mật khẩu (8-30 ký tự)"
                        >
                        <button type="button" onclick="togglePassword('password')" class="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-500 hover:text-gray-700 transition-colors">
                            <i class="fas fa-eye" id="toggleIcon1"></i>
                        </button>
                    </div>
                    @error('password')
                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-blue-600"></i>Xác nhận mật khẩu
                    </label>
                    <div class="relative">
                        <input
                            type="password"
                            class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            id="password_confirmation"
                            name="password_confirmation"
                            required
                            placeholder="Nhập lại mật khẩu (8-30 ký tự)"
                        >
                        <button type="button" onclick="togglePassword('password_confirmation')" class="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-500 hover:text-gray-700 transition-colors">
                            <i class="fas fa-eye" id="toggleIcon2"></i>
                        </button>
                    </div>
                </div>

                <!-- Terms -->
                <div class="flex items-start">
                    <input type="checkbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1" id="terms" required>
                    <label class="ml-2 block text-sm text-gray-700" for="terms">
                        Tôi đồng ý với
                        <a href="#" class="text-blue-600 hover:text-blue-700 transition-colors">Điều khoản sử dụng</a>
                        và
                        <a href="#" class="text-blue-600 hover:text-blue-700 transition-colors">Chính sách bảo mật</a>
                    </label>
                </div>

                <!-- CAPTCHA -->
                @if(env('TURNSTILE_SITE_KEY'))
                    <div class="cf-turnstile" data-sitekey="{{ env('TURNSTILE_SITE_KEY') }}" data-theme="light"></div>
                @error('cf-turnstile-response')
                    <p class="mt-2 text-sm text-red-600 text-center">{{ $message }}</p>
                @enderror
                @endif

                <!-- Submit Button -->
                <button type="submit" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>Đăng ký
                </button>
            </form>

            <!-- Links -->
            <div class="text-center mt-6">
                <p class="text-gray-600">
                    Đã có tài khoản?
                    <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                        Đăng nhập ngay
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'toggleIcon1' : 'toggleIcon2');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Validate username format
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const usernameRegex = /^[a-z0-9]+$/;

    if (username && (username.length < 6 || username.length > 20)) {
        this.setCustomValidity('Username phải có từ 6-20 ký tự');
        this.classList.add('is-invalid');
    } else if (username && !usernameRegex.test(username)) {
        this.setCustomValidity('Username chỉ được chứa chữ cái thường (a-z) và số (0-9)');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Validate password confirmation
document.getElementById('password_confirmation').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword && confirmPassword !== '') {
        this.setCustomValidity('Mật khẩu xác nhận không khớp');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});
</script>
@endpush

@if(env('TURNSTILE_SITE_KEY'))
<script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
@endif
@endsection
