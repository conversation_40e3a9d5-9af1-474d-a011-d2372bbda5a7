@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Fix input focus border issue */
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
textarea:focus,
select:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgb(59 130 246 / 0.5) !important; /* blue-500 with opacity */
}

/* Remove browser default focus outline */
input:focus,
textarea:focus,
select:focus,
button:focus {
    outline: none;
}

/* Ensure Tailwind focus styles work properly */
.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}

.focus\:ring-blue-500:focus {
    --tw-ring-color: rgb(59 130 246 / 0.5);
}

.focus\:border-blue-500:focus {
    border-color: rgb(59 130 246) !important;
}
