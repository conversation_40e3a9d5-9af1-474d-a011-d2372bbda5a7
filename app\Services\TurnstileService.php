<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class TurnstileService
{
    /**
     * Verify Turnstile token
     */
    public static function verify($token, $ip = null)
    {
        $secretKey = env('TURNSTILE_SECRET_KEY');
        $siteKey = env('TURNSTILE_SITE_KEY');

        if (!$secretKey || !$siteKey) {
            return false;
        }

        $response = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
            'secret' => $secretKey,
            'response' => $token,
            'remoteip' => $ip ?? request()->ip(),
        ]);

        $result = $response->json();
        
        return $result['success'] ?? false;
    }
}
