<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'description',
        'price',
        'images',
        'note',
        'status',
        'featured',
        'allow_preorder',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'featured' => 'boolean',
            'allow_preorder' => 'boolean',
            'images' => 'array',
        ];
    }

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function productAccounts()
    {
        return $this->hasMany(ProductAccount::class);
    }

    public function availableAccounts()
    {
        return $this->hasMany(ProductAccount::class)->where('status', 'available');
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    // Accessors
    public function getMainImageAttribute()
    {
        $images = $this->images;

        // Ensure images is an array
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return is_array($images) && count($images) > 0 ? $images[0] : null;
    }

    public function getTotalQuantityAttribute()
    {
        return $this->productAccounts()->count();
    }

    public function getAvailableQuantityAttribute()
    {
        return $this->productAccounts()->where('status', 'available')->count();
    }

    public function getSoldQuantityAttribute()
    {
        return $this->productAccounts()->where('status', 'sold')->count();
    }

    public function getGuideImagesAttribute()
    {
        $images = $this->images;

        // Ensure images is an array
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return is_array($images) ? array_slice($images, 1) : [];
    }

    public function hasGuideImages()
    {
        $images = $this->images;

        // Ensure images is an array
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return is_array($images) && count($images) > 1;
    }

    public function getImagesArrayAttribute()
    {
        $images = $this->images;

        // Ensure images is an array
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return is_array($images) ? $images : [];
    }
}
