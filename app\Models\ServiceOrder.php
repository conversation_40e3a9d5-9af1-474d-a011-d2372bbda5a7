<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ServiceOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'service_id',
        'order_number',
        'status',
        'price',
        'content',
        'notes',
        'admin_notes',
        'completed_at'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'completed_at' => 'datetime'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    public function usedVoucher()
    {
        return $this->hasOne(UserVoucher::class, 'service_order_id')->with('voucher');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }



    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return format_money((float)$this->price);
    }

    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'pending' => 'Chờ xử lý',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            default => 'Không xác định'
        };
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'completed' => 'green',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    // Methods

    public function markAsCompleted($adminNotes = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'admin_notes' => $adminNotes
        ]);
    }

    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
    }

    // Helper methods
    public function generateOrderNumber()
    {
        return 'SRV' . date('Ymd') . str_pad($this->id, 4, '0', STR_PAD_LEFT);
    }
}
