document.addEventListener('DOMContentLoaded', function() {
    const searchInputs = document.querySelectorAll('input[name="q"]');
    
    searchInputs.forEach(input => {
        setupAutocomplete(input);
    });
});

function setupAutocomplete(input) {
    let timeout;
    let dropdown;
    let currentFocus = -1;

    // Create dropdown element
    function createDropdown() {
        if (dropdown) return dropdown;
        
        dropdown = document.createElement('div');
        dropdown.className = 'absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b-md shadow-lg z-50 max-h-80 overflow-y-auto hidden';
        dropdown.style.marginTop = '1px';
        
        // Position relative to input's parent
        const parent = input.parentElement;
        parent.style.position = 'relative';
        parent.appendChild(dropdown);
        
        return dropdown;
    }

    // Show dropdown
    function showDropdown() {
        if (!dropdown) return;
        dropdown.classList.remove('hidden');
    }

    // Hide dropdown
    function hideDropdown() {
        if (!dropdown) return;
        dropdown.classList.add('hidden');
        currentFocus = -1;
    }

    // Search categories
    function searchCategories(query) {
        if (query.length < 2) {
            hideDropdown();
            return;
        }

        fetch(`/api/search/categories?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(categories => {
                displayResults(categories);
            })
            .catch(error => {
                console.error('Search error:', error);
                hideDropdown();
            });
    }

    // Display search results
    function displayResults(categories) {
        const dropdown = createDropdown();
        
        if (categories.length === 0) {
            dropdown.innerHTML = `
                <div class="p-4 text-center text-gray-500">
                    <i class="fas fa-search text-2xl mb-2"></i>
                    <p>Không tìm thấy danh mục nào</p>
                </div>
            `;
            showDropdown();
            return;
        }

        const html = categories.map((category, index) => `
            <div class="search-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" 
                 data-url="${category.url}" 
                 data-index="${index}">
                <div class="flex items-center">
                    <div class="mr-3 flex-shrink-0">
                        ${category.image_url
                            ? `<img src="${category.image_url}" alt="${category.name}" class="w-10 h-10 rounded object-cover">`
                            : `<div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center">
                                 <i class="fas fa-gamepad text-white text-sm"></i>
                               </div>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="font-medium text-gray-900 truncate">${category.name}</div>
                        ${category.publisher
                            ? `<div class="text-sm text-gray-500 truncate">${category.publisher}</div>`
                            : ''
                        }
                    </div>
                </div>
            </div>
        `).join('');

        dropdown.innerHTML = html;
        
        // Add click handlers
        dropdown.querySelectorAll('.search-item').forEach(item => {
            item.addEventListener('click', function() {
                window.location.href = this.dataset.url;
            });
        });

        showDropdown();
    }

    // Handle keyboard navigation
    function handleKeyNavigation(e) {
        if (!dropdown || dropdown.classList.contains('hidden')) return;
        
        const items = dropdown.querySelectorAll('.search-item');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            currentFocus = Math.min(currentFocus + 1, items.length - 1);
            updateFocus(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            currentFocus = Math.max(currentFocus - 1, -1);
            updateFocus(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (currentFocus >= 0 && items[currentFocus]) {
                window.location.href = items[currentFocus].dataset.url;
            }
        } else if (e.key === 'Escape') {
            hideDropdown();
            input.blur();
        }
    }

    // Update focus styling
    function updateFocus(items) {
        items.forEach((item, index) => {
            if (index === currentFocus) {
                item.classList.add('bg-blue-50');
            } else {
                item.classList.remove('bg-blue-50');
            }
        });
    }

    // Event listeners
    input.addEventListener('input', function() {
        clearTimeout(timeout);
        const query = this.value.trim();
        
        timeout = setTimeout(() => {
            searchCategories(query);
        }, 300);
    });

    input.addEventListener('keydown', handleKeyNavigation);

    input.addEventListener('focus', function() {
        if (this.value.trim().length >= 2) {
            searchCategories(this.value.trim());
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!input.contains(e.target) && (!dropdown || !dropdown.contains(e.target))) {
            hideDropdown();
        }
    });

    // Hide dropdown when input loses focus (with delay for clicks)
    input.addEventListener('blur', function() {
        setTimeout(() => {
            hideDropdown();
        }, 150);
    });
}
