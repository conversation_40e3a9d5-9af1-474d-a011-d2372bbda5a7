<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BalanceTransaction;
use App\Models\User;

class BalanceTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testUser = User::where('username', 'testuser')->first();
        $demoUser = User::where('username', 'demo')->first();
        $admin = User::where('role', 'admin')->first();

        $transactions = [
            // Test user nạp tiền
            [
                'user_id' => $testUser->id,
                'type' => BalanceTransaction::TYPE_TOPUP_ATM,
                'amount' => 100000,
                'balance_before' => 0,
                'balance_after' => 100000,
                'description' => 'Nạp tiền qua ATM',
                'admin_id' => null,
                'reference_type' => null,
                'reference_id' => null,
                'metadata' => ['bank' => 'Vietcombank', 'transaction_id' => 'VCB123456'],
            ],
            // Test user mua hàng
            [
                'user_id' => $testUser->id,
                'type' => BalanceTransaction::TYPE_PURCHASE,
                'amount' => -50000,
                'balance_before' => 100000,
                'balance_after' => 50000,
                'description' => 'Mua tài khoản Mobile Legends VIP',
                'admin_id' => null,
                'reference_type' => 'App\Models\Order',
                'reference_id' => 1,
                'metadata' => ['order_number' => 'ORD' . date('Ymd') . '0001'],
            ],
            // Demo user nạp tiền
            [
                'user_id' => $demoUser->id,
                'type' => BalanceTransaction::TYPE_TOPUP_CARD,
                'amount' => 50000,
                'balance_before' => 0,
                'balance_after' => 50000,
                'description' => 'Nạp tiền qua thẻ cào',
                'admin_id' => null,
                'reference_type' => null,
                'reference_id' => null,
                'metadata' => ['card_type' => 'Viettel', 'card_value' => 50000],
            ],
            // Admin cộng tiền cho demo user
            [
                'user_id' => $demoUser->id,
                'type' => BalanceTransaction::TYPE_ADMIN_ADD,
                'amount' => 20000,
                'balance_before' => 50000,
                'balance_after' => 70000,
                'description' => 'Admin cộng tiền khuyến mãi',
                'admin_id' => $admin->id,
                'reference_type' => null,
                'reference_id' => null,
                'metadata' => ['reason' => 'Khuyến mãi thành viên mới'],
            ],
        ];

        foreach ($transactions as $transaction) {
            BalanceTransaction::create($transaction);
        }
    }
}
