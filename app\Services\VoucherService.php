<?php

namespace App\Services;

use App\Models\Voucher;
use App\Models\UserVoucher;
use App\Models\User;
use App\Models\Order;

class VoucherService
{
    /**
     * Tạo voucher tự động khi user mua product (Logic mới)
     */
    public function createAutoVoucherForProductPurchase(Order $order)
    {
        // Eager load order items và products để tránh N+1
        $order->load(['orderItems.product:id,category_id']);

        // Lấy user từ order
        $user = User::find($order->user_id);
        if (!$user) {
            return;
        }

        // Lấy tất cả auto voucher có trigger conditions
        $autoVouchers = Voucher::autoTrigger()->active()->get();

        foreach ($autoVouchers as $voucher) {
            $voucherCount = $this->checkTriggerConditionsForProduct($voucher, $order);

            if ($voucherCount > 0) {
                // Cấp voucher theo số lượng (1:1 với quantity)
                for ($i = 0; $i < $voucherCount; $i++) {
                    $this->grantAutoVoucherToUser($user, $voucher, $order->id);
                }
            }
        }
    }

    /**
     * Tạo voucher tự động khi user đặt service
     */
    public function createAutoVoucherForServicePurchase($serviceOrder)
    {
        // Eager load service để tránh N+1
        if (!$serviceOrder->relationLoaded('service')) {
            $serviceOrder->load(['service:id,category_id']);
        }

        // Lấy user từ service order
        $user = User::find($serviceOrder->user_id);
        if (!$user) {
            return;
        }

        // Lấy tất cả auto voucher có trigger conditions
        $autoVouchers = Voucher::autoTrigger()->active()->get();

        foreach ($autoVouchers as $voucher) {
            $voucherCount = $this->checkTriggerConditionsForService($voucher, $serviceOrder);

            if ($voucherCount > 0) {
                // Cấp voucher theo số lượng (service thường là 1)
                for ($i = 0; $i < $voucherCount; $i++) {
                    $this->grantAutoVoucherToUser($user, $voucher, null, $serviceOrder->id);
                }
            }
        }
    }

    /**
     * Kiểm tra trigger conditions của voucher cho product order và trả về số lượng voucher cần cấp
     */
    private function checkTriggerConditionsForProduct(Voucher $voucher, Order $order)
    {
        $conditions = $voucher->trigger_conditions_array;

        if (!$conditions) {
            return 0;
        }

        // Kiểm tra trigger type
        if (!isset($conditions['trigger_type']) || $conditions['trigger_type'] !== 'product_purchase') {
            return 0;
        }

        // Kiểm tra minimum order amount
        if (isset($conditions['min_order_amount']) && $order->total_amount < $conditions['min_order_amount']) {
            return 0;
        }

        // Kiểm tra condition type
        $conditionType = $conditions['condition_type'] ?? 'all';
        $voucherCount = 0;

        if ($conditionType === 'all') {
            // Tất cả đơn hàng đều trigger - tính tổng quantity
            $voucherCount = $order->orderItems->sum('quantity');
        } elseif ($conditionType === 'categories') {
            // Kiểm tra category conditions - cấp voucher theo quantity của sản phẩm matching
            if (isset($conditions['trigger_categories']) && !empty($conditions['trigger_categories'])) {
                $triggerCategoryIds = $conditions['trigger_categories'];

                foreach ($order->orderItems as $orderItem) {
                    if (in_array($orderItem->product->category_id, $triggerCategoryIds)) {
                        $voucherCount += $orderItem->quantity;
                    }
                }
            }
        } elseif ($conditionType === 'specific_items') {
            // Kiểm tra specific products - cấp voucher theo quantity của sản phẩm matching
            $triggerProducts = $conditions['trigger_products'] ?? [];

            if (!empty($triggerProducts)) {
                foreach ($order->orderItems as $orderItem) {
                    if (in_array($orderItem->product->id, $triggerProducts)) {
                        $voucherCount += $orderItem->quantity;
                    }
                }
            }
        }

        return $voucherCount;
    }

    /**
     * Kiểm tra trigger conditions của voucher cho service order và trả về số lượng voucher cần cấp
     */
    private function checkTriggerConditionsForService(Voucher $voucher, $serviceOrder)
    {
        $conditions = $voucher->trigger_conditions_array;

        if (!$conditions) {
            return 0;
        }

        // Kiểm tra trigger type
        if (!isset($conditions['trigger_type']) || $conditions['trigger_type'] !== 'product_purchase') {
            return 0;
        }

        // Kiểm tra minimum order amount
        if (isset($conditions['min_order_amount']) && $serviceOrder->price < $conditions['min_order_amount']) {
            return 0;
        }

        // Kiểm tra condition type
        $conditionType = $conditions['condition_type'] ?? 'all';
        $voucherCount = 0;

        if ($conditionType === 'all') {
            // Tất cả service order đều trigger - thường là 1
            $voucherCount = 1;
        } elseif ($conditionType === 'categories') {
            // Kiểm tra category conditions cho service
            if (isset($conditions['trigger_categories']) && !empty($conditions['trigger_categories'])) {
                $triggerCategoryIds = $conditions['trigger_categories'];

                if (in_array($serviceOrder->service->category_id, $triggerCategoryIds)) {
                    $voucherCount = 1;
                }
            }
        } elseif ($conditionType === 'specific_items') {
            // Kiểm tra specific services
            $triggerServices = $conditions['trigger_services'] ?? [];

            if (!empty($triggerServices) && in_array($serviceOrder->service_id, $triggerServices)) {
                $voucherCount = 1;
            }
        }

        return $voucherCount;
    }

    /**
     * Cấp auto voucher cho user (cho phép multiple vouchers cùng loại)
     */
    private function grantAutoVoucherToUser(User $user, Voucher $voucher, $referenceOrderId = null, $referenceServiceOrderId = null)
    {
        // Kiểm tra tổng số voucher user đã có (cả used và unused)
        $totalVoucherCount = UserVoucher::where('user_id', $user->id)
            ->where('voucher_id', $voucher->id)
            ->count();

        if ($totalVoucherCount >= $voucher->max_uses_per_user) {
            return null; // Đã đạt giới hạn max_uses_per_user
        }

        // Tính expiry date
        $expiresAt = null;
        if ($voucher->user_expires_days) {
            $expiresAt = now()->addDays(intval($voucher->user_expires_days));
        }

        // Xác định granted_reason dựa trên loại order
        $grantedReason = $referenceServiceOrderId ? 'service_purchase' : 'product_purchase';

        return UserVoucher::create([
            'user_id' => $user->id,
            'voucher_id' => $voucher->id,
            'granted_at' => now(),
            'granted_reason' => $grantedReason,
            'reference_order_id' => $referenceOrderId,
            'expires_at' => $expiresAt,
        ]);
    }
    

    
    /**
     * Cấp voucher cho user
     */
    public function grantVoucherToUser(User $user, Voucher $voucher, $reason = 'manual', $referenceOrderId = null)
    {
        // Kiểm tra user đã có voucher này chưa
        $existingUserVoucher = UserVoucher::where('user_id', $user->id)
            ->where('voucher_id', $voucher->id)
            ->first();

        if ($existingUserVoucher) {
            return $existingUserVoucher;
        }

        // Tính expiry date
        $expiresAt = null;
        if ($voucher->user_expires_days) {
            $expiresAt = now()->addDays($voucher->user_expires_days);
        }

        return UserVoucher::create([
            'user_id' => $user->id,
            'voucher_id' => $voucher->id,
            'granted_at' => now(),
            'granted_reason' => $reason,
            'reference_order_id' => $referenceOrderId,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Kiểm tra user có thể sử dụng voucher không (không tạo record)
     * Sử dụng database locks để tránh race condition
     */
    private function canUserUseVoucher(User $user, Voucher $voucher)
    {
        return \DB::transaction(function () use ($user, $voucher) {
            // Lock voucher record để tránh race condition
            $voucher = Voucher::lockForUpdate()->find($voucher->id);

            if (!$voucher || !$voucher->is_active || $voucher->isExpired()) {
                return null;
            }

            // Lấy tất cả user vouchers với lock
            $userVouchers = UserVoucher::lockForUpdate()
                ->where('user_id', $user->id)
                ->where('voucher_id', $voucher->id)
                ->get();

            // Tìm voucher chưa sử dụng và chưa hết hạn
            $unusedVoucher = $userVouchers
                ->whereNull('used_at')
                ->filter(function ($userVoucher) {
                    return !$userVoucher->isExpired();
                })
                ->first();

            if ($unusedVoucher) {
                return $unusedVoucher;
            }

            // Kiểm tra số lần đã sử dụng
            $usedCount = $userVouchers->whereNotNull('used_at')->count();
            if ($usedCount >= $voucher->max_uses_per_user) {
                return null; // Đã hết lượt sử dụng
            }

            // Kiểm tra tổng số lần sử dụng voucher (global limit)
            if ($voucher->total_uses_limit) {
                $totalUsageCount = UserVoucher::where('voucher_id', $voucher->id)
                    ->whereNotNull('used_at')
                    ->count();

                if ($totalUsageCount >= $voucher->total_uses_limit) {
                    return null; // Voucher đã hết lượt sử dụng toàn cục
                }
            }

            // Nếu là manual voucher và chưa có, cho phép sử dụng (sẽ tạo khi apply)
            if ($voucher->type === 'manual' && $userVouchers->isEmpty()) {
                return 'can_use'; // Trả về string để biết là có thể sử dụng
            }

            // Nếu là auto voucher mà chưa có, không cho phép
            if ($voucher->type === 'auto_product_purchase' && $userVouchers->isEmpty()) {
                return null;
            }

            return $unusedVoucher;
        });
    }

    /**
     * Lấy hoặc tạo user voucher khi thực sự apply
     * Manual voucher chỉ được tạo khi user thực sự apply (không tự động cấp)
     */
    private function getOrCreateUserVoucherForApply(User $user, Voucher $voucher)
    {
        // Lấy tất cả user vouchers cho voucher này
        $userVouchers = UserVoucher::with('voucher')
            ->where('user_id', $user->id)
            ->where('voucher_id', $voucher->id)
            ->get();

        // Tìm voucher chưa sử dụng
        $unusedVoucher = $userVouchers->whereNull('used_at')->first();
        if ($unusedVoucher) {
            return $unusedVoucher;
        }

        // Nếu là manual voucher và chưa có, tạo mới khi apply
        // (Manual voucher chỉ tạo khi user thực sự sử dụng, không tự động cấp)
        if ($voucher->type === 'manual' && $userVouchers->isEmpty()) {
            return $this->grantVoucherToUser($user, $voucher, 'manual');
        }

        return null;
    }
    
    /**
     * Validate voucher có thể áp dụng cho đơn hàng không
     */
    public function validateVoucherForOrder($voucherCode, User $user, $orderType, $categoryId = null, $totalAmount = 0, $itemIds = [])
    {
        $voucher = Voucher::where('code', $voucherCode)
            ->with(['applicableItems', 'category']) // Eager load để tránh N+1
            ->active()
            ->notExpired()
            ->first();

        if (!$voucher) {
            return ['valid' => false, 'message' => 'Voucher không tồn tại hoặc đã hết hạn'];
        }

        // Kiểm tra user có thể sử dụng voucher này không (không tạo record ngay)
        $canUse = $this->canUserUseVoucher($user, $voucher);

        if (!$canUse) {
            return ['valid' => false, 'message' => 'Bạn đã sử dụng voucher này hoặc đã hết lượt sử dụng'];
        }
        
        // Kiểm tra voucher có áp dụng được cho loại đơn hàng này không
        if ($orderType === 'product' && !$voucher->isApplicableToProducts()) {
            return ['valid' => false, 'message' => 'Voucher này không áp dụng cho sản phẩm'];
        }
        
        if ($orderType === 'service' && !$voucher->isApplicableToServices()) {
            return ['valid' => false, 'message' => 'Voucher này không áp dụng cho dịch vụ'];
        }
        
        // Kiểm tra category nếu voucher có giới hạn category
        if ($voucher->category_id && $voucher->category_id != $categoryId) {
            return ['valid' => false, 'message' => 'Voucher này không áp dụng cho danh mục này'];
        }

        // Kiểm tra specific items nếu voucher chỉ áp dụng cho items cụ thể
        if ($voucher->specific_items_only) {
            // Nếu voucher yêu cầu specific items nhưng không có itemIds được truyền vào
            if (empty($itemIds)) {
                return ['valid' => false, 'message' => 'Voucher này chỉ áp dụng cho sản phẩm/dịch vụ cụ thể'];
            }

            // Sử dụng collection đã eager load thay vì query mới
            $applicableItemIds = $voucher->applicableItems
                ->where('applicable_type', $orderType === 'product' ? 'product' : 'service')
                ->pluck('applicable_id')
                ->toArray();

            // Nếu không có applicable items nào được cấu hình
            if (empty($applicableItemIds)) {
                return ['valid' => false, 'message' => 'Voucher này chưa được cấu hình sản phẩm/dịch vụ áp dụng. Vui lòng liên hệ admin.'];
            }

            $validItems = array_intersect($itemIds, $applicableItemIds);
            if (empty($validItems)) {
                return ['valid' => false, 'message' => 'Voucher này không áp dụng cho sản phẩm/dịch vụ đã chọn'];
            }
        }
        
        // Kiểm tra giá trị đơn hàng tối thiểu
        if ($voucher->min_order_amount && $totalAmount < $voucher->min_order_amount) {
            $minAmount = number_format((float)$voucher->min_order_amount, 0, ',', '.') . ' đ';
            return ['valid' => false, 'message' => "Đơn hàng tối thiểu {$minAmount} để sử dụng voucher này"];
        }
        
        // Kiểm tra tổng số lần sử dụng
        if ($voucher->hasReachedTotalLimit()) {
            return ['valid' => false, 'message' => 'Voucher đã hết lượt sử dụng'];
        }
        
        return [
            'valid' => true,
            'voucher' => $voucher,
            'can_use' => $canUse, // Có thể là UserVoucher object hoặc string 'can_use'
            'discount_amount' => $this->calculateDiscountAmount($voucher, $totalAmount)
        ];
    }
    
    /**
     * Tính số tiền giảm giá
     */
    public function calculateDiscountAmount(Voucher $voucher, $totalAmount)
    {
        if ($voucher->discount_type === 'percentage') {
            $discountAmount = ($totalAmount * $voucher->discount_value) / 100;

            // Áp dụng max discount cap nếu có
            if ($voucher->max_discount_amount && $discountAmount > $voucher->max_discount_amount) {
                $discountAmount = $voucher->max_discount_amount;
            }

            return $discountAmount;
        } elseif ($voucher->discount_type === 'fixed') {
            // Đảm bảo số tiền giảm không vượt quá tổng tiền đơn hàng
            return min($voucher->discount_value, $totalAmount);
        }

        return 0;
    }
    
    /**
     * Áp dụng voucher cho đơn hàng
     */
    public function applyVoucherToOrder($voucherCode, User $user, $orderId = null, $serviceOrderId = null)
    {
        // Tìm voucher theo code
        $voucher = Voucher::where('code', $voucherCode)->active()->notExpired()->first();
        if (!$voucher) {
            return false;
        }

        // Lấy hoặc tạo user voucher
        $userVoucher = $this->getOrCreateUserVoucherForApply($user, $voucher);

        if (!$userVoucher) {
            return false;
        }

        $userVoucher->markAsUsed($orderId, $serviceOrderId);
        return true;
    }
    
    /**
     * Lấy danh sách voucher available cho user với phân trang
     * Chỉ hiển thị auto voucher, manual voucher user phải tự nhập
     */
    public function getAvailableVouchersForUser(User $user, $orderType = null, $categoryId = null, $perPage = 10)
    {
        $query = $user->availableVouchers()
            ->with(['voucher.category']) // Eager load voucher và category để tránh N+1
            ->whereHas('voucher', function ($q) {
                // Chỉ lấy auto voucher, không lấy manual voucher
                $q->where('type', 'auto_product_purchase');
            });

        if ($orderType) {
            $query->whereHas('voucher', function ($q) use ($orderType) {
                if ($orderType === 'product') {
                    $q->where('applicable_to', 'products')
                      ->orWhere('applicable_to', 'both');
                } elseif ($orderType === 'service') {
                    $q->where('applicable_to', 'services')
                      ->orWhere('applicable_to', 'both');
                }
            });
        }

        if ($categoryId) {
            $query->whereHas('voucher', function ($q) use ($categoryId) {
                $q->where('category_id', $categoryId)
                  ->orWhereNull('category_id');
            });
        }

        // Sử dụng paginate thay vì get
        $vouchers = $query->paginate($perPage, ['*'], 'available_page');

        // Đảm bảo tất cả relationships cần thiết đã được load
        $this->ensureVoucherRelationshipsLoaded($vouchers);

        return $vouchers;
    }

    /**
     * Lấy danh sách voucher available cho user không phân trang (cho API/AJAX)
     * Chỉ hiển thị auto voucher, manual voucher user phải tự nhập
     */
    public function getAvailableVouchersForUserCollection(User $user, $orderType = null, $categoryId = null)
    {
        $query = $user->availableVouchers()
            ->with(['voucher.category']) // Eager load voucher và category để tránh N+1
            ->whereHas('voucher', function ($q) {
                // Chỉ lấy auto voucher, không lấy manual voucher
                $q->where('type', 'auto_product_purchase');
            });

        if ($orderType) {
            $query->whereHas('voucher', function ($q) use ($orderType) {
                if ($orderType === 'product') {
                    $q->where('applicable_to', 'products')
                      ->orWhere('applicable_to', 'both');
                } elseif ($orderType === 'service') {
                    $q->where('applicable_to', 'services')
                      ->orWhere('applicable_to', 'both');
                }
            });
        }

        if ($categoryId) {
            $query->whereHas('voucher', function ($q) use ($categoryId) {
                $q->where('category_id', $categoryId)
                  ->orWhereNull('category_id');
            });
        }

        $vouchers = $query->get();

        // Đảm bảo tất cả relationships cần thiết đã được load
        $this->ensureVoucherRelationshipsLoaded($vouchers);

        return $vouchers;
    }

    /**
     * Đảm bảo voucher relationships đã được eager load để tránh N+1
     */
    private function ensureVoucherRelationshipsLoaded($userVouchers)
    {
        // Hỗ trợ cả Collection và Paginator
        $items = $userVouchers instanceof \Illuminate\Pagination\LengthAwarePaginator
            ? $userVouchers->getCollection()
            : $userVouchers;

        if ($items->isEmpty()) {
            return;
        }

        // Kiểm tra và load voucher relationship nếu chưa có
        if (!$items->first()->relationLoaded('voucher')) {
            $items->load('voucher.category');
        } elseif ($items->first()->voucher && !$items->first()->voucher->relationLoaded('category')) {
            $items->load('voucher.category');
        }
    }

    /**
     * Tự động cấp tất cả manual voucher active cho user
     * DEPRECATED: Manual voucher giờ user phải tự nhập, không tự động cấp
     */
    private function autoGrantManualVouchers(User $user)
    {
        // Không làm gì cả - manual voucher user phải tự nhập
        return;
    }

    /**
     * Cleanup voucher hết hạn cho user cụ thể
     */
    public function cleanupExpiredVouchersForUser(User $user, $daysAfterExpiry = 0)
    {
        if ($daysAfterExpiry === 0) {
            // Xóa ngay khi hết hạn
            $cutoffDate = now();
        } else {
            // Xóa sau grace period
            $cutoffDate = now()->subDays($daysAfterExpiry);
        }

        $deletedCount = UserVoucher::where('user_id', $user->id)
            ->whereNull('used_at')
            ->where('expires_at', '<', $cutoffDate)
            ->delete();

        return $deletedCount;
    }

    /**
     * Lấy thống kê voucher hết hạn
     */
    public function getExpiredVoucherStats($daysAfterExpiry = 0)
    {
        if ($daysAfterExpiry === 0) {
            // Thống kê voucher hết hạn ngay
            $cutoffDate = now();
        } else {
            // Thống kê voucher hết hạn sau grace period
            $cutoffDate = now()->subDays($daysAfterExpiry);
        }

        return [
            'total_expired' => UserVoucher::whereNull('used_at')
                ->where('expires_at', '<', $cutoffDate)
                ->count(),
            'by_voucher' => UserVoucher::whereNull('used_at')
                ->where('expires_at', '<', $cutoffDate)
                ->with('voucher:id,name,code')
                ->get()
                ->groupBy('voucher.name')
                ->map(function ($group) {
                    return [
                        'count' => $group->count(),
                        'code' => $group->first()->voucher->code ?? 'N/A',
                    ];
                })
                ->toArray(),
        ];
    }
}
