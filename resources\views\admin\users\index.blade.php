@extends('layouts.admin')

@section('title', 'Người dùng')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Người dùng</h1>
            <p class="text-gray-600 mt-1">Quản lý tài khoản người dùng và thông tin cá nhân</p>
        </div>
        <div class="flex-shrink-0">
            <a href="{{ route('admin.users.create') }}" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Thêm mới
            </a>
        </div>
    </div>



    <!-- Search -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <form method="GET" class="flex gap-3">
            <div class="flex-1">
                <input type="text" name="search" value="{{ request('search') }}"
                       placeholder="Tìm kiếm theo tên, email, username..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                <i class="fas fa-search"></i>
            </button>
            @if(request('search'))
                <a href="{{ route('admin.users.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-times"></i>
                </a>
            @endif
        </form>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Username
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Tên hiển thị
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Email
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Số dư
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Vai trò
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Trạng thái
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Ngày tạo
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Thao tác
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($users ?? [] as $user)
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $user->id ?? 'N/A' }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $user->username ?? 'N/A' }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ $user->display_name ?? '' }}
                                    @if(!$user->display_name)
                                        <span class="text-gray-400 italic">Chưa đặt</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">{{ $user->email ?? 'N/A' }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ format_money($user->balance ?? 0) }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                @if(($user->role ?? 'user') == 'admin')
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-crown mr-1"></i>Admin
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-user mr-1"></i>User
                                    </span>
                                @endif
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                @if(($user->is_active ?? true))
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Hoạt động
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-ban mr-1"></i>Bị khóa
                                    </span>
                                @endif
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $user->created_at ? $user->created_at->format('d/m/Y') : 'N/A' }}
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button onclick="openBalanceModal({{ $user->id }}, '{{ $user->username }}', {{ $user->balance ?? 0 }})"
                                            class="text-green-600 hover:text-green-900 transition-colors" title="Cộng/trừ tiền">
                                        <i class="fas fa-wallet"></i>
                                    </button>
                                    <button onclick="toggleUserStatus({{ $user->id }}, '{{ ($user->is_active ?? true) ? 'true' : 'false' }}')"
                                            class="{{ ($user->is_active ?? true) ? 'text-orange-600 hover:text-orange-900' : 'text-green-600 hover:text-green-900' }} transition-colors"
                                            title="{{ ($user->is_active ?? true) ? 'Khóa tài khoản' : 'Mở khóa tài khoản' }}">
                                        <i class="fas {{ ($user->is_active ?? true) ? 'fa-ban' : 'fa-check-circle' }}"></i>
                                    </button>
                                    <a href="{{ route('admin.users.edit', $user->id) }}"
                                       class="text-blue-600 hover:text-blue-900 transition-colors" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="confirmDelete({{ $user->id }})"
                                            class="text-red-600 hover:text-red-900 transition-colors" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="px-4 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-users text-4xl mb-4 block"></i>
                                    <p class="text-lg">Chưa có người dùng nào</p>
                                    <p class="text-sm">Hãy thêm người dùng đầu tiên</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if(isset($users) && $users->hasPages())
            <div class="px-4 py-3 border-t border-gray-200">
                {{ $users->withQueryString()->links('pagination.custom') }}
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
function confirmDelete(userId) {
    if (confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
        // Create form to delete
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin/users') }}/${userId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function toggleUserStatus(userId, currentStatus) {
    // Chuyển đổi về boolean để so sánh chính xác
    const isActive = (currentStatus === 'true' || currentStatus === true || currentStatus === 1);
    const action = isActive ? 'khóa' : 'mở khóa';

    if (confirm(`Bạn có chắc chắn muốn ${action} tài khoản này?`)) {
        // Create form to toggle status
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin/users') }}/${userId}/toggle-status`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PATCH';

        form.appendChild(csrfToken);
        form.appendChild(methodInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Balance Modal Functions
function openBalanceModal(userId, username, currentBalance) {
    document.getElementById('balanceUserId').value = userId;
    document.getElementById('balanceUsername').textContent = username;
    document.getElementById('currentBalance').textContent = new Intl.NumberFormat('de-DE').format(currentBalance) + 'đ';
    document.getElementById('balanceModal').classList.remove('hidden');
    document.getElementById('amount').focus();
}

function closeBalanceModal() {
    document.getElementById('balanceModal').classList.add('hidden');
    document.getElementById('balanceForm').reset();
}

// Close modal when clicking outside
document.getElementById('balanceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBalanceModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('balanceModal').classList.contains('hidden')) {
        closeBalanceModal();
    }
});

function submitBalanceForm() {
    const form = document.getElementById('balanceForm');
    const formData = new FormData(form);
    const userId = formData.get('user_id');
    const submitBtn = form.querySelector('button[type="submit"]');

    // Disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Đang xử lý...';

    fetch(`{{ route('admin.users.adjust-balance', ':userId') }}`.replace(':userId', userId), {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Just reload without alert
            location.reload();
        } else {
            alert(data.message || 'Có lỗi xảy ra');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi xử lý yêu cầu: ' + error.message);
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-check mr-1"></i>Xác nhận';
    });
}
</script>
@endpush

<!-- Balance Adjustment Modal -->
<div id="balanceModal" class="fixed inset-0 hidden z-50" onclick="closeBalanceModal()" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6 shadow-2xl" onclick="event.stopPropagation()">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-wallet text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Điều chỉnh số dư</h3>
                        <p class="text-sm text-gray-500">Quản lý tài khoản người dùng</p>
                    </div>
                </div>
                <button onclick="closeBalanceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- User Info -->
            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                <div class="text-sm text-gray-600">User:</div>
                <div class="font-medium text-gray-900" id="balanceUsername"></div>
                <div class="text-sm text-gray-600 mt-1">Số dư hiện tại:</div>
                <div class="font-medium text-blue-600" id="currentBalance"></div>
            </div>

            <!-- Balance Form -->
            <form id="balanceForm" onsubmit="event.preventDefault(); submitBalanceForm();">
                <input type="hidden" id="balanceUserId" name="user_id">

                <!-- Action Type -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Loại thao tác</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="action" value="add" class="mr-2" checked>
                            <span class="text-green-600"><i class="fas fa-plus mr-1"></i>Cộng tiền</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="action" value="subtract" class="mr-2">
                            <span class="text-red-600"><i class="fas fa-minus mr-1"></i>Trừ tiền</span>
                        </label>
                    </div>
                </div>

                <!-- Amount -->
                <div class="mb-4">
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Số tiền</label>
                    <input type="number" id="amount" name="amount" min="1000" max="1000000" step="1000" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Nhập số tiền (tối đa 1,000,000đ)">
                    <div class="text-xs text-gray-500 mt-1">Tối thiểu: 1,000đ - Tối đa: 1,000,000đ</div>
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Lý do</label>
                    <textarea id="description" name="description" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Nhập lý do điều chỉnh số dư..."></textarea>
                </div>

                <!-- Actions -->
                <div class="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeBalanceModal()"
                            class="px-5 py-2.5 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors">
                        Hủy
                    </button>
                    <button type="submit"
                            class="px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm">
                        <i class="fas fa-check mr-2"></i>Xác nhận
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection
