<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Thêm 'service_purchase' vào enum type
        DB::statement("ALTER TABLE balance_transactions MODIFY COLUMN type ENUM('topup_atm', 'topup_card', 'admin_add', 'admin_subtract', 'purchase', 'service_purchase', 'refund') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert về enum cũ
        DB::statement("ALTER TABLE balance_transactions MODIFY COLUMN type ENUM('topup_atm', 'topup_card', 'admin_add', 'admin_subtract', 'purchase', 'refund') NOT NULL");
    }
};