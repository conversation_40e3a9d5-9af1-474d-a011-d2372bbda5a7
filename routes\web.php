<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\Admin\AdminAuthController;


// Home
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Login
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Register
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    // Forgot Password
    Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/reset-password/{token}', [ForgotPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('/reset-password', [ForgotPasswordController::class, 'reset'])->name('password.update');
});

// Logout (requires auth)
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth')->name('logout');

// Authenticated user routes
Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'index'])->name('profile.index');
    Route::put('/profile/display-name', [App\Http\Controllers\ProfileController::class, 'updateDisplayName'])->name('profile.display-name');
    Route::put('/profile/email', [App\Http\Controllers\ProfileController::class, 'updateEmail'])->name('profile.email');
    Route::put('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password');

    // Wallet route
    Route::get('/wallet', function () {
        return view('user.wallet');
    })->name('wallet');

    // Topup routes
    Route::get('/topup', [App\Http\Controllers\TopupController::class, 'index'])->name('topup.index');
    Route::get('/topup/atm', [App\Http\Controllers\TopupController::class, 'atm'])->name('topup.atm');
    Route::get('/topup/card', [App\Http\Controllers\TopupController::class, 'card'])->name('topup.card');
    Route::post('/topup/card', [App\Http\Controllers\TopupController::class, 'processCard'])->name('topup.card.process');
    Route::get('/topup/history', [App\Http\Controllers\TopupController::class, 'history'])->name('topup.history');

    // Purchase routes với throttling để tránh spam
    Route::post('/purchase', [App\Http\Controllers\PurchaseController::class, 'purchase'])
        ->middleware('throttle:10,1') // 10 requests per minute per user
        ->name('purchase');

    // Voucher routes với throttling để tránh spam
    Route::post('/vouchers/validate', [App\Http\Controllers\VoucherController::class, 'validate'])
        ->middleware('throttle:10,1') // 10 requests per minute per user
        ->name('vouchers.validate');
    Route::get('/vouchers/available', [App\Http\Controllers\VoucherController::class, 'getAvailable'])
        ->middleware('throttle:20,1') // 20 requests per minute per user
        ->name('vouchers.available');

    // Order routes
    Route::get('/orders', [App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
});

// Public routes
Route::get('/product/{slug}', [App\Http\Controllers\ProductController::class, 'show'])->name('product.show');

Route::get('/category/{slug}', [App\Http\Controllers\CategoryController::class, 'show'])->name('category.show');

// Service routes
Route::get('/services/{service}', [ServiceController::class, 'show'])->name('services.show');

// Guide routes
Route::get('/guides', [App\Http\Controllers\GuideController::class, 'index'])->name('guides.index');
Route::get('/guide/{slug}', [App\Http\Controllers\GuideController::class, 'show'])->name('guide.show');

// Protected service routes
Route::middleware('auth')->group(function () {
    Route::post('/services/{service}/purchase', [ServiceController::class, 'purchase'])
        ->middleware('throttle:5,1') // 5 requests per minute per user
        ->name('services.purchase');

    // Service Orders
    Route::get('/service-orders', [App\Http\Controllers\ServiceOrderController::class, 'index'])->name('service-orders.index');
    Route::get('/service-orders/{serviceOrder}', [App\Http\Controllers\ServiceOrderController::class, 'show'])->name('service-orders.show');
});


// Admin Routes với Secret Key
Route::prefix(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin')
    ->middleware(['web'])
    ->group(function () {
        // Admin Authentication Routes
        Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('admin.login');
        Route::post('/login', [AdminAuthController::class, 'login'])->name('admin.login.post');

        // Protected Admin Routes
        Route::middleware(['auth', 'admin.access'])->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AdminDashboardController::class, 'index'])->name('admin.dashboard');
            Route::post('/logout', [AdminAuthController::class, 'logout'])->name('admin.logout');

            // User Management
            Route::resource('users', App\Http\Controllers\Admin\UserController::class, ['as' => 'admin']);
            Route::post('users/bulk-action', [App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('admin.users.bulk-action');
            Route::patch('users/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('admin.users.toggle-status');
            Route::post('users/{user}/adjust-balance', [App\Http\Controllers\Admin\UserController::class, 'adjustBalance'])->name('admin.users.adjust-balance');

            // Category Management
            Route::resource('categories', App\Http\Controllers\Admin\CategoryController::class, ['as' => 'admin']);
            Route::post('categories/bulk-action', [App\Http\Controllers\Admin\CategoryController::class, 'bulkAction'])->name('admin.categories.bulk-action');
            Route::patch('categories/{category}/toggle-status', [App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('admin.categories.toggle-status');

            // Product Management
            Route::resource('products', App\Http\Controllers\Admin\ProductController::class, ['as' => 'admin']);
            Route::post('products/bulk-action', [App\Http\Controllers\Admin\ProductController::class, 'bulkAction'])->name('admin.products.bulk-action');
            Route::patch('products/{product}/toggle-status', [App\Http\Controllers\Admin\ProductController::class, 'toggleStatus'])->name('admin.products.toggle-status');
            Route::get('products/search/categories', [App\Http\Controllers\Admin\ProductController::class, 'searchCategories'])->name('admin.products.search-categories');

            // Product Account Management - Quay lại cách cũ thông qua products
            Route::get('products/{product}/accounts', [App\Http\Controllers\Admin\ProductController::class, 'accounts'])->name('admin.products.accounts');
            Route::post('products/{product}/accounts', [App\Http\Controllers\Admin\ProductController::class, 'storeAccount'])->name('admin.products.accounts.store');
            Route::put('products/{product}/accounts/{account}', [App\Http\Controllers\Admin\ProductController::class, 'updateAccount'])->name('admin.products.accounts.update');
            Route::delete('products/{product}/accounts/{account}', [App\Http\Controllers\Admin\ProductController::class, 'destroyAccount'])->name('admin.products.accounts.destroy');
            Route::post('products/{product}/accounts/bulk-import', [App\Http\Controllers\Admin\ProductController::class, 'bulkImportAccounts'])->name('admin.products.accounts.bulk-import');
            Route::post('products/{product}/accounts/bulk-action', [App\Http\Controllers\Admin\ProductController::class, 'bulkActionAccounts'])->name('admin.products.accounts.bulk-action');

            // Order Management
            Route::resource('orders', App\Http\Controllers\Admin\OrderController::class, ['as' => 'admin', 'only' => ['index', 'show']]);

            // Service Management
            Route::resource('services', App\Http\Controllers\Admin\AdminServiceController::class, ['as' => 'admin', 'except' => ['show']]);

            // Service Order Management
            Route::prefix('service-orders')->name('admin.service-orders.')->group(function () {
                Route::get('/', [App\Http\Controllers\Admin\AdminServiceOrderController::class, 'index'])->name('index');
                Route::get('/export', [App\Http\Controllers\Admin\AdminServiceOrderController::class, 'export'])->name('export');
                Route::get('/{serviceOrder}', [App\Http\Controllers\Admin\AdminServiceOrderController::class, 'show'])->name('show');
                Route::post('/{serviceOrder}/status', [App\Http\Controllers\Admin\AdminServiceOrderController::class, 'updateStatus'])->name('update-status');
                Route::post('/bulk-update', [App\Http\Controllers\Admin\AdminServiceOrderController::class, 'bulkUpdateStatus'])->name('bulk-update');
            });

            // Voucher Management
            Route::resource('vouchers', App\Http\Controllers\Admin\VoucherController::class, ['as' => 'admin']);
            Route::patch('vouchers/{voucher}/toggle-status', [App\Http\Controllers\Admin\VoucherController::class, 'toggleStatus'])->name('admin.vouchers.toggle-status');

            // Guide Management
            Route::resource('guides', App\Http\Controllers\Admin\AdminGuideController::class, ['as' => 'admin']);
            Route::patch('vouchers/{id}/restore', [App\Http\Controllers\Admin\VoucherController::class, 'restore'])->name('admin.vouchers.restore');
            Route::delete('vouchers/{id}/force-delete', [App\Http\Controllers\Admin\VoucherController::class, 'forceDelete'])->name('admin.vouchers.force-delete');

            // Transaction Logs
            Route::prefix('transaction-logs')->name('admin.transaction-logs.')->group(function () {
                Route::get('/', [App\Http\Controllers\Admin\TransactionLogController::class, 'index'])->name('index');
                Route::get('/export', [App\Http\Controllers\Admin\TransactionLogController::class, 'export'])->name('export');
            });

            // Revenue Reports
            Route::prefix('revenue-report')->name('admin.revenue-report.')->group(function () {
                Route::get('/', [App\Http\Controllers\Admin\RevenueReportController::class, 'index'])->name('index');
                Route::get('/export', [App\Http\Controllers\Admin\RevenueReportController::class, 'export'])->name('export');
            });
        });
    });

Route::get('/api/search/categories', [App\Http\Controllers\SearchController::class, 'searchCategories'])->name('api.search.categories');
Route::get('/api/admin/search/categories', [App\Http\Controllers\Admin\ProductController::class, 'searchCategories'])->name('api.admin.search.categories');

// Storage file serving routes
Route::get('/storage/{folder}/{filename}', function ($folder, $filename) {
    // Sanitize the path to prevent directory traversal
    $folder = preg_replace('/[^a-zA-Z0-9_-]/', '', $folder);
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);

    $path = storage_path("app/public/{$folder}/{$filename}");

    // Additional security check
    $realPath = realpath($path);
    $allowedPath = realpath(storage_path('app/public'));

    if (!$realPath || !str_starts_with($realPath, $allowedPath) || !file_exists($path)) {
        abort(404);
    }

    $lastModified = filemtime($path);
    $etag = md5($lastModified . $path);

    // Check if browser has cached version
    $ifModifiedSince = request()->header('If-Modified-Since');
    $ifNoneMatch = request()->header('If-None-Match');

    if ($ifModifiedSince && strtotime($ifModifiedSince) >= $lastModified) {
        return response('', 304);
    }

    if ($ifNoneMatch && $ifNoneMatch === '"' . $etag . '"') {
        return response('', 304);
    }

    $file = file_get_contents($path);
    $type = mime_content_type($path);

    return response($file, 200)
        ->header('Content-Type', $type)
        ->header('Cache-Control', 'public, max-age=86400') // Cache for 1 day
        ->header('Last-Modified', gmdate('D, d M Y H:i:s', $lastModified) . ' GMT')
        ->header('ETag', '"' . $etag . '"'); // ETag để browser check thay đổi
})->where(['folder' => '[a-zA-Z0-9_-]+', 'filename' => '[a-zA-Z0-9._-]+'])
  ->middleware('optimize.images')
  ->name('storage.file');

// Card API callback (không cần auth)
Route::post('/qjHMWJ8v/charge/callback', [App\Http\Controllers\CardCallbackController::class, 'handle'])->name('card.callback');
// SePay webhook (không cần auth) - URL đã config bên SePay
Route::post('/xej2AeSj/hooks/sepay-payment', [App\Http\Controllers\SePayController::class, 'webhook'])->name('sepay.webhook');