<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CardApiService
{
    private $apiUrl;
    private $partnerId;
    private $partnerKey;
    private $walletId;

    public function __construct()
    {
        $this->apiUrl = config('card.api.url');
        $this->partnerId = config('card.api.partner_id');
        $this->partnerKey = config('card.api.partner_key');
        $this->walletId = config('card.api.wallet_id');
    }

    /**
     * Nạp thẻ cào
     */
    public function chargeCard($telco, $code, $serial, $amount)
    {
        try {
            // Tạo request_id unique
            $requestId = time() . rand(1000, 9999);
            
            // Tạo chữ ký MD5
            $sign = md5($this->partnerKey . $code . $serial);
            
            $payload = [
                'telco' => strtoupper($telco),
                'code' => $code,
                'serial' => $serial,
                'amount' => (int)$amount,
                'request_id' => $requestId,
                'partner_id' => $this->partnerId,
                'sign' => $sign,
                'command' => 'charging'
            ];

            Log::info('Card API Request', [
                'url' => $this->apiUrl,
                'payload' => $payload
            ]);

            $timeout = config('card.api.timeout', 30);
            $response = Http::timeout($timeout)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($this->apiUrl, $payload);

            $result = $response->json();

            Log::info('Card API Response', [
                'status' => $response->status(),
                'response' => $result
            ]);

            return [
                'success' => $response->successful(),
                'data' => $result,
                'request_id' => $requestId,
                'http_status' => $response->status()
            ];

        } catch (\Exception $e) {
            Log::error('Card API Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Lấy danh sách nhà mạng hỗ trợ
     */
    public static function getSupportedTelcos()
    {
        return config('card.supported_telcos', []);
    }

    /**
     * Giải thích mã trạng thái
     */
    public static function getStatusMessage($status)
    {
        $messages = config('card.status_messages', []);
        return $messages[$status] ?? 'Lỗi không xác định';
    }

    /**
     * Kiểm tra trạng thái thành công
     */
    public static function isSuccess($status)
    {
        return $status == config('card.status.success', 1);
    }

    /**
     * Kiểm tra trạng thái chờ xử lý
     */
    public static function isPending($status)
    {
        return $status == config('card.status.pending', 99);
    }

    /**
     * Validate card data theo loại thẻ
     */
    public static function validateCardData($telco, $amount, $serial, $code)
    {
        $errors = [];

        // Validate telco
        $supportedTelcos = ['VIETTEL', 'MOBIFONE', 'VINAPHONE', 'ZING'];
        if (!in_array($telco, $supportedTelcos)) {
            $errors['telco'] = 'Nhà mạng không được hỗ trợ';
        }

        // Validate amount
        $allowedAmounts = [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000];
        if (!in_array($amount, $allowedAmounts)) {
            $errors['amount'] = 'Mệnh giá không hợp lệ';
        }

        // Validate theo loại thẻ
        if ($telco === 'ZING') {
            // Thẻ Zing có format khác
            if (strlen($serial) < 8 || strlen($serial) > 15) {
                $errors['serial'] = 'Serial thẻ Zing phải từ 8-15 ký tự';
            }
            if (strlen($code) < 8 || strlen($code) > 15) {
                $errors['code'] = 'Mã thẻ Zing phải từ 8-15 ký tự';
            }
            // Pattern cho thẻ Zing (chữ và số)
            if (!preg_match('/^[A-Z0-9]+$/', $serial)) {
                $errors['serial'] = 'Serial thẻ Zing chỉ được chứa chữ hoa và số';
            }
            if (!preg_match('/^[A-Z0-9]+$/', $code)) {
                $errors['code'] = 'Mã thẻ Zing chỉ được chứa chữ hoa và số';
            }
        } else {
            // Thẻ điện thoại (Viettel, Mobifone, Vinaphone)
            if (strlen($serial) < 10 || strlen($serial) > 20) {
                $errors['serial'] = 'Serial thẻ điện thoại phải từ 10-20 ký tự';
            }
            if (strlen($code) < 10 || strlen($code) > 20) {
                $errors['code'] = 'Mã thẻ điện thoại phải từ 10-20 ký tự';
            }
            // Pattern cho thẻ điện thoại (chỉ số)
            if (!preg_match('/^[0-9]+$/', $serial)) {
                $errors['serial'] = 'Serial thẻ điện thoại chỉ được chứa số';
            }
            if (!preg_match('/^[0-9]+$/', $code)) {
                $errors['code'] = 'Mã thẻ điện thoại chỉ được chứa số';
            }
        }

        return $errors;
    }

    /**
     * Lấy danh sách mệnh giá được hỗ trợ cho nhà mạng
     */
    public static function getSupportedAmounts($telco = null)
    {
        if ($telco) {
            $telcos = static::getSupportedTelcos();
            return $telcos[$telco]['amounts'] ?? [];
        }

        return config('card.validation.amount.allowed', []);
    }

    /**
     * Kiểm tra trạng thái thẻ
     */
    public function checkCard($telco, $code, $serial, $amount, $requestId = null)
    {
        try {
            // Sử dụng request_id cũ hoặc tạo mới
            $requestId = $requestId ?: (time() . rand(1000, 9999));

            // Tạo chữ ký MD5
            $sign = md5($this->partnerKey . $code . $serial);

            $payload = [
                'telco' => strtoupper($telco),
                'code' => $code,
                'serial' => $serial,
                'amount' => (int)$amount,
                'request_id' => $requestId,
                'partner_id' => $this->partnerId,
                'sign' => $sign,
                'command' => 'check'
            ];

            Log::info('Card Check API Request', [
                'url' => $this->apiUrl,
                'payload' => $payload
            ]);

            $timeout = config('card.api.timeout', 30);
            $response = Http::timeout($timeout)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($this->apiUrl, $payload);

            $result = $response->json();

            Log::info('Card Check API Response', [
                'status' => $response->status(),
                'response' => $result
            ]);

            return [
                'success' => $response->successful(),
                'data' => $result,
                'request_id' => $requestId,
                'http_status' => $response->status()
            ];

        } catch (\Exception $e) {
            Log::error('Card Check API Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null
            ];
        }
    }
}
