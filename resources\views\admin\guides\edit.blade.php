@extends('layouts.admin')

@section('title', 'Chỉnh sửa Hướng dẫn')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Chỉnh sửa Hướng dẫn</h1>
            <p class="text-gray-600 mt-1">Chỉnh sửa bài hướng dẫn</p>
        </div>
        <div class="flex-shrink-0">
            <a href="{{ route('admin.guides.index') }}"
               class="inline-flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('admin.guides.update', $guide) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Title -->
                <div class="bg-white rounded-lg shadow p-6">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Tiêu đề <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="title" name="title" value="{{ old('title', $guide->title) }}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('title') border-red-500 @enderror"
                           placeholder="Nhập tiêu đề hướng dẫn">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Content -->
                <div class="bg-white rounded-lg shadow p-6">
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        Nội dung <span class="text-red-500">*</span>
                    </label>
                    <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-medium text-blue-900 mb-2">💡 Hướng dẫn chèn media:</h4>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li><strong>Ảnh:</strong> <code>&lt;img src="URL_ảnh" alt="Mô tả" class="w-full rounded-lg"&gt;</code></li>
                            <li><strong>YouTube (mặc định):</strong> <code>&lt;iframe src="https://www.youtube.com/embed/VIDEO_ID" frameborder="0" allowfullscreen&gt;&lt;/iframe&gt;</code></li>
                            <li><strong>YouTube (tùy chỉnh kích thước):</strong> <code>&lt;iframe src="https://www.youtube.com/embed/VIDEO_ID" width="640" height="360" frameborder="0" allowfullscreen&gt;&lt;/iframe&gt;</code></li>
                            <li><strong>Link:</strong> <code>&lt;a href="URL" target="_blank"&gt;Tên link&lt;/a&gt;</code></li>
                        </ul>
                    </div>
                    <textarea id="content" name="content" rows="15" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('content') border-red-500 @enderror"
                              placeholder="Nhập nội dung hướng dẫn (hỗ trợ HTML)">{{ old('content', $guide->content) }}</textarea>
                    @error('content')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Publish Settings -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Cài đặt xuất bản</h3>
                    
                    <!-- Status -->
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                        <select id="status" name="status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="draft" {{ old('status', $guide->status) == 'draft' ? 'selected' : '' }}>Nháp</option>
                            <option value="published" {{ old('status', $guide->status) == 'published' ? 'selected' : '' }}>Xuất bản</option>
                        </select>
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Thứ tự sắp xếp</label>
                        <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', $guide->sort_order) }}" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex items-center justify-end space-x-4 bg-white rounded-lg shadow p-6">
            <a href="{{ route('admin.guides.index') }}" 
               class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                Hủy
            </a>
            <button type="submit" 
                    class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors">
                <i class="fas fa-save mr-2"></i>
                Cập nhật hướng dẫn
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
// Auto-resize textarea
document.getElementById('content').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>
@endpush
@endsection
