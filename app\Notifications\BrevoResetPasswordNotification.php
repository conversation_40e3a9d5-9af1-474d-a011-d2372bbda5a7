<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use App\Services\BrevoService;

class BrevoResetPasswordNotification extends Notification
{
    use Queueable;

    public $token;
    private $brevoService;

    /**
     * Create a new notification instance.
     */
    public function __construct($token)
    {
        $this->token = $token;
        $this->brevoService = new BrevoService();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['brevo'];
    }

    /**
     * Send notification via Brevo
     */
    public function toBrevo($notifiable)
    {
        $resetUrl = url(route('password.reset', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));

        $userName = $notifiable->display_name ?: $notifiable->username;

        $result = $this->brevoService->sendPasswordResetEmail(
            $notifiable->getEmailForPasswordReset(),
            $resetUrl,
            $userName
        );

        if (!$result['success']) {
            throw new \Exception('Failed to send email via Brevo: ' . $result['error']);
        }

        return $result;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ];
    }
}
