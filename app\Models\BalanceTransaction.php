<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class BalanceTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'description',
        'admin_id',
        'reference_type',
        'reference_id',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
    ];

    // Transaction Types
    public const TYPE_TOPUP_ATM = 'topup_atm';
    public const TYPE_TOPUP_CARD = 'topup_card';
    public const TYPE_ADMIN_ADD = 'admin_add';
    public const TYPE_ADMIN_SUBTRACT = 'admin_subtract';
    public const TYPE_PURCHASE = 'purchase';
    public const TYPE_SERVICE_PURCHASE = 'service_purchase';
    public const TYPE_REFUND = 'refund';

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeIncome($query)
    {
        return $query->where('amount', '>', 0);
    }

    public function scopeExpense($query)
    {
        return $query->where('amount', '<', 0);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    // Helper Methods
    public function isIncome(): bool
    {
        return $this->amount > 0;
    }

    public function isExpense(): bool
    {
        return $this->amount < 0;
    }

    public function getFormattedAmountAttribute(): string
    {
        $sign = $this->amount >= 0 ? '+' : '-';
        return $sign . format_money(abs((float)$this->amount), false) . 'đ';
    }

    public function getTypeNameAttribute(): string
    {
        return match($this->type) {
            self::TYPE_TOPUP_ATM => 'Nạp tiền ATM',
            self::TYPE_TOPUP_CARD => 'Nạp tiền thẻ',
            self::TYPE_ADMIN_ADD => 'Admin cộng tiền',
            self::TYPE_ADMIN_SUBTRACT => 'Admin trừ tiền',
            self::TYPE_PURCHASE => 'Mua sản phẩm',
            self::TYPE_SERVICE_PURCHASE => 'Mua dịch vụ',
            self::TYPE_REFUND => 'Hoàn tiền',
            default => 'Không xác định',
        };
    }

    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            self::TYPE_TOPUP_ATM, self::TYPE_TOPUP_CARD => 'text-green-600',
            self::TYPE_ADMIN_ADD => 'text-blue-600',
            self::TYPE_ADMIN_SUBTRACT => 'text-red-600',
            self::TYPE_PURCHASE => 'text-orange-600',
            self::TYPE_SERVICE_PURCHASE => 'text-purple-600',
            self::TYPE_REFUND => 'text-purple-600',
            default => 'text-gray-600',
        };
    }

    // Static Methods
    public static function createTransaction(
        User $user,
        string $type,
        int $amount,
        string $description,
        ?User $admin = null,
        ?Model $reference = null,
        array $metadata = []
    ): self {
        $balanceBefore = $user->balance;
        $balanceAfter = $balanceBefore + $amount;

        return self::create([
            'user_id' => $user->id,
            'type' => $type,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'description' => $description,
            'admin_id' => $admin?->id,
            'reference_type' => $reference ? get_class($reference) : null,
            'reference_id' => $reference?->id,
            'metadata' => $metadata,
        ]);
    }
}
