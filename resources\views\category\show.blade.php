@extends('layouts.app')

@section('title', $category->name . ' - AccReroll')

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-6 lg:mb-8" aria-label="Breadcrumb">
        <ol class="flex flex-wrap items-center gap-1 md:gap-2">
            <li class="flex items-center">
                <a href="{{ route('home') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 px-2 py-1 rounded">
                    <i class="fas fa-home mr-1 md:mr-2"></i>
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
            <li aria-current="page" class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                <span class="text-sm font-medium text-gray-500 px-2 py-1 rounded max-w-[120px] sm:max-w-[200px] lg:max-w-none truncate" title="{{ $category->name }}">
                    {{ $category->name }}
                </span>
            </li>
        </ol>
    </nav>

    <!-- Category Header -->
    <div class="mb-6 lg:mb-8">
        <div class="flex items-center mb-4">
            @if($category->image)
                <img src="{{ storage_url($category->image) }}"
                     alt="{{ $category->name }}"
                     class="w-16 h-16 rounded-lg mr-4 object-contain">
            @else
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-gamepad text-white text-2xl"></i>
                </div>
            @endif
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $category->name }}</h1>
                @if($category->publisher)
                    <p class="text-gray-600 mt-1">{{ $category->publisher }}</p>
                @endif
                @if($category->description)
                    <p class="text-gray-600 mt-2">{{ $category->description }}</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div>
        <div class="mb-6 flex items-center justify-between">
            <p class="text-gray-600">{{ $products->total() }} sản phẩm</p>

            <!-- Sort Dropdown -->
            <div class="relative">
                <select id="sortSelect" name="sort"
                        class="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="price_asc" {{ request('sort', 'price_asc') == 'price_asc' ? 'selected' : '' }}>Giá: Thấp → Cao</option>
                    <option value="price_desc" {{ request('sort') == 'price_desc' ? 'selected' : '' }}>Giá: Cao → Thấp</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </div>
            </div>
        </div>

        <div id="productsContainer">
            @if($products->count() > 0)
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    @foreach($products as $product)
                        <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                            <!-- Product Image -->
                            <div class="relative">
                                @if($product->main_image)
                                    <img src="{{ storage_url($product->main_image) }}"
                                         alt="{{ $product->name }}"
                                         class="w-full h-40 object-contain">
                                @else
                                    <div class="w-full h-40 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                        <i class="fas fa-gamepad text-white text-3xl"></i>
                                    </div>
                                @endif
                            </div>

                            <!-- Product Info -->
                            <div class="p-4 flex flex-col flex-1">
                                <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2">{{ $product->name }}</h3>
                                <p class="text-sm text-gray-500 mb-3">{{ $product->category->name }}</p>

                                <!-- Bottom section - always at bottom -->
                                <div class="mt-auto">
                                    @if($product->available_quantity > 0)
                                        <div class="text-green-600 font-semibold text-sm mb-2">
                                            Sẵn Có: {{ $product->available_quantity }}
                                        </div>
                                    @elseif($product->allow_preorder)
                                        <div class="font-semibold text-sm mb-2" style="color: #0ea5e9;">
                                            Còn hàng (nhận mã đơn)
                                        </div>
                                    @else
                                        <div class="text-red-600 font-semibold text-sm mb-2">
                                            Hết hàng
                                        </div>
                                    @endif
                                    <div class="text-orange-600 font-bold text-xl">{{ number_format($product->price, 0, ',', '.') }} đ</div>
                                </div>
                            </div>

                            <!-- Clickable overlay -->
                            <a href="{{ route('product.show', $product->slug) }}" class="absolute inset-0 z-10"></a>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8" id="paginationContainer">
                    {{ $products->links('pagination.custom') }}
                </div>
            @else
                <div class="text-center py-20">
                    <i class="fas fa-shopping-cart text-gray-400 text-5xl mb-3"></i>
                    <h4 class="text-gray-500 text-xl">Chưa có sản phẩm nào trong danh mục này</h4>
                    <p class="text-gray-500">Vui lòng quay lại sau!</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Services Section -->
      @if($services->count() > 0)
    <div class="mt-12 pt-8 border-t border-gray-200">
        <div class="mb-6">
            <span class="text-gray-600">{{ $services->count() }} dịch vụ</span>
        </div>
        
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                @foreach($services as $service)
                    <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                        <!-- Service Image -->
                        <div class="relative">
                            @if($service->first_image)
                                <img src="{{ $service->first_image }}"
                                     alt="{{ $service->name }}"
                                     class="w-full h-40 object-contain">
                            @else
                                <div class="w-full h-40 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                                    <i class="fas fa-tools text-white text-3xl"></i>
                                </div>
                            @endif
                        </div>

                        <!-- Service Info -->
                        <div class="p-4 flex flex-col flex-1">
                            <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2">{{ $service->name }}</h3>
                            <p class="text-sm text-gray-500 mb-3">{{ $service->category->name }}</p>

                            <!-- Bottom section - always at bottom -->
                            <div class="mt-auto">
                                <div class="text-orange-600 font-bold text-xl">{{ $service->formatted_price }}</div>
                            </div>
                        </div>

                        <!-- Clickable overlay -->
                        <a href="{{ route('services.show', $service) }}" class="absolute inset-0 z-10"></a>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sortSelect = document.getElementById('sortSelect');
    const productsContainer = document.getElementById('productsContainer');
    const productCountElement = document.querySelector('.text-gray-600');

    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;
            const currentUrl = new URL(window.location);

            // Show loading state
            productsContainer.style.opacity = '0.5';
            productsContainer.style.pointerEvents = 'none';

            // Update URL parameter
            currentUrl.searchParams.set('sort', sortValue);
            currentUrl.searchParams.delete('page'); // Reset to page 1

            // Make AJAX request
            fetch(currentUrl.toString(), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Parse the response
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Update products container
                const newProductsContainer = doc.getElementById('productsContainer');
                if (newProductsContainer) {
                    productsContainer.innerHTML = newProductsContainer.innerHTML;
                }

                // Update product count
                const newProductCount = doc.querySelector('.text-gray-600');
                if (newProductCount && productCountElement) {
                    productCountElement.textContent = newProductCount.textContent;
                }

                // Don't update URL - keep it clean

                // Restore normal state
                productsContainer.style.opacity = '1';
                productsContainer.style.pointerEvents = 'auto';

                // Scroll to top of products
                productsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            })
            .catch(error => {
                console.error('Error:', error);
                // Restore normal state on error
                productsContainer.style.opacity = '1';
                productsContainer.style.pointerEvents = 'auto';
            });
        });
    }

    // Handle pagination clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('#paginationContainer a')) {
            e.preventDefault();
            const link = e.target.closest('a');
            const url = link.href;

            // Show loading state
            productsContainer.style.opacity = '0.5';
            productsContainer.style.pointerEvents = 'none';

            fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Update products container
                const newProductsContainer = doc.getElementById('productsContainer');
                if (newProductsContainer) {
                    productsContainer.innerHTML = newProductsContainer.innerHTML;
                }

                // Update product count
                const newProductCount = doc.querySelector('.text-gray-600');
                if (newProductCount && productCountElement) {
                    productCountElement.textContent = newProductCount.textContent;
                }

                // Don't update URL for pagination

                // Restore normal state
                productsContainer.style.opacity = '1';
                productsContainer.style.pointerEvents = 'auto';

                // Scroll to top of products
                productsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            })
            .catch(error => {
                console.error('Error:', error);
                productsContainer.style.opacity = '1';
                productsContainer.style.pointerEvents = 'auto';
            });
        }
    });
});
</script>
@endsection
