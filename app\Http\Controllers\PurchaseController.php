<?php

namespace App\Http\Controllers;

use App\Models\BalanceTransaction;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductAccount;
use App\Services\VoucherService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    /**
     * Xử lý mua hàng
     */
    public function purchase(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1|max:10',
            'voucher_code' => 'nullable|string',
        ]);

        $user = Auth::user();
        $product = Product::findOrFail($request->product_id);
        $quantity = $request->quantity;

        // Kiểm tra sản phẩm có hoạt động không
        if ($product->status !== 'active') {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Sản phẩm không còn hoạt động!'
                ], 400);
            } else {
                return redirect()->back()->with('error', 'Sản phẩm không còn hoạt động!');
            }
        }

        // Kiểm tra số lượng tài khoản có sẵn
        $availableAccounts = ProductAccount::where('product_id', $product->id)
            ->where('status', 'available')
            ->count();

        $isPreorder = false;
        if ($availableAccounts == 0) {
            // Nếu hết hàng hoàn toàn
            if (!$product->allow_preorder) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Sản phẩm đã hết hàng!'
                    ], 400);
                } else {
                    return redirect()->back()->with('error', 'Sản phẩm đã hết hàng!');
                }
            }
            // Nếu cho phép đặt hàng thì đánh dấu là preorder
            $isPreorder = true;
        } elseif ($availableAccounts < $quantity) {
            // Nếu không đủ số lượng yêu cầu
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => "Chỉ còn {$availableAccounts} tài khoản có sẵn!"
                ], 400);
            } else {
                return redirect()->back()->with('error', "Chỉ còn {$availableAccounts} tài khoản có sẵn!");
            }
        }

        // Tính tổng tiền
        $totalAmount = $product->price * $quantity;
        $discountAmount = 0;
        $voucherService = new VoucherService();
        $appliedVoucher = null;

        // Xử lý voucher nếu có
        if ($request->voucher_code) {
            $voucherValidation = $voucherService->validateVoucherForOrder(
                $request->voucher_code,
                $user,
                'product',
                $product->category_id,
                $totalAmount,
                [$product->id] // Truyền product ID để kiểm tra specific items
            );

            if (!$voucherValidation['valid']) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $voucherValidation['message']
                    ], 400);
                } else {
                    return redirect()->back()->with('error', $voucherValidation['message']);
                }
            }

            $discountAmount = $voucherValidation['discount_amount'];
            $appliedVoucher = $voucherValidation['voucher'];
        }

        $finalAmount = $totalAmount - $discountAmount;

        // Kiểm tra số dư
        if ($user->balance < $finalAmount) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Số dư không đủ! Vui lòng liên hệ admin để nạp tiền.',
                    'required_amount' => $finalAmount,
                    'current_balance' => $user->balance,
                    'shortage' => $finalAmount - $user->balance
                ], 400);
            } else {
                $shortage = $finalAmount - $user->balance;
                return redirect()->back()->with('error',
                    'Số dư không đủ! Bạn thiếu ' . number_format($shortage) . 'đ. Vui lòng liên hệ admin để nạp tiền.');
            }
        }

        try {
            DB::beginTransaction();

            // Tạo order_number dựa trên timestamp và user_id
            $orderNumber = 'ORD' . date('Ymd') . str_pad($user->id, 4, '0', STR_PAD_LEFT) . substr(str_replace('.', '', microtime(true) * 1000), -3);

            // Tạo đơn hàng
            $order = Order::create([
                'user_id' => $user->id,
                'total_amount' => $finalAmount,
                'status' => 'completed',
                'order_number' => $orderNumber,
            ]);

            if ($isPreorder) {
                // Trường hợp đặt hàng - tạo OrderItem không có tài khoản
                for ($i = 0; $i < $quantity; $i++) {
                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'product_account_id' => null, // Không gán tài khoản
                        'quantity' => 1,
                        'price' => $product->price,
                    ]);
                }
            } else {
                // Logic cũ - có tài khoản available
                $accounts = ProductAccount::where('product_id', $product->id)
                    ->where('status', 'available')
                    ->limit($quantity)
                    ->get();

                // Tạo order items và cập nhật trạng thái tài khoản
                foreach ($accounts as $account) {
                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'product_account_id' => $account->id,
                        'quantity' => 1,
                        'price' => $product->price,
                    ]);

                    // Cập nhật trạng thái tài khoản thành đã bán
                    $account->update([
                        'status' => 'sold',
                        'sold_at' => now(),
                    ]);
                }
            }

            // Áp dụng voucher nếu có
            if ($request->voucher_code && $appliedVoucher) {
                $voucherService->applyVoucherToOrder($request->voucher_code, $user, $order->id);
            }

            // Tạo balance transaction log
            $description = "Mua {$quantity}x {$product->name} - Đơn hàng #{$order->order_number}";
            if ($discountAmount > 0) {
                $description .= " (Giảm " . number_format($discountAmount, 0, ',', '.') . "đ)";
            }

            BalanceTransaction::createTransaction(
                user: $user,
                type: BalanceTransaction::TYPE_PURCHASE,
                amount: -$finalAmount, // Số âm vì trừ tiền
                description: $description,
                reference: $order,
                metadata: [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'unit_price' => $product->price,
                    'total_amount' => $totalAmount,
                    'discount_amount' => $discountAmount,
                    'final_amount' => $finalAmount,
                    'voucher_code' => $request->voucher_code,
                    'order_number' => $order->order_number
                ]
            );

            // Trừ tiền user
            $user->decrement('balance', $finalAmount);

            // Tạo voucher tự động nếu category enable
            $voucherService->createAutoVoucherForProductPurchase($order);

            DB::commit();

            // Kiểm tra nếu là AJAX request thì trả JSON, nếu không thì redirect
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Mua hàng thành công!',
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'redirect' => route('orders.show', $order->id)
                ]);
            } else {
                // Form submission - redirect trực tiếp
                return redirect()->route('orders.show', $order->id)
                    ->with('success', 'Mua hàng thành công! Đơn hàng #' . $order->order_number);
            }

        } catch (\Exception $e) {
            DB::rollback();
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi xử lý đơn hàng: ' . $e->getMessage()
                ], 500);
            } else {
                return redirect()->back()
                    ->with('error', 'Có lỗi xảy ra khi xử lý đơn hàng: ' . $e->getMessage());
            }
        }
    }


}
