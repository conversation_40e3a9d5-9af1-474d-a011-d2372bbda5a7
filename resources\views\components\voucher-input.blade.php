<!-- Voucher Input Component -->
<div class="voucher-section mb-4">
    <div class="flex items-center justify-between mb-2">
        <label class="text-sm font-medium text-gray-700">Mã giảm giá</label>
        <button type="button" onclick="showAvailableVouchers()" class="text-xs text-blue-600 hover:text-blue-800">
            Xem voucher khả dụng
        </button>
    </div>
    
    <div class="flex space-x-2">
        <div class="flex-1">
            <input type="text"
                   id="voucher_code"
                   name="voucher_code"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                   placeholder="Nhập mã voucher"
                   onchange="validateVoucher()"
                   oninput="toggleVoucherApplyButton()">
        </div>
        <button type="button"
                id="applyVoucherBtn"
                onclick="validateVoucher()"
                disabled
                class="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200 text-sm">
            Áp dụng
        </button>
    </div>
    
    <!-- Voucher validation message -->
    <div id="voucher-message" class="mt-2 text-sm hidden"></div>
    
    <!-- Applied voucher display -->
    <div id="applied-voucher" class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md text-sm text-green-800 hidden">
        <div class="flex items-center justify-between">
            <span id="voucher-info"></span>
            <button type="button" onclick="removeVoucher()" class="text-green-600 hover:text-green-800">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<!-- Available Vouchers Modal -->
<div id="vouchersModal" class="fixed inset-0 hidden z-50 flex items-center justify-center p-4" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex items-center justify-between p-4 border-b">
            <h3 class="text-lg font-semibold text-gray-900">Voucher khả dụng</h3>
            <button type="button" onclick="closeVouchersModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="vouchers-list" class="p-4">
            <!-- Vouchers will be loaded here -->
        </div>
    </div>
</div>

<script>
let currentVoucher = null;
let originalTotal = 0;

function showAvailableVouchers() {
    const orderType = '{{ $orderType ?? "product" }}';
    const categoryId = '{{ $categoryId ?? "" }}';
    
    fetch(`/vouchers/available?order_type=${orderType}&category_id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            const vouchersList = document.getElementById('vouchers-list');
            
            if (data.vouchers.length === 0) {
                vouchersList.innerHTML = '<p class="text-gray-500 text-center">Không có voucher khả dụng</p>';
            } else {
                vouchersList.innerHTML = data.vouchers.map(voucher => `
                    <div class="border border-gray-200 rounded-lg p-3 mb-3 cursor-pointer hover:bg-gray-50" onclick="selectVoucher('${voucher.code}')">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="font-medium text-gray-900">${voucher.name}</h4>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                ${voucher.discount_type === 'fixed' ?
                                    `-${new Intl.NumberFormat('de-DE').format(voucher.discount_value)}đ` :
                                    `-${voucher.discount_value}%`
                                }
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">${voucher.description}</p>
                        <div class="text-xs text-gray-500">
                            <div>Mã: <code class="bg-gray-100 px-1 rounded">${voucher.code}</code></div>
                            ${voucher.min_order_amount ? `<div>Đơn tối thiểu: ${new Intl.NumberFormat('de-DE').format(voucher.min_order_amount)}đ</div>` : ''}
                            ${voucher.expires_at ? `<div>Hết hạn: ${voucher.expires_at}</div>` : ''}
                        </div>
                    </div>
                `).join('');
            }
            
            document.getElementById('vouchersModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading vouchers:', error);
        });
}

function closeVouchersModal() {
    document.getElementById('vouchersModal').classList.add('hidden');
}

// Toggle apply button state based on voucher input
function toggleVoucherApplyButton() {
    const voucherInput = document.getElementById('voucher_code');
    const applyBtn = document.getElementById('applyVoucherBtn');
    const hasValue = voucherInput.value.trim().length > 0;

    if (hasValue) {
        applyBtn.disabled = false;
        applyBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm';
    } else {
        applyBtn.disabled = true;
        applyBtn.className = 'px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200 text-sm';
    }
}

function selectVoucher(code) {
    document.getElementById('voucher_code').value = code;
    closeVouchersModal();
    toggleVoucherApplyButton(); // Update button state
    validateVoucher();
}

function validateVoucher() {
    const voucherCode = document.getElementById('voucher_code').value.trim();
    const messageDiv = document.getElementById('voucher-message');
    const applyBtn = document.getElementById('applyVoucherBtn');

    // Prevent validation if button is disabled
    if (applyBtn.disabled) {
        return;
    }

    if (!voucherCode) {
        removeVoucher();
        return;
    }

    const orderType = '{{ $orderType ?? "product" }}';
    const categoryId = '{{ $categoryId ?? "" }}';
    const totalAmount = originalTotal || getCurrentTotal();

    fetch('/vouchers/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            voucher_code: voucherCode,
            order_type: orderType,
            category_id: categoryId,
            total_amount: totalAmount
        })
    })
    .then(response => {
        if (response.status === 429) {
            // Handle rate limiting
            return response.json().then(data => {
                throw new Error(data.message || 'Quá nhiều yêu cầu. Vui lòng thử lại sau.');
            });
        }
        return response.json();
    })
    .then(data => {
        messageDiv.classList.remove('hidden');

        if (data.valid) {
            messageDiv.className = 'mt-2 text-sm text-green-600';
            messageDiv.textContent = 'Voucher hợp lệ!';

            currentVoucher = data;
            showAppliedVoucher(data);
            updateTotal();
        } else {
            messageDiv.className = 'mt-2 text-sm text-red-600';
            messageDiv.textContent = data.message;
            // Không reset input, chỉ clear voucher data và applied voucher display
            currentVoucher = null;
            document.getElementById('applied-voucher').classList.add('hidden');
            if (typeof updateTotal === 'function') {
                updateTotal();
            }
        }
    })
    .catch(error => {
        messageDiv.classList.remove('hidden');
        messageDiv.className = 'mt-2 text-sm text-red-600';

        // Handle different error types
        if (error.message.includes('Quá nhiều yêu cầu')) {
            messageDiv.textContent = error.message;
        } else {
            messageDiv.textContent = 'Có lỗi xảy ra khi kiểm tra voucher';
        }

        // Không reset input khi có lỗi network
        currentVoucher = null;
        document.getElementById('applied-voucher').classList.add('hidden');
        if (typeof updateTotal === 'function') {
            updateTotal();
        }
    });
}

function showAppliedVoucher(voucherData) {
    const appliedDiv = document.getElementById('applied-voucher');
    const infoSpan = document.getElementById('voucher-info');

    const discountText = voucherData.voucher.discount_type === 'fixed' ?
        `-${new Intl.NumberFormat('de-DE').format(voucherData.voucher.discount_value)}đ` :
        `-${voucherData.voucher.discount_value}%`;

    infoSpan.textContent = `${voucherData.voucher.name} (${discountText})`;
    appliedDiv.classList.remove('hidden');
}

function removeVoucher() {
    currentVoucher = null;
    document.getElementById('voucher_code').value = '';
    document.getElementById('voucher-message').classList.add('hidden');
    document.getElementById('applied-voucher').classList.add('hidden');
    toggleVoucherApplyButton(); // Update button state
    updateTotal();
}

function updateTotal() {
    const totalElement = document.getElementById('modalTotal');
    if (!totalElement) return;
    
    let total = originalTotal || getCurrentTotal();
    
    if (currentVoucher) {
        total -= currentVoucher.discount_amount;
    }
    
    totalElement.textContent = new Intl.NumberFormat('de-DE').format(Math.max(0, total)) + 'đ';
}

function getCurrentTotal() {
    // This should be implemented based on your specific total calculation logic
    // For now, return a default value
    return 0;
}

// Initialize original total when component loads
document.addEventListener('DOMContentLoaded', function() {
    const totalElement = document.getElementById('modalTotal');
    if (totalElement) {
        originalTotal = parseInt(totalElement.textContent.replace(/[^\d]/g, ''));
    }
});
</script>
