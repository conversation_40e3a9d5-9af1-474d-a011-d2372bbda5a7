<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ServiceOrder;
use App\Models\Service;
use App\Models\User;

class AdminServiceOrderController extends Controller
{
    /**
     * Display a listing of service orders
     */
    public function index(Request $request)
    {
        $query = ServiceOrder::with(['user', 'service']);

        // Search by user
        if ($request->search) {
            $query->whereHas('user', function($q) use ($request) {
                $q->where('username', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Search by service
        if ($request->service_search) {
            $query->whereHas('service', function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->service_search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'total' => ServiceOrder::count(),
            'pending' => ServiceOrder::where('status', 'pending')->count(),
            'completed' => ServiceOrder::where('status', 'completed')->count(),
            'cancelled' => ServiceOrder::where('status', 'cancelled')->count(),
            'total_revenue' => ServiceOrder::where('status', 'completed')->sum('price'),
        ];

        return view('admin.service-orders.index', compact('orders', 'stats'));
    }

    /**
     * Display the specified service order
     */
    public function show(ServiceOrder $serviceOrder)
    {
        $serviceOrder->load(['user', 'service', 'usedVoucher']);
        return view('admin.service-orders.show', compact('serviceOrder'));
    }

    /**
     * Update service order status
     */
    public function updateStatus(Request $request, ServiceOrder $serviceOrder)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,cancelled',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        $oldStatus = $serviceOrder->status;
        $newStatus = $request->status;

        // Update status
        if ($newStatus === 'completed') {
            $serviceOrder->markAsCompleted($request->admin_notes);
        } else {
            $serviceOrder->update([
                'status' => $newStatus,
                'admin_notes' => $request->admin_notes,
                'completed_at' => $newStatus === 'completed' ? now() : null
            ]);
        }

        // Log status change
        $statusText = [
            'pending' => 'Chờ xử lý',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy'
        ];

        $message = "Trạng thái đơn hàng đã được cập nhật từ '{$statusText[$oldStatus]}' thành '{$statusText[$newStatus]}'";

        return redirect()->back()->with('success', $message);
    }

    /**
     * Bulk update status
     */
    public function bulkUpdateStatus(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:service_orders,id',
            'status' => 'required|in:pending,completed,cancelled'
        ]);

        $count = ServiceOrder::whereIn('id', $request->order_ids)
            ->update(['status' => $request->status]);

        return redirect()->back()->with('success', "Đã cập nhật trạng thái cho {$count} đơn hàng");
    }

    /**
     * Export service orders
     */
    public function export(Request $request)
    {
        $query = ServiceOrder::with(['user', 'service']);

        // Apply same filters as index
        if ($request->status) {
            $query->where('status', $request->status);
        }
        if ($request->service_id) {
            $query->where('service_id', $request->service_id);
        }
        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->orderBy('created_at', 'desc')->get();

        $filename = 'service_orders_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'User', 'Email', 'Service', 'Price', 'Status',
                'Content', 'Notes', 'Admin Notes', 'Created At', 'Completed At'
            ]);

            // CSV data
            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->id,
                    $order->user->username,
                    $order->user->email,
                    $order->service->name,
                    $order->price,
                    $order->status_text,
                    $order->content,
                    $order->notes,
                    $order->admin_notes,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->completed_at ? $order->completed_at->format('Y-m-d H:i:s') : ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
