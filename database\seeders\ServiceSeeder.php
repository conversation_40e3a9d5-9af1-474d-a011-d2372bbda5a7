<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Service;
use App\Models\Category;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mlCategory = Category::where('slug', 'mobile-legends')->first();
        $ffCategory = Category::where('slug', 'free-fire')->first();

        $services = [
            [
                'name' => 'Dịch vụ Push Rank Mobile Legends',
                'description' => 'Dịch vụ hỗ trợ push rank từ Epic lên Mythic',
                'order_instructions' => 'Vui lòng cung cấp tài khoản và mật khẩu game. Thời gian hoàn thành: 3-5 ngày.',
                'price' => 200000,
                'category_id' => $mlCategory->id,
                'images' => [],
                'status' => 'active',
            ],
            [
                'name' => 'Dịch vụ Nạp Kim Cương Free Fire',
                'description' => 'Dịch vụ nạp kim cương Free Fire giá rẻ',
                'order_instructions' => 'Cung cấp ID game và server. Nạp trong vòng 24h.',
                'price' => 100000,
                'category_id' => $ffCategory->id,
                'images' => [],
                'status' => 'active',
            ],
            [
                'name' => 'Dịch vụ Thuê Acc Mobile Legends',
                'description' => 'Cho thuê tài khoản Mobile Legends VIP theo giờ',
                'order_instructions' => 'Thuê tối thiểu 2 giờ. Không được đổi mật khẩu.',
                'price' => 50000,
                'category_id' => $mlCategory->id,
                'images' => [],
                'status' => 'active',
            ],
        ];

        foreach ($services as $service) {
            Service::create($service);
        }
    }
}
