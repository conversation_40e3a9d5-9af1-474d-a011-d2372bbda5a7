@extends('layouts.app')

@section('title', 'D<PERSON><PERSON> vụ - AccReroll')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">D<PERSON><PERSON> vụ</h1>
        <p class="text-gray-600">Các dịch vụ hỗ trợ game chuyên nghi<PERSON></p>
    </div>

    <!-- Filters & Search -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
        <form method="GET" class="space-y-4 md:space-y-0 md:flex md:items-center md:space-x-4">
            <!-- Search -->
            <div class="flex-1">
                <input type="text" 
                       name="search" 
                       value="{{ request('search') }}"
                       placeholder="Tìm kiếm dịch vụ..." 
                       class="w-full px-4 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Category Filter -->
            <div class="md:w-48">
                <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Tất cả danh mục</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Sort -->
            <div class="md:w-48">
                <select name="sort" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Tên A-Z</option>
                    <option value="price" {{ request('sort') == 'price' ? 'selected' : '' }}>Giá thấp đến cao</option>
                </select>
                <input type="hidden" name="order" value="{{ request('sort') == 'price' ? 'asc' : 'asc' }}">
            </div>

            <!-- Submit -->
            <button type="submit" class="w-full md:w-auto bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Tìm kiếm
            </button>
        </form>
    </div>

    <!-- Services Grid -->
    @if($services->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            @foreach($services as $service)
                <x-service-card :service="$service" />
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="flex justify-center">
            {{ $services->appends(request()->query())->links('pagination.custom') }}
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="text-gray-400 text-6xl mb-4">
                <i class="fas fa-tools"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy dịch vụ</h3>
            <p class="text-gray-600 mb-4">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
            <a href="{{ route('services.index') }}" class="text-blue-600 hover:text-blue-700">
                Xem tất cả dịch vụ
            </a>
        </div>
    @endif
</div>
@endsection
