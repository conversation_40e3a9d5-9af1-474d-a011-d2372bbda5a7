@extends('layouts.admin')

@section('title', 'Quản lý Voucher')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Quản lý Voucher</h1>
            <p class="text-gray-600 mt-1">Quản lý mã giảm giá và khuyến mãi cho khách hàng</p>
        </div>
        <div class="flex-shrink-0">
            <a href="{{ route('admin.vouchers.create') }}" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>T<PERSON>o Voucher
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex flex-wrap gap-2">
            <a href="{{ route('admin.vouchers.index') }}"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors {{ !request('status') ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Tất cả
            </a>
            <a href="{{ route('admin.vouchers.index', ['status' => 'active']) }}"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors {{ request('status') === 'active' ? 'bg-green-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Hoạt động
            </a>
            <a href="{{ route('admin.vouchers.index', ['status' => 'inactive']) }}"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors {{ request('status') === 'inactive' ? 'bg-yellow-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Tạm dừng
            </a>
            <a href="{{ route('admin.vouchers.index', ['status' => 'archived']) }}"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors {{ request('status') === 'archived' ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Đã lưu trữ
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <span>{{ session('success') }}</span>
            </div>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <!-- Vouchers Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Voucher</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Loại</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Giảm giá</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Áp dụng</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sử dụng</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trạng thái</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Hành động</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($vouchers as $voucher)
                        <tr>
                            <td class="px-4 py-3 whitespace-nowrap">{{ $voucher->id }}</td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $voucher->name }}</div>
                                    <div class="text-xs text-gray-500">{{ $voucher->code }}</div>
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $voucher->type === 'manual' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                    {{ $voucher->type === 'manual' ? 'Thủ công' : 'Tự động' }}
                                </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                @if($voucher->discount_type === 'fixed')
                                    <span class="font-medium">{{ number_format($voucher->discount_value, 0, ',', '.') }}đ</span>
                                @else
                                    <span class="font-medium">{{ $voucher->discount_value }}%</span>
                                @endif
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div>
                                    @if($voucher->applicable_to === 'products')
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                            <i class="fas fa-box mr-1"></i>Sản phẩm
                                        </span>
                                    @elseif($voucher->applicable_to === 'services')
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                            <i class="fas fa-cogs mr-1"></i>Dịch vụ
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                                            <i class="fas fa-globe mr-1"></i>Tất cả
                                        </span>
                                    @endif
                                    @if($voucher->category)
                                        <div class="text-xs text-gray-400 mt-1">{{ $voucher->category->name }}</div>
                                    @endif
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                @php
                                    $totalGranted = $voucher->user_vouchers_count ?? 0;
                                    $totalUsed = $voucher->used_count ?? 0;
                                @endphp
                                <div class="text-sm font-medium">{{ $totalUsed }}/{{ $totalGranted }}</div>
                                @if($totalGranted > 0)
                                    <div class="text-xs text-gray-400">({{ round(($totalUsed / $totalGranted) * 100, 1) }}%)</div>
                                @else
                                    <div class="text-xs text-gray-400">(0%)</div>
                                @endif
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                @if($voucher->trashed())
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-archive mr-1"></i>Đã lưu trữ
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {{ $voucher->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        <i class="fas fa-{{ $voucher->is_active ? 'check-circle' : 'pause-circle' }} mr-1"></i>
                                        {{ $voucher->is_active ? 'Hoạt động' : 'Tạm dừng' }}
                                    </span>
                                @endif
                                @if($voucher->expires_at && $voucher->expires_at < now())
                                    <div class="mt-1">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-clock mr-1"></i>Hết hạn
                                        </span>
                                    </div>
                                @endif
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex space-x-2">
                                    @if($voucher->trashed())
                                        <!-- Voucher đã archived -->
                                        <form method="POST" action="{{ route('admin.vouchers.restore', $voucher->id) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-green-600 hover:text-green-900" title="Khôi phục">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </form>

                                        <form method="POST" action="{{ route('admin.vouchers.force-delete', $voucher->id) }}" class="inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn XÓA VĨNH VIỄN voucher này? Hành động này không thể hoàn tác!')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-900" title="Xóa vĩnh viễn">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                    @else
                                        <!-- Voucher active -->
                                        <a href="{{ route('admin.vouchers.edit', $voucher) }}" class="text-indigo-600 hover:text-indigo-900" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <form method="POST" action="{{ route('admin.vouchers.toggle-status', $voucher) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-yellow-600 hover:text-yellow-900" title="{{ $voucher->is_active ? 'Tạm dừng' : 'Kích hoạt' }}">
                                                <i class="fas fa-{{ $voucher->is_active ? 'pause' : 'play' }}"></i>
                                            </button>
                                        </form>

                                        @if($voucher->type === 'manual')
                                            <form method="POST" action="{{ route('admin.vouchers.destroy', $voucher) }}" class="inline"
                                                  onsubmit="return confirm('Bạn có chắc chắn muốn lưu trữ (archive) voucher này?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-orange-600 hover:text-orange-900" title="Lưu trữ">
                                                    <i class="fas fa-archive"></i>
                                                </button>
                                            </form>
                                        @endif
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="px-4 py-8 text-center">
                                <div class="text-gray-400">
                                    <i class="fas fa-ticket-alt text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">Chưa có voucher nào</p>
                                    <p class="text-sm">Tạo voucher đầu tiên để bắt đầu</p>
                                </div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if($vouchers->hasPages())
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $vouchers->links('pagination.custom') }}
        </div>
    @endif
</div>
@endsection
