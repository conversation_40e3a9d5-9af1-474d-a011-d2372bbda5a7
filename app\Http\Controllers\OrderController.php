<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    /**
     * <PERSON><PERSON><PERSON> thị danh sách đơn hàng của user
     */
    public function index()
    {
        $orders = Order::with(['orderItems.product', 'orderItems.productAccount'])
            ->where('user_id', Auth::id())
            ->latest()
            ->paginate(10);

        return view('orders.index', compact('orders'));
    }

    /**
     * Hiển thị chi tiết đơn hàng
     */
    public function show(Order $order)
    {
        // <PERSON><PERSON>m tra quyền truy cập
        if ($order->user_id != Auth::id()) {
            abort(404);
        }

        $order->load(['orderItems.product', 'orderItems.productAccount', 'usedVoucher']);

        return view('orders.show', compact('order'));
    }
}
