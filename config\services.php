<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'brevo' => [
        'api_key' => env('BREVO_API_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'sepay' => [
        'api_url' => env('SEPAY_API_URL'),
        'api_key' => env('SEPAY_API_KEY'),
        'account_number' => env('SEPAY_ACCOUNT_NUMBER'),
        'pattern' => env('SEPAY_PATTERN', 'guidang'),
    ],

    'card_api' => [
        'url' => env('CARD_API_URL', 'https://gachthefast.com/chargingws/v2'),
        'partner_id' => env('CARD_PARTNER_ID', '**********'),
        'partner_key' => env('CARD_PARTNER_KEY', 'cd636161a39d2a63a7c1869af09cd31b'),
        'wallet_id' => env('CARD_WALLET_ID', '**********'),

        // API Settings
        'timeout' => env('CARD_API_TIMEOUT', 30),
        'retry_attempts' => env('CARD_API_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('CARD_API_RETRY_DELAY', 5),

        // Supported telcos and amounts
        'supported_telcos' => [
            'VIETTEL' => [
                'name' => 'Viettel',
                'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000]
            ],
            'MOBIFONE' => [
                'name' => 'Mobifone',
                'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000]
            ],
            'VINAPHONE' => [
                'name' => 'Vinaphone',
                'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000]
            ],
            'Zing' => [
                'name' => 'Zing',
                'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000]
            ]
        ],

        // Status messages
        'status_messages' => [
            1 => 'Thành công',
            2 => 'Sai mệnh giá',
            3 => 'Thẻ lỗi',
            4 => 'Hệ thống bảo trì',
            99 => 'Thẻ chờ xử lý',
            100 => 'Gửi thẻ thất bại - Có thể do kiểm tra lại thông tin',
        ],
    ],

];
