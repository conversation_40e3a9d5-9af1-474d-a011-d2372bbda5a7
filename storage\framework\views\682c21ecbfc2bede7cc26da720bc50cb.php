<header class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
    <!-- Main Navigation -->
    <nav class="py-4">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center h-10">
                    <a class="flex items-center justify-center w-25" href="<?php echo e(route('home')); ?>">
                        <img src="<?php echo e(asset('logo.png')); ?>"  alt="AccReroll">
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <button class="lg:hidden flex items-center px-3 py-2 border rounded text-gray-500 border-gray-600 hover:text-gray-900 hover:border-gray-900" type="button" onclick="toggleMobileMenu()">
                    <svg class="fill-current h-3 w-3" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <title>Menu</title>
                        <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"/>
                    </svg>
                </button>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-4 flex-1 justify-center">
                    <!-- Navigation Links -->
                    <div class="flex items-center space-x-5">
                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200 font-medium <?php echo e(request()->routeIs('home') ? 'text-blue-600' : ''); ?>" href="<?php echo e(route('home')); ?>">
                            <i class="fas fa-home mr-2"></i>Trang chủ
                        </a>

                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200 font-medium" href="<?php echo e(route('topup.index')); ?>">
                            <i class="fas fa-wallet mr-2"></i>Nạp tiền
                        </a>

                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200 font-medium <?php echo e(request()->routeIs('guides.*') || request()->routeIs('guide.*') ? 'text-blue-600' : ''); ?>" href="<?php echo e(route('guides.index')); ?>">
                            <i class="fas fa-book mr-2"></i>Hướng dẫn
                        </a>

                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200 font-medium" href="https://www.facebook.com/profile.php?id=61578528443379" target="_blank">
                            <i class="fab fa-facebook mr-2"></i>Liên hệ
                        </a>
                    </div>
                </div>

                <!-- Right Side: Search and Auth -->
                <div class="hidden lg:flex items-center space-x-3 flex-shrink-0">

                    <!-- Search Form -->
                    <div class="relative">
                        <input class="block w-80 rounded-md border-2 border-blue-200 bg-white px-4 py-2 text-sm focus:!border-blue-200 focus:!ring-0 focus:outline-none pr-10 shadow-sm"
                               type="search"
                               name="q"
                               placeholder="Tìm game..."
                               autocomplete="off">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-search text-blue-400 text-sm"></i>
                        </div>
                    </div>

                    <!-- Authentication -->
                    <?php if(auth()->guard()->guest()): ?>
                        <div class="flex space-x-3">
                            <a href="<?php echo e(route('login')); ?>" class="inline-flex items-center px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 transition-colors duration-200 font-medium">
                                <i class="fas fa-sign-in-alt mr-2"></i>Đăng nhập
                            </a>
                            <a href="<?php echo e(route('register')); ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium">
                                <i class="fas fa-user-plus mr-2"></i>Đăng ký
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="relative">
                            <button onclick="toggleDropdown()" class="flex items-center px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors duration-200 border border-gray-200 font-medium" type="button" id="user-dropdown-button">
                                <i class="fas fa-user mr-2"></i>
                                <span class="font-medium"><?php echo e(Auth::user()->display_name); ?></span>
                                <i class="fas fa-chevron-down ml-2 text-xs"></i>
                            </button>
                            <div id="user-dropdown" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                <div class="px-4 py-2 border-b border-gray-100">
                                    <small class="text-green-600 font-semibold">Số dư: <?php echo e(format_money((float)Auth::user()->balance)); ?></small>
                                </div>
                                <a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:translate-x-1 transition-all duration-200" href="<?php echo e(route('profile.index')); ?>">
                                    <i class="fas fa-user mr-3 text-blue-600 w-4"></i>Thông tin cá nhân
                                </a>
                            
                                <a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:translate-x-1 transition-all duration-200" href="<?php echo e(route('topup.index')); ?>">
                                    <i class="fas fa-wallet mr-3 text-blue-600 w-4"></i>Nạp tiền
                                </a>
                                <a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:translate-x-1 transition-all duration-200" href="<?php echo e(route('topup.history')); ?>">
                                    <i class="fas fa-history mr-3 text-blue-600 w-4"></i>Lịch sử nạp tiền
                                </a>

                                <a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:translate-x-1 transition-all duration-200" href="<?php echo e(route('orders.index')); ?>">
                                    <i class="fas fa-shopping-bag mr-3 text-blue-600 w-4"></i>Đơn hàng
                                </a>
                                <a class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-50 hover:translate-x-1 transition-all duration-200" href="<?php echo e(route('service-orders.index')); ?>">
                                    <i class="fas fa-cogs mr-3 text-blue-600 w-4"></i>Đơn dịch vụ
                                </a>
                                <div class="border-t border-gray-100 mt-1 pt-1">
                                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="w-full">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="flex items-center w-full px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200">
                                            <i class="fas fa-sign-out-alt mr-3 w-4"></i>Đăng xuất
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div id="mobile-menu" class="lg:hidden hidden mt-4 border-t border-gray-200 pt-4">
                <!-- Mobile Search -->
                <div class="relative mb-4">
                    <input class="block w-full rounded-md border-2 border-blue-200 bg-white px-4 py-2 focus:!border-blue-200 focus:!ring-0 focus:outline-none pr-10 shadow-sm"
                           type="search"
                           name="q"
                           placeholder="Tìm game..."
                           autocomplete="off">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="fas fa-search text-blue-400"></i>
                    </div>
                </div>

                <?php if(auth()->guard()->check()): ?>
                    <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-user mr-2 text-blue-600"></i>
                                <span class="font-semibold text-gray-900"><?php echo e(Auth::user()->display_name); ?></span>
                            </div>
                            <span class="text-green-600 font-semibold">(<?php echo e(format_money((float)Auth::user()->balance)); ?>)</span>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="space-y-2">
                    <a class="flex items-center px-3 py-2 text-gray-700 rounded-md <?php echo e(request()->routeIs('home') ? 'text-blue-600' : ''); ?>" href="<?php echo e(route('home')); ?>">
                        <i class="fas fa-home mr-2"></i>Trang chủ
                    </a>

                    <a class="flex items-center px-3 py-2 text-gray-700 rounded-md" href="<?php echo e(route('topup.index')); ?>">
                        <i class="fas fa-wallet mr-2"></i>Nạp tiền
                    </a>

                    <a class="flex items-center px-3 py-2 text-gray-700 rounded-md" href="<?php echo e(route('guides.index')); ?>">
                        <i class="fas fa-book mr-2"></i>Hướng dẫn
                    </a>

                    <a class="flex items-center px-3 py-2 text-gray-700 rounded-md" href="https://www.facebook.com/profile.php?id=61578528443379" target="_blank">
                        <i class="fab fa-facebook mr-2"></i>Liên hệ
                    </a>

                    <?php if(auth()->guard()->check()): ?>
                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" href="<?php echo e(route('profile.index')); ?>">
                            <i class="fas fa-user mr-2 text-blue-600"></i>Thông tin cá nhân
                        </a>
                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" href="<?php echo e(route('topup.history')); ?>">
                            <i class="fas fa-history mr-2 text-blue-600"></i>Lịch sử nạp tiền
                        </a>

                         <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" href="<?php echo e(route('orders.index')); ?>">
                            <i class="fas fa-shopping-bag mr-2 text-blue-600"></i>Đơn hàng
                        </a>
                        <a class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md" href="<?php echo e(route('service-orders.index')); ?>">
                            <i class="fas fa-cogs mr-2 text-blue-600"></i>Đơn dịch vụ
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Mobile Auth buttons -->
                <?php if(auth()->guard()->guest()): ?>
                    <div class="mt-4 space-y-2">
                        <a href="<?php echo e(route('login')); ?>" class="block w-full text-center px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50">
                            <i class="fas fa-sign-in-alt mr-2"></i>Đăng nhập
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="block w-full text-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <i class="fas fa-user-plus mr-2"></i>Đăng ký
                        </a>
                    </div>
                <?php else: ?>
                    <div class="mt-4">
                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="w-full">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="block w-full text-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                <i class="fas fa-sign-out-alt mr-2"></i>Đăng xuất
                            </button>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('hidden');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('user-dropdown');
            const button = document.getElementById('user-dropdown-button');

            if (dropdown && button && !button.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });
    </script>
</header>
<?php /**PATH /home/<USER>/rainshop/resources/views/layouts/header.blade.php ENDPATH**/ ?>