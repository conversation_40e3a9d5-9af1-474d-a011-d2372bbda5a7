

<?php $__env->startSection('title', 'Sản phẩm'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Sản phẩm</h1>
            <p class="text-gray-600 mt-1">Quản lý danh sách sản phẩm và tài khoản game</p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo e(route('admin.products.create')); ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Thêm mới
            </a>
        </div>
    </div>

    <!-- Search & Filters -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <form method="GET" class="flex flex-wrap items-center gap-3">
            <!-- Search by product name -->
            <div class="flex-1 min-w-0 sm:min-w-64">
                <input type="text" name="search" value="<?php echo e(request('search')); ?>"
                       placeholder="Tìm sản phẩm..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Category search -->
            <div class="flex-1 min-w-0 sm:min-w-64 relative">
                <input type="hidden" name="category_id" id="category_id" value="<?php echo e(request('category_id')); ?>">
                <input type="text" id="category_search"
                       placeholder="Tìm danh mục..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       autocomplete="off">
                <div id="category_dropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden max-h-60 overflow-y-auto">
                    <!-- Dropdown items will be populated by JavaScript -->
                </div>
            </div>

            <!-- Stock status filter -->
            <div class="min-w-0 sm:min-w-40">
                <select name="stock_status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Tất cả</option>
                    <option value="in_stock" <?php echo e(request('stock_status') === 'in_stock' ? 'selected' : ''); ?>>Còn hàng</option>
                    <option value="preorder" <?php echo e(request('stock_status') === 'preorder' ? 'selected' : ''); ?>>Đặt hàng</option>
                    <option value="out_of_stock" <?php echo e(request('stock_status') === 'out_of_stock' ? 'selected' : ''); ?>>Hết hàng</option>
                </select>
            </div>

            <!-- Action buttons -->
            <div class="flex gap-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-search"></i>
                </button>
                <?php if(request()->hasAny(['search', 'category_id', 'stock_status'])): ?>
                    <a href="<?php echo e(route('admin.products.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-times"></i>
                    </a>
                <?php endif; ?>
            </div>

            <!-- Selected category indicator -->
            <?php if(request('category_id')): ?>
                <div class="w-full sm:w-auto">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-filter mr-1"></i>
                        Đã lọc theo danh mục
                    </span>
                </div>
            <?php endif; ?>
        </form>
    </div>
    <!-- Products Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            ID
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Sản phẩm
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Danh mục
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Giá
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Số lượng
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Trạng thái
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Thao tác
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $products ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($product->id ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($product->name ?? 'N/A'); ?></div>
                                <div class="text-sm text-gray-500 truncate max-w-xs" title="<?php echo e($product->slug ?? 'N/A'); ?>">
                                    <?php echo e($product->slug ?? 'N/A'); ?>

                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo e($product->category->name ?? 'Chưa phân loại'); ?>

                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo e(format_money($product->price ?? 0)); ?>

                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php
                                    $stockCount = $product->available_quantity ?? 0;
                                ?>
                                <?php if($stockCount > 0): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <?php echo e($stockCount); ?> tài khoản
                                    </span>
                                <?php elseif(($product->allow_preorder ?? false)): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <i class="fas fa-clock mr-1"></i>Đặt hàng
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Hết hàng
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php if(($product->status ?? 'active') == 'active'): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-eye mr-1"></i>Hiển thị
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-eye-slash mr-1"></i>Ẩn
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.products.edit', $product->id ?? 0)); ?>"
                                       class="text-blue-600 hover:text-blue-900 transition-colors" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.products.accounts', $product)); ?>"
                                       class="text-purple-600 hover:text-purple-900 transition-colors" title="Quản lý tài khoản">
                                        <i class="fas fa-user-cog"></i>
                                    </a>
                                    <button onclick="confirmDelete(<?php echo e($product->id ?? 0); ?>)"
                                            class="text-red-600 hover:text-red-900 transition-colors" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-4 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-box text-4xl mb-4 block"></i>
                                    <p class="text-lg">Chưa có sản phẩm nào</p>
                                    <p class="text-sm">Hãy thêm sản phẩm đầu tiên</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if(isset($products) && $products->hasPages()): ?>
            <div class="px-4 py-3 border-t border-gray-200">
                <?php echo e($products->withQueryString()->links('pagination.custom')); ?>

            </div>
        <?php endif; ?>
    </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmDelete(productId) {
    if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
        // Create form to delete
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo e(url(env('ADMIN_SECRET_KEY', 'admin-secret') . '/admin/products')); ?>/${productId}`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Category search autocomplete
document.addEventListener('DOMContentLoaded', function() {
    const categorySearch = document.getElementById('category_search');
    const categoryId = document.getElementById('category_id');
    const dropdown = document.getElementById('category_dropdown');
    let searchTimeout;

    // Load selected category name if exists
    if (categoryId.value) {
        loadSelectedCategory(categoryId.value);
    }

    categorySearch.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);

        if (query.length < 2) {
            hideDropdown();
            return;
        }

        searchTimeout = setTimeout(() => {
            searchCategories(query);
        }, 300);
    });

    categorySearch.addEventListener('focus', function() {
        if (this.value.length >= 2) {
            searchCategories(this.value);
        }
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!categorySearch.contains(e.target) && !dropdown.contains(e.target)) {
            hideDropdown();
        }
    });

    function searchCategories(query) {
        fetch(`<?php echo e(route('api.admin.search.categories')); ?>?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                showDropdown(data);
            })
            .catch(error => {
                console.error('Error:', error);
                hideDropdown();
            });
    }

    function showDropdown(categories) {
        dropdown.innerHTML = '';

        if (categories.length === 0) {
            dropdown.innerHTML = '<div class="px-3 py-2 text-sm text-gray-500">Không tìm thấy danh mục nào</div>';
        } else {
            categories.forEach(category => {
                const item = document.createElement('div');
                item.className = 'px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm';
                item.innerHTML = `
                    <div class="font-medium text-gray-900">${category.name}</div>
                    <div class="text-xs text-gray-500">${category.publisher}</div>
                `;
                item.addEventListener('click', () => selectCategory(category));
                dropdown.appendChild(item);
            });
        }

        dropdown.classList.remove('hidden');
    }

    function hideDropdown() {
        dropdown.classList.add('hidden');
    }

    function selectCategory(category) {
        categorySearch.value = category.display;
        categoryId.value = category.id;
        hideDropdown();

        // Auto submit form
        categorySearch.closest('form').submit();
    }

    function loadSelectedCategory(id) {
        // You could make an API call here to get category name
        // For now, just show that a category is selected
        if (id) {
            categorySearch.placeholder = 'Danh mục đã được chọn - nhập để thay đổi';
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/admin/products/index.blade.php ENDPATH**/ ?>