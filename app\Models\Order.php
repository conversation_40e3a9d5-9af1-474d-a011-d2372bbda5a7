<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $fillable = [
        'user_id',
        'order_number',
        'total_amount',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'total_amount' => 'decimal:2',
        ];
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function usedVoucher()
    {
        return $this->hasOne(UserVoucher::class, 'order_id')->with('voucher');
    }

    // Helper methods
    public function generateOrderNumber()
    {
        return 'ORD' . date('Ymd') . str_pad($this->id, 4, '0', STR_PAD_LEFT);
    }
}
