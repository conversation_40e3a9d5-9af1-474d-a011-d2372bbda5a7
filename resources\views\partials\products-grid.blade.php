@if($latestProducts->count() > 0)
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        @foreach($latestProducts as $product)
            <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                <!-- Product Image -->
                <div class="relative">
                    @if($product->main_image)
                        <img src="{{ storage_url($product->main_image) }}"
                             alt="{{ $product->name }}"
                             class="w-full h-40 object-contain">
                    @else
                        <div class="w-full h-40 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-gamepad text-white text-3xl"></i>
                        </div>
                    @endif
                </div>

                <!-- Product Info -->
                <div class="p-4 flex flex-col flex-1">
                    <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2">{{ $product->name }}</h3>
                    <p class="text-sm text-gray-500 mb-3">{{ $product->category->name }}</p>

                    <!-- Bottom section - always at bottom -->
                    <div class="mt-auto">
                        @if($product->available_quantity > 0)
                            <div class="text-green-600 font-semibold text-sm mb-2">
                                Sẵn Có: {{ $product->available_quantity }}
                            </div>
                        @elseif($product->allow_preorder)
                            <div class="font-semibold text-sm mb-2" style="color: #0ea5e9;">
                                Còn hàng (nhận mã đơn)
                            </div>
                        @else
                            <div class="text-red-600 font-semibold text-sm mb-2">
                                Hết hàng
                            </div>
                        @endif
                        <div class="text-orange-600 font-bold text-xl">{{ number_format($product->price, 0, ',', '.') }} đ</div>
                    </div>
                </div>

                <!-- Clickable overlay -->
                <a href="{{ route('product.show', $product->slug) }}" class="absolute inset-0 z-10"></a>
            </div>
        @endforeach
    </div>

    <!-- Pagination -->
    @if($latestProducts->hasPages())
        <div class="mt-8">
            {{ $latestProducts->appends(request()->query())->links('pagination.custom') }}
        </div>
    @endif
@else
    <div class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
            <i class="fas fa-box-open"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Chưa có sản phẩm nào</h3>
        <p class="text-gray-500">Hãy quay lại sau để xem các sản phẩm mới nhất!</p>
    </div>
@endif
