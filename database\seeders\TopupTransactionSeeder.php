<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TopupTransaction;
use App\Models\User;

class TopupTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testUser = User::where('username', 'testuser')->first();
        $demoUser = User::where('username', 'demo')->first();

        $topupTransactions = [
            // ATM transaction - completed
            [
                'method' => TopupTransaction::METHOD_ATM,
                'user_id' => $testUser->id,
                'amount' => 100000,
                'status' => TopupTransaction::STATUS_COMPLETED,
                'transaction_code' => 'ATM' . time() . '001',
                'note' => 'Nạp tiền qua ATM Vietcombank',
                // SePay fields for ATM
                'sepay_id' => 'SP' . time() . '001',
                'gateway' => 'VCB',
                'transactionDate' => now()->format('Y-m-d H:i:s'),
                'accountNumber' => '**********',
                'subAccount' => null,
                'code' => 'VCB001',
                'content' => 'ACCREROLL ' . $testUser->id,
                'transferType' => 'in',
                'description' => 'Nap tien qua ATM',
                'referenceCode' => 'REF001',
            ],
            
            // Card transaction - completed
            [
                'method' => TopupTransaction::METHOD_CARD,
                'user_id' => $demoUser->id,
                'amount' => 50000,
                'status' => TopupTransaction::STATUS_COMPLETED,
                'transaction_code' => 'CARD' . time() . '001',
                'note' => 'Nạp thẻ cào Viettel 50k',
                // Card fields
                'card_type' => 'Viettel',
                'card_serial' => '**********1234',
                'card_code' => '**********12345',
            ],
            
            // ATM transaction - pending
            [
                'method' => TopupTransaction::METHOD_ATM,
                'user_id' => $testUser->id,
                'amount' => 200000,
                'status' => TopupTransaction::STATUS_PENDING,
                'transaction_code' => 'ATM' . time() . '002',
                'note' => 'Nạp tiền qua ATM - đang chờ xử lý',
                // SePay fields for ATM
                'sepay_id' => 'SP' . time() . '002',
                'gateway' => 'TCB',
                'transactionDate' => now()->format('Y-m-d H:i:s'),
                'accountNumber' => '**********',
                'subAccount' => null,
                'code' => 'TCB001',
                'content' => 'ACCREROLL ' . $testUser->id,
                'transferType' => 'in',
                'description' => 'Nap tien qua ATM Techcombank',
                'referenceCode' => 'REF002',
            ],
            
            // Card transaction - failed
            [
                'method' => TopupTransaction::METHOD_CARD,
                'user_id' => $demoUser->id,
                'amount' => 100000,
                'status' => TopupTransaction::STATUS_FAILED,
                'transaction_code' => 'CARD' . time() . '002',
                'note' => 'Thẻ cào không hợp lệ',
                // Card fields
                'card_type' => 'Mobifone',
                'card_serial' => '**********9876',
                'card_code' => '***************',
            ],
            
            // Card transaction - pending
            [
                'method' => TopupTransaction::METHOD_CARD,
                'user_id' => $testUser->id,
                'amount' => 20000,
                'status' => TopupTransaction::STATUS_PENDING,
                'transaction_code' => 'CARD' . time() . '003',
                'note' => 'Nạp thẻ cào Vinaphone - đang xử lý',
                // Card fields
                'card_type' => 'Vinaphone',
                'card_serial' => '11111111111111',
                'card_code' => '222222222222222',
            ],
        ];

        foreach ($topupTransactions as $transaction) {
            TopupTransaction::create($transaction);
        }
    }
}
