<?php

namespace App\Services;

use App\Models\BalanceTransaction;
use App\Models\Order;
use App\Models\ServiceOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RevenueService
{
    /**
     * Lấy tổng quan doanh thu theo các nguồn
     */
    public function getRevenueOverview(Carbon $startDate = null, Carbon $endDate = null): array
    {
        $query = function ($model) use ($startDate, $endDate) {
            $q = $model::query();
            if ($startDate) {
                $q->whereDate('created_at', '>=', $startDate);
            }
            if ($endDate) {
                $q->whereDate('created_at', '<=', $endDate);
            }
            return $q;
        };

        // 1. <PERSON>anh thu thực (tiền thực sự vào hệ thống)
        $topupRevenue = $query(BalanceTransaction::class)
            ->whereIn('type', [
                BalanceTransaction::TYPE_TOPUP_ATM,
                BalanceTransaction::TYPE_TOPUP_CARD
            ])
            ->sum('amount');

        // 2. <PERSON><PERSON>h số bán sản phẩm (luân chuyển tiền trong hệ thống)
        $productRevenue = $query(Order::class)
            ->where('status', 'completed')
            ->sum('total_amount');

        // 3. Doanh số dịch vụ (luân chuyển tiền trong hệ thống)
        $serviceRevenue = $query(ServiceOrder::class)
            ->where('status', 'completed')
            ->sum('price');

        // 4. Tổng doanh số kinh doanh (không phải doanh thu thực)
        $businessRevenue = $productRevenue + $serviceRevenue;

        // 5. Doanh thu thực = chỉ tiền nạp vào
        $totalRevenue = $topupRevenue;

        return [
            'topup_revenue' => $topupRevenue, // Doanh thu thực
            'product_revenue' => $productRevenue, // Doanh số sản phẩm
            'service_revenue' => $serviceRevenue, // Doanh số dịch vụ
            'business_revenue' => $businessRevenue, // Tổng doanh số kinh doanh
            'total_revenue' => $totalRevenue, // Doanh thu thực = topup_revenue
            'breakdown' => [
                'topup_percentage' => 100, // Doanh thu thực luôn là 100%
                'product_percentage' => $topupRevenue > 0 ? round(($productRevenue / $topupRevenue) * 100, 2) : 0, // % so với doanh thu thực
                'service_percentage' => $topupRevenue > 0 ? round(($serviceRevenue / $topupRevenue) * 100, 2) : 0, // % so với doanh thu thực
            ]
        ];
    }

    /**
     * Lấy doanh thu theo ngày trong khoảng thời gian
     */
    public function getDailyRevenue(Carbon $startDate, Carbon $endDate): array
    {
        $data = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dayData = $this->getRevenueOverview($currentDate, $currentDate);
            
            $data[] = [
                'date' => $currentDate->format('Y-m-d'),
                'date_formatted' => $currentDate->format('d/m'),
                'topup_revenue' => $dayData['topup_revenue'],
                'product_revenue' => $dayData['product_revenue'],
                'service_revenue' => $dayData['service_revenue'],
                'business_revenue' => $dayData['business_revenue'],
                'total_revenue' => $dayData['total_revenue'],
            ];

            $currentDate->addDay();
        }

        return $data;
    }

    /**
     * Lấy thống kê doanh thu theo thời gian cụ thể
     */
    public function getTimeBasedStats(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisWeekStart = Carbon::now()->startOfWeek();
        $thisMonthStart = Carbon::now()->startOfMonth();
        $lastMonthStart = Carbon::now()->subMonth()->startOfMonth();
        $lastMonthEnd = Carbon::now()->subMonth()->endOfMonth();

        return [
            'today' => $this->getRevenueOverview($today, $today),
            'yesterday' => $this->getRevenueOverview($yesterday, $yesterday),
            'this_week' => $this->getRevenueOverview($thisWeekStart, $today),
            'this_month' => $this->getRevenueOverview($thisMonthStart, $today),
            'last_month' => $this->getRevenueOverview($lastMonthStart, $lastMonthEnd),
        ];
    }

    /**
     * Lấy top sản phẩm bán chạy theo doanh thu
     */
    public function getTopProductsByRevenue(int $limit = 10, Carbon $startDate = null, Carbon $endDate = null): array
    {
        $query = DB::table('orders')
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->where('orders.status', 'completed')
            ->select(
                'products.id',
                'products.name',
                DB::raw('SUM(order_items.price * order_items.quantity) as total_revenue'),
                DB::raw('SUM(order_items.quantity) as total_sold')
            )
            ->groupBy('products.id', 'products.name')
            ->orderBy('total_revenue', 'desc')
            ->limit($limit);

        if ($startDate) {
            $query->whereDate('orders.created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('orders.created_at', '<=', $endDate);
        }

        return $query->get()->toArray();
    }

    /**
     * Lấy top dịch vụ theo doanh thu
     */
    public function getTopServicesByRevenue(int $limit = 10, Carbon $startDate = null, Carbon $endDate = null): array
    {
        $query = DB::table('service_orders')
            ->join('services', 'service_orders.service_id', '=', 'services.id')
            ->where('service_orders.status', 'completed')
            ->select(
                'services.id',
                'services.name',
                DB::raw('SUM(service_orders.price) as total_revenue'),
                DB::raw('COUNT(service_orders.id) as total_orders')
            )
            ->groupBy('services.id', 'services.name')
            ->orderBy('total_revenue', 'desc')
            ->limit($limit);

        if ($startDate) {
            $query->whereDate('service_orders.created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('service_orders.created_at', '<=', $endDate);
        }

        return $query->get()->toArray();
    }

    /**
     * Lấy thống kê nạp tiền theo phương thức
     */
    public function getTopupMethodStats(Carbon $startDate = null, Carbon $endDate = null): array
    {
        $query = BalanceTransaction::query();
        
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $atmRevenue = $query->clone()->where('type', BalanceTransaction::TYPE_TOPUP_ATM)->sum('amount');
        $cardRevenue = $query->clone()->where('type', BalanceTransaction::TYPE_TOPUP_CARD)->sum('amount');
        $totalTopup = $atmRevenue + $cardRevenue;

        return [
            'atm_revenue' => $atmRevenue,
            'card_revenue' => $cardRevenue,
            'total_topup' => $totalTopup,
            'atm_percentage' => $totalTopup > 0 ? round(($atmRevenue / $totalTopup) * 100, 2) : 0,
            'card_percentage' => $totalTopup > 0 ? round(($cardRevenue / $totalTopup) * 100, 2) : 0,
        ];
    }

    /**
     * Format số tiền theo chuẩn Việt Nam (dấu chấm)
     */
    public function formatMoney($amount): string
    {
        return number_format($amount, 0, '.', '.') . ' đ';
    }

    /**
     * Lấy dữ liệu cho biểu đồ 7 ngày gần đây
     */
    public function getWeeklyChartData(): array
    {
        $endDate = Carbon::today();
        $startDate = Carbon::today()->subDays(6);
        
        return $this->getDailyRevenue($startDate, $endDate);
    }

    /**
     * Lấy dữ liệu cho biểu đồ 30 ngày gần đây
     */
    public function getMonthlyChartData(): array
    {
        $endDate = Carbon::today();
        $startDate = Carbon::today()->subDays(29);
        
        return $this->getDailyRevenue($startDate, $endDate);
    }
}
