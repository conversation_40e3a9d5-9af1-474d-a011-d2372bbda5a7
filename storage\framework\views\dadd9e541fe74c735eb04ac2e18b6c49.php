<?php $__env->startSection('title', '<PERSON> tiết đơn hàng #' . $order->order_number . ' - AccReroll'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 py-4 lg:py-8">
    <!-- Breadcrumb -->
    <nav class="mb-4 lg:mb-6" aria-label="Breadcrumb">
        <ol class="flex flex-wrap items-center gap-1 lg:gap-2 text-sm lg:text-base">
            <li>
                <a href="<?php echo e(route('home')); ?>" class="text-gray-500 hover:text-blue-600 px-1">
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
            <li><i class="fas fa-chevron-right text-gray-400 text-xs"></i></li>
            <li>
                <a href="<?php echo e(route('orders.index')); ?>" class="text-gray-500 hover:text-blue-600 px-1">
                    <span class="hidden sm:inline">Đơn hàng</span>
                    <span class="sm:hidden">Orders</span>
                </a>
            </li>
            <li><i class="fas fa-chevron-right text-gray-400 text-xs"></i></li>
            <li class="text-gray-900 font-medium px-1 truncate max-w-[120px] sm:max-w-[200px] lg:max-w-none" title="<?php echo e($order->order_number); ?>">
                <?php echo e($order->order_number); ?>

            </li>
        </ol>
    </nav>

    <!-- Order Header -->
    <div class="bg-white rounded-lg shadow-md p-4 lg:p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 space-y-2 lg:space-y-0">
            <div>
                <h1 class="text-xl lg:text-2xl font-bold text-gray-900">Đơn hàng <?php echo e($order->order_number); ?></h1>
                <p class="text-gray-600 text-sm lg:text-base">Đặt hàng lúc <?php echo e($order->created_at->format('H:i d/m/Y')); ?></p>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 self-start lg:self-auto">
                <i class="fas fa-check mr-2"></i>Hoàn thành
            </span>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Tổng tiền</div>
                <div class="text-lg lg:text-xl font-bold text-red-600"><?php echo e(format_money($order->total_amount)); ?></div>
            </div>
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Số lượng</div>
                <div class="text-lg lg:text-xl font-bold text-gray-900"><?php echo e($order->orderItems->count()); ?> sản phẩm</div>
            </div>
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Trạng thái</div>
                <div class="text-lg lg:text-xl font-bold text-green-600">Hoàn thành</div>
            </div>
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Voucher sử dụng</div>
                <?php if($order->usedVoucher && $order->usedVoucher->voucher): ?>
                    <div class="text-sm font-medium text-green-600">
                        <i class="fas fa-ticket-alt mr-1"></i><?php echo e($order->usedVoucher->voucher->code); ?>

                    </div>
                    <div class="text-xs text-gray-500">
                        <?php if($order->usedVoucher->voucher->discount_type === 'fixed'): ?>
                            Giảm <?php echo e(number_format($order->usedVoucher->voucher->discount_value, 0, ',', '.')); ?>đ
                        <?php else: ?>
                            Giảm <?php echo e($order->usedVoucher->voucher->discount_value); ?>%
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-sm text-gray-400">Không sử dụng</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="bg-white rounded-lg shadow-md p-4 lg:p-6">
        <h2 class="text-lg lg:text-xl font-semibold text-gray-900 mb-4 lg:mb-6">Sản phẩm đã mua</h2>

        <div class="space-y-6">
            <?php
                // Group items by product
                $groupedItems = $order->orderItems->groupBy('product_id');
            ?>

            <?php $__currentLoopData = $groupedItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productId => $items): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $firstItem = $items->first();
                $totalQuantity = $items->sum('quantity');
                $totalPrice = $items->sum(function($item) { return $item->price * $item->quantity; });
            ?>
            <div class="border border-gray-200 rounded-lg p-3 lg:p-4">
                <!-- Product Info -->
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 space-y-3 lg:space-y-0">
                    <div class="flex items-center space-x-3 lg:space-x-4">
                        <div class="w-14 h-14 lg:w-16 lg:h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <?php if($firstItem->product->main_image): ?>
                                <img src="<?php echo e(storage_url($firstItem->product->main_image)); ?>" alt="<?php echo e($firstItem->product->name); ?>"
                                     class="w-full h-full object-cover rounded-lg">
                            <?php else: ?>
                                <i class="fas fa-gamepad text-gray-400 text-lg lg:text-xl"></i>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-gray-900 text-sm lg:text-base"><?php echo e($firstItem->product->name); ?></h3>
                            <p class="text-xs lg:text-sm text-gray-500"><?php echo e($firstItem->product->category->name); ?></p>
                            <p class="text-xs lg:text-sm text-gray-500">Số lượng: <?php echo e($totalQuantity); ?> tài khoản</p>
                        </div>
                    </div>
                    <div class="text-right lg:text-right">
                        <div class="text-base lg:text-lg font-bold text-red-600"><?php echo e(format_money($totalPrice)); ?></div>
                        <div class="text-xs lg:text-sm text-gray-500"><?php echo e(format_money($firstItem->price)); ?> x<?php echo e($totalQuantity); ?></div>
                    </div>
                </div>

                <!-- Account Details -->
                <div class="space-y-3">
                    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($item->productAccount): ?>
                        <!-- Hiển thị tài khoản thật như cũ -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 lg:p-4">
                            <div class="mb-3">
                                <h4 class="font-medium text-blue-900 text-sm lg:text-base">
                                    Tài khoản #<?php echo e($index + 1); ?>

                                    <?php if($totalQuantity > 1): ?>
                                        <span class="text-sm font-normal text-blue-700">(<?php echo e($index + 1); ?>/<?php echo e($totalQuantity); ?>)</span>
                                    <?php endif; ?>
                                </h4>
                            </div>
                            <div class="space-y-2">
                                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-1 lg:space-y-0">
                                    <span class="text-blue-700 font-medium text-sm">Tên đăng nhập:</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-blue-900 font-mono text-sm break-all"><?php echo e($item->productAccount->username); ?></span>
                                        <button onclick="copyToClipboard('<?php echo e($item->productAccount->username); ?>')"
                                                class="text-blue-600 hover:text-blue-800 text-xs">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-1 lg:space-y-0">
                                    <span class="text-blue-700 font-medium text-sm">Mật khẩu:</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-blue-900 font-mono text-sm break-all"><?php echo e($item->productAccount->password); ?></span>
                                        <button onclick="copyToClipboard('<?php echo e($item->productAccount->password); ?>')"
                                                class="text-blue-600 hover:text-blue-800 text-xs">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Hiển thị mã đơn hàng cho preorder -->
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-3 lg:p-4">
                            <div class="mb-3">
                                <h4 class="font-medium text-orange-900 text-sm lg:text-base">
                                    Sản phẩm #<?php echo e($index + 1); ?> - Đang chờ xử lý
                                </h4>
                            </div>
                            <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-1 lg:space-y-0">
                                <span class="text-orange-700 font-medium text-sm">Mã đơn hàng:</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-orange-900 font-mono text-sm break-all"><?php echo e($order->order_number); ?></span>
                                    <button onclick="copyToClipboard('<?php echo e($order->order_number); ?>')" class="text-orange-600 hover:text-orange-800 text-xs">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <a href="https://www.facebook.com/profile.php?id=61578528443379" target="_blank"
                                   class="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700 transition-colors">
                                    <i class="fab fa-facebook-messenger mr-1"></i>
                                    Liên hệ để nhận tài khoản
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>


</div>

<script>
function copyToClipboard(text) {
    const button = event.target.closest('button');

    navigator.clipboard.writeText(text).then(function() {
        showCopySuccess(button);
    }).catch(function(err) {
        // Fallback for older browsers or non-HTTPS
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopySuccess(button);
    });
}

function showCopySuccess(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('text-green-600');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('text-green-600');
    }, 2000);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/orders/show.blade.php ENDPATH**/ ?>