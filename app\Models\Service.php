<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'order_instructions',
        'price',
        'category_id',
        'images',
        'status'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'images' => 'array'
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function serviceOrders()
    {
        return $this->hasMany(ServiceOrder::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return format_money($this->price);
    }

    public function getFirstImageAttribute()
    {
        $images = $this->images;

        // Ensure images is an array
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        if (is_array($images) && count($images) > 0) {
            return asset('storage/' . $images[0]);
        }
        return asset('images/default-service.png');
    }

    public function getImagesArrayAttribute()
    {
        $images = $this->images;

        // Ensure images is an array
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return is_array($images) ? $images : [];
    }
}
