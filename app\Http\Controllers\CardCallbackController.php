<?php

namespace App\Http\Controllers;

use App\Models\BalanceTransaction;
use App\Models\TopupTransaction;
use App\Models\User;
use App\Services\CardApiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CardCallbackController extends Controller
{
    /**
     * Xử lý callback từ Card API
     */
    public function handle(Request $request)
    {
        try {
            $data = $request->all();
            
            Log::info('Card Callback Received', [
                'data' => $data,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Validate required fields
            $requiredFields = ['status', 'code', 'serial', 'callback_sign'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    Log::error('Card Callback Missing Field', ['field' => $field]);
                    return response()->json(['error' => 'Missing required field: ' . $field], 400);
                }
            }

            // Verify signature
            $partnerKey = config('services.card_api.partner_key');
            $expectedSign = md5($partnerKey . $data['code'] . $data['serial']);
            
            if ($data['callback_sign'] !== $expectedSign) {
                Log::error('Card Callback Invalid Signature', [
                    'expected' => $expectedSign,
                    'received' => $data['callback_sign']
                ]);
                return response()->json(['error' => 'Invalid signature'], 403);
            }

            // Find transaction by code and serial
            $transaction = TopupTransaction::where('card_code', $data['code'])
                ->where('card_serial', $data['serial'])
                ->where('method', TopupTransaction::METHOD_CARD)
                ->first();

            if (!$transaction) {
                Log::warning('Card Callback Transaction Not Found', [
                    'code' => $data['code'],
                    'serial' => $data['serial']
                ]);
                return response()->json(['error' => 'Transaction not found'], 404);
            }

            // Xử lý trường hợp callback success nhưng transaction đã failed
            $status = (int)$data['status'];

            if ($transaction->status !== TopupTransaction::STATUS_PENDING) {
                // Nếu callback báo thành công nhưng transaction đã failed, cần xử lý lại
                if (CardApiService::isSuccess($status) && $transaction->status === TopupTransaction::STATUS_FAILED) {
                    Log::info('Card Callback Success After Failed - Reprocessing', [
                        'transaction_id' => $transaction->id,
                        'previous_status' => $transaction->status,
                        'callback_status' => $status
                    ]);
                    // Tiếp tục xử lý thay vì return
                } else {
                    Log::info('Card Callback Already Processed', [
                        'transaction_id' => $transaction->id,
                        'current_status' => $transaction->status,
                        'callback_status' => $status
                    ]);
                    return response()->json(['message' => 'Already processed'], 200);
                }
            }

            DB::transaction(function () use ($transaction, $data, $status) {
                $user = User::find($transaction->user_id);
                // Update transaction with callback data
                $transaction->update([
                    'note' => json_encode($data),
                    'status' => CardApiService::isSuccess($status) 
                        ? TopupTransaction::STATUS_COMPLETED 
                        : TopupTransaction::STATUS_FAILED
                ]);

                if (CardApiService::isSuccess($status)) {
                    // Lấy các giá trị từ callback
                    $declaredValue = isset($data['declared_value']) ? (int)$data['declared_value'] : $transaction->amount;
                    $actualValue = isset($data['value']) ? (int)$data['value'] : $declaredValue;
                    $apiAmount = isset($data['amount']) ? (int)$data['amount'] : $transaction->amount;

                    // Logic tính số tiền user nhận được
                    if ($declaredValue === $actualValue) {
                        // User khai đúng giá trị → Nhận amount từ API (đã tính đúng tỷ lệ)
                        $actualAmount = $actualValue * 80 / 100;
                        $note = "Khai báo đúng giá trị - Nhận 80% giá trị thẻ";
                    } else {
                        // User khai sai → Nhận amount từ API (đã bị trừ phí cao hơn)
                        $actualAmount = $apiAmount;
                        $note = "Khai báo sai giá trị - API trừ phí cao";
                    }
                    
                    // Create balance transaction log
                    BalanceTransaction::createTransaction(
                        user: $user,
                        type: BalanceTransaction::TYPE_TOPUP_CARD,
                        amount: $actualAmount,
                        description: "Nạp thẻ {$transaction->card_type} - Khai báo: {$declaredValue}đ, Thực tế: {$actualValue}đ, Nhận: {$actualAmount}đ - {$note}",
                        reference: $transaction,
                        metadata: [
                            'telco' => $transaction->card_type,
                            'serial' => $transaction->card_serial,
                            'card_code' => $transaction->card_code,
                            'declared_value' => $declaredValue,
                            'actual_value' => $actualValue,
                            'api_amount' => $apiAmount,
                            'final_amount' => $actualAmount,
                            'calculation_note' => $note,
                            'callback_data' => $data
                        ]
                    );

                    // Add balance to user account
                    $user->addBalance($actualAmount);

                    Log::info('Card Callback Success', [
                        'transaction_id' => $transaction->id,
                        'user_id' => $user->id,
                        'declared_value' => $declaredValue,
                        'actual_value' => $actualValue,
                        'api_amount' => $apiAmount,
                        'final_amount' => $actualAmount,
                        'calculation_note' => $note,
                        'is_correct_declaration' => $declaredValue === $actualValue
                    ]);
                } else {
                    Log::info('Card Callback Failed', [
                        'transaction_id' => $transaction->id,
                        'status' => $status,
                        'message' => CardApiService::getStatusMessage($status)
                    ]);
                }
            });

            return response()->json([
                'message' => 'Callback processed successfully',
                'transaction_id' => $transaction->id,
                'status' => $transaction->fresh()->status
            ], 200);

        } catch (\Exception $e) {
            Log::error('Card Callback Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $request->all()
            ]);

            return response()->json([
                'error' => 'Internal server error'
            ], 500);
        }
    }
}
