<?php

namespace App\Services;

use App\Models\BalanceTransaction;
use App\Models\TopupTransaction;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TopupService
{
    protected $cardApiService;

    public function __construct(CardApiService $cardApiService)
    {
        $this->cardApiService = $cardApiService;
    }

    /**
     * Tạo QR code SePay với nội dung nạp riêng cho user (lowercase)
     */
    public function generateSePayQR($userId)
    {
        $pattern = config('sepay.pattern', 'guidang');
        $content = strtolower("{$pattern}{$userId}"); // Đảm bảo lowercase

        $params = [
            'bank' => config('sepay.bank_info.bank_name', 'MBBank'),
            'acc' => config('sepay.bank_info.account_number', '*********'),
            'template' => config('sepay.qr_settings.template', 'compact'),
            'des' => $content
        ];

        return config('sepay.qr_settings.base_url', 'https://qr.sepay.vn/img') . '?' . http_build_query($params);
    }

    /**
     * Xử lý nạp thẻ cào
     */
    public function processCardTopup(User $user, array $cardData)
    {
        try {
            DB::beginTransaction();

            // Tạo topup transaction với status pending
            $transaction = TopupTransaction::create([
                'method' => TopupTransaction::METHOD_CARD,
                'user_id' => $user->id,
                'amount' => $cardData['amount'],
                'status' => TopupTransaction::STATUS_PENDING,
                'card_type' => $cardData['telco'],
                'card_serial' => $cardData['serial'],
                'card_code' => $cardData['code'],
                'transaction_code' => 'CARD_' . time() . '_' . $user->id,
            ]);

            // Gọi API nạp thẻ
            $result = $this->cardApiService->chargeCard(
                $cardData['telco'],
                $cardData['code'],
                $cardData['serial'],
                $cardData['amount']
            );

            if (!$result['success']) {
                throw new \Exception('Lỗi kết nối API: ' . ($result['error'] ?? 'Unknown error'));
            }

            $apiResponse = $result['data'];
            $status = $apiResponse['status'] ?? 100;

            // Cập nhật transaction với kết quả API và request_id
            $transaction->update([
                'note' => json_encode(array_merge($apiResponse, ['request_id' => $result['request_id']])),
            ]);

            if (CardApiService::isSuccess($status)) {
                // Thành công - cộng tiền
                $transaction->update(['status' => TopupTransaction::STATUS_COMPLETED]);

                // Tạo balance transaction log
                BalanceTransaction::createTransaction(
                    user: $user,
                    type: BalanceTransaction::TYPE_TOPUP_CARD,
                    amount: $cardData['amount'],
                    description: "Nạp thẻ {$cardData['telco']} {$cardData['amount']}đ - Serial: {$cardData['serial']}",
                    reference: $transaction
                );

                // Cộng tiền vào tài khoản
                $user->addBalance($cardData['amount']);

                DB::commit();

                return [
                    'success' => true,
                    'message' => "Gửi thẻ thành công",
                    'type' => 'success'
                ];

            } elseif (CardApiService::isPending($status)) {
                // Chờ xử lý
                DB::commit();

                return [
                    'success' => true,
                    'message' => 'Thẻ đang được xử lý. Vui lòng chờ trong giây lát.',
                    'type' => 'info'
                ];

            } else {
                // Thất bại
                $transaction->update(['status' => TopupTransaction::STATUS_FAILED]);
                DB::commit();

                return [
                    'success' => false,
                    'message' => "Có lỗi xảy ra, vui lòng thử lại",
                    'type' => 'error'
                ];
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Card topup error', [
                'user_id' => $user->id,
                'card_data' => $cardData,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra, vui lòng thử lại',
                'type' => 'error'
            ];
        }
    }

    /**
     * Kiểm tra trạng thái thẻ cào
     */
    public function checkCardStatus($transactionId)
    {
        $transaction = TopupTransaction::findOrFail($transactionId);

        // Kiểm tra quyền truy cập
        if ($transaction->user_id !== auth()->id()) {
            return [
                'success' => false,
                'message' => 'Không có quyền truy cập',
                'type' => 'error'
            ];
        }

        // Nếu đã hoàn thành hoặc thất bại, không cần check lại
        if (in_array($transaction->status, [TopupTransaction::STATUS_COMPLETED, TopupTransaction::STATUS_FAILED])) {
            return [
                'success' => true,
                'status' => $transaction->status === TopupTransaction::STATUS_COMPLETED ? 1 : 0,
                'message' => $transaction->status === TopupTransaction::STATUS_COMPLETED 
                    ? 'Thẻ đã được xử lý thành công'
                    : 'Thẻ đã được xử lý',
                'transaction_status' => $transaction->status
            ];
        }

        $result = $this->cardApiService->checkCard(
            $transaction->card_type,
            $transaction->card_code,
            $transaction->card_serial,
            $transaction->amount
        );

        if (!$result['success']) {
            return [
                'success' => false,
                'message' => 'Lỗi kết nối API',
                'type' => 'error'
            ];
        }

        $apiResponse = $result['data'];
        $status = $apiResponse['status'] ?? 100;

        // Cập nhật transaction nếu có thay đổi
        if (CardApiService::isSuccess($status)) {
            DB::transaction(function () use ($transaction, $apiResponse) {
                $user = $transaction->user;

                // Lấy các giá trị từ API response
                $declaredValue = isset($apiResponse['declared_value']) ? (int)$apiResponse['declared_value'] : $transaction->amount;
                $actualValue = isset($apiResponse['value']) ? (int)$apiResponse['value'] : $declaredValue;
                $apiAmount = isset($apiResponse['amount']) ? (int)$apiResponse['amount'] : $transaction->amount;

                // Logic tính số tiền user nhận được
                if ($declaredValue === $actualValue) {
                    // User khai đúng giá trị → Nhận amount từ API (đã tính đúng tỷ lệ)
                    $actualAmount = $apiAmount;
                    $note = "Khai báo đúng giá trị - Nhận " . round(($apiAmount / $actualValue) * 100, 1) . "% giá trị thẻ";
                } else {
                    // User khai sai → Nhận amount từ API (đã bị trừ phí cao hơn)
                    $actualAmount = $apiAmount;
                    $note = "Khai báo sai giá trị - API trừ phí cao";
                }

                // Cập nhật transaction
                $transaction->update([
                    'status' => TopupTransaction::STATUS_COMPLETED,
                    'note' => json_encode($apiResponse)
                ]);

                // Tạo balance transaction log với số tiền thực tế
                BalanceTransaction::createTransaction(
                    user: $user,
                    type: BalanceTransaction::TYPE_TOPUP_CARD,
                    amount: $actualAmount,
                    description: "Nạp thẻ {$transaction->card_type} - Khai báo: " . number_format($declaredValue) . "đ, Thực tế: " . number_format($actualValue) . "đ, Nhận: " . number_format($actualAmount) . "đ - {$note}",
                    reference: $transaction,
                    metadata: [
                        'declared_value' => $declaredValue,
                        'actual_value' => $actualValue,
                        'api_amount' => $apiAmount,
                        'final_amount' => $actualAmount,
                        'calculation_note' => $note
                    ]
                );

                // Cộng tiền thực tế vào tài khoản
                $user->addBalance($actualAmount);

                Log::info('Card Status Check Success', [
                    'transaction_id' => $transaction->id,
                    'user_id' => $user->id,
                    'declared_value' => $declaredValue,
                    'actual_value' => $actualValue,
                    'api_amount' => $apiAmount,
                    'final_amount' => $actualAmount,
                    'calculation_note' => $note,
                    'is_correct_declaration' => $declaredValue === $actualValue
                ]);
            });
        } elseif (!CardApiService::isPending($status)) {
            // Thất bại - cập nhật status
            $transaction->update([
                'status' => TopupTransaction::STATUS_FAILED,
                'note' => json_encode($apiResponse)
            ]);
        }

        return [
            'success' => true,
            'status' => $status,
            'message' => CardApiService::getStatusMessage($status),
            'transaction_status' => $transaction->fresh()->status
        ];
    }

    /**
     * Lấy lịch sử nạp tiền của user
     */
    public function getUserTopupHistory($userId, $perPage = 20)
    {
        return TopupTransaction::forUser($userId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Lấy giao dịch nạp tiền gần đây
     */
    public function getRecentTopupTransactions($userId, $limit = 5)
    {
        return TopupTransaction::forUser($userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
