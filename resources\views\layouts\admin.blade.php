<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicon với nhi<PERSON>u kích thước -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ asset('favicon-96x96.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ asset('favicon.ico') }}">

    <title>@yield('title', 'AccReroll Admin')</title>
    <meta name="description" content="@yield('description', 'Admin Panel - AccReroll Management')">

    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    @stack('styles')
</head>
<body class="bg-gray-100 font-sans antialiased">
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-gray-600 bg-opacity-50 z-40 lg:hidden hidden"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 flex flex-col shadow-sm">
            <!-- Header -->
            <div class="flex items-center justify-center h-16 border-b border-gray-200 bg-white">
                <h1 class="text-gray-800 text-lg font-semibold">
                    AccReroll Admin
                </h1>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 py-4">
                <div class="px-3 space-y-1">
                    <!-- Dashboard -->
                    <a href="{{ route('admin.dashboard') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.dashboard') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-chart-line w-5 mr-3 {{ request()->routeIs('admin.dashboard') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Dashboard
                    </a>

                    <!-- Users -->
                    <a href="{{ route('admin.users.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.users.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-users w-5 mr-3 {{ request()->routeIs('admin.users.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Người dùng
                    </a>

                    <!-- Products -->
                    <a href="{{ route('admin.products.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.products.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-box w-5 mr-3 {{ request()->routeIs('admin.products.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Sản phẩm
                    </a>

                    <!-- Categories -->
                    <a href="{{ route('admin.categories.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.categories.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-tags w-5 mr-3 {{ request()->routeIs('admin.categories.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Danh mục
                    </a>

                    <!-- Orders -->
                    <a href="{{ route('admin.orders.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.orders.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-shopping-cart w-5 mr-3 {{ request()->routeIs('admin.orders.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Đơn hàng
                    </a>

                    <!-- Services -->
                    <a href="{{ route('admin.services.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.services.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-tools w-5 mr-3 {{ request()->routeIs('admin.services.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Dịch vụ
                    </a>

                    <!-- Service Orders -->
                    <a href="{{ route('admin.service-orders.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.service-orders.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-clipboard-list w-5 mr-3 {{ request()->routeIs('admin.service-orders.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Đơn dịch vụ
                    </a>

                    <!-- Vouchers -->
                    <a href="{{ route('admin.vouchers.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.vouchers.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-ticket-alt w-5 mr-3 {{ request()->routeIs('admin.vouchers.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Voucher
                    </a>



                    <!-- Guides -->
                    <a href="{{ route('admin.guides.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.guides.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-book-open w-5 mr-3 {{ request()->routeIs('admin.guides.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Hướng dẫn
                    </a>



                    <!-- Transaction Logs -->
                    <a href="{{ route('admin.transaction-logs.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.transaction-logs.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-receipt w-5 mr-3 {{ request()->routeIs('admin.transaction-logs.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Lịch sử giao dịch
                    </a>

                    <!-- Revenue Report -->
                    <a href="{{ route('admin.revenue-report.index') }}"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 {{ request()->routeIs('admin.revenue-report.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                        <i class="fas fa-chart-line w-5 mr-3 {{ request()->routeIs('admin.revenue-report.*') ? 'text-blue-600' : 'text-gray-400' }}"></i>
                        Báo cáo doanh thu
                    </a>


                </div>
            </nav>

            <!-- User Info & Logout -->
            <div class="border-t border-gray-200 p-3">
                <!-- User Info -->
                <div class="flex items-center px-3 py-2 mb-2">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-blue-600 text-sm"></i>
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ Auth::user()->username }}</p>
                        <p class="text-xs text-gray-500">Administrator</p>
                    </div>
                </div>

                <!-- Logout Button -->
                <form method="POST" action="{{ route('admin.logout') }}">
                    @csrf
                    <button type="submit" class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 hover:bg-red-50 hover:text-red-700 rounded-md transition-colors duration-150">
                        <i class="fas fa-sign-out-alt w-5 mr-3 text-gray-400"></i>
                        Đăng xuất
                    </button>
                </form>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden" id="mainContentArea">
            <!-- Mobile Header Bar -->
            <div class="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center shadow-sm">
                <button id="sidebar-toggle" class="text-gray-600 p-2 rounded-md hover:bg-gray-50 transition-colors">
                    <i class="fas fa-bars text-lg"></i>
                </button>
            </div>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
                <!-- Breadcrumb -->
                @if(isset($breadcrumbs))
                <nav class="mb-6">
                    <ol class="flex items-center space-x-2 text-sm text-gray-600">
                        @foreach($breadcrumbs as $breadcrumb)
                            @if(!$loop->last)
                                <li>
                                    <a href="{{ $breadcrumb['url'] }}" class="hover:text-gray-900">{{ $breadcrumb['title'] }}</a>
                                </li>
                                <li><i class="fas fa-chevron-right text-xs"></i></li>
                            @else
                                <li class="text-gray-900 font-medium">{{ $breadcrumb['title'] }}</li>
                            @endif
                        @endforeach
                    </ol>
                </nav>
                @endif

                <!-- Flash Messages -->
                @if(session('success'))
                    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ session('error') }}</span>
                    </div>
                @endif

                <!-- Main Content -->
                @yield('content')
            </main>
        </div>
    </div></div>

    @stack('scripts')

    <!-- Sidebar Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            function toggleSidebar() {
                sidebar.classList.toggle('-translate-x-full');
                sidebarOverlay.classList.toggle('hidden');
            }

            sidebarToggle.addEventListener('click', toggleSidebar);
            sidebarOverlay.addEventListener('click', toggleSidebar);
        });
    </script>
</body>
</html>
