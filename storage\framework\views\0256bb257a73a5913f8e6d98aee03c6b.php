<?php $__env->startSection('title', '<PERSON> tiết Đơn hàng D<PERSON> vụ'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Chi tiết đơn dịch vụ <?php echo e($serviceOrder->order_number ?? 'SRV' . str_pad($serviceOrder->id, 4, '0', STR_PAD_LEFT)); ?></h1>
        <a href="<?php echo e(route('admin.service-orders.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Quay lại
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Order Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Order Info -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thông tin Đơn hàng</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">ID đơn hàng:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->id); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Dịch vụ:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->service->name); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Danh mục:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->service->category->name); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Giá:</span>
                                <span class="text-sm font-medium text-blue-600"><?php echo e($serviceOrder->formatted_price); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Voucher sử dụng:</span>
                                <?php if($serviceOrder->usedVoucher && $serviceOrder->usedVoucher->voucher): ?>
                                    <span class="text-sm text-green-600">
                                        <i class="fas fa-ticket-alt mr-1"></i><?php echo e($serviceOrder->usedVoucher->voucher->code); ?>

                                        <span class="text-xs text-gray-500">
                                            <?php if($serviceOrder->usedVoucher->voucher->discount_type === 'fixed'): ?>
                                                (Giảm <?php echo e(format_money($serviceOrder->usedVoucher->voucher->discount_value, false)); ?>đ)
                                            <?php else: ?>
                                                (Giảm <?php echo e($serviceOrder->usedVoucher->voucher->discount_value); ?>%)
                                            <?php endif; ?>
                                        </span>
                                    </span>
                                <?php else: ?>
                                    <span class="text-sm text-gray-400">Không sử dụng</span>
                                <?php endif; ?>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Trạng thái:</span>
                                <span>
                                    <?php if($serviceOrder->status === 'pending'): ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Chờ xử lý</span>
                                    <?php elseif($serviceOrder->status === 'completed'): ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>Hoàn thành
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Đã hủy</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Khách hàng:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->user->username); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Email:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->user->email); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Ngày đặt:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->created_at->format('d/m/Y H:i:s')); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Cập nhật:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->updated_at->format('d/m/Y H:i:s')); ?></span>
                            </div>
                            <?php if($serviceOrder->completed_at): ?>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500">Hoàn thành:</span>
                                <span class="text-sm text-gray-900"><?php echo e($serviceOrder->completed_at->format('d/m/Y H:i:s')); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content & Notes -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Nội dung & Ghi chú</h3>
                </div>
                <div class="p-6 space-y-6">
                    <!-- User Content -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Thông tin từ khách hàng:</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <pre class="text-sm text-gray-700 whitespace-pre-wrap font-sans"><?php echo e($serviceOrder->content); ?></pre>
                        </div>
                    </div>

                    <!-- User Notes -->
                    <?php if($serviceOrder->notes): ?>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Ghi chú từ khách hàng:</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <pre class="text-sm text-gray-700 whitespace-pre-wrap font-sans"><?php echo e($serviceOrder->notes); ?></pre>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Admin Notes -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Ghi chú của Admin:</h4>
                        <?php if($serviceOrder->admin_notes): ?>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <pre class="text-sm text-gray-700 whitespace-pre-wrap font-sans"><?php echo e($serviceOrder->admin_notes); ?></pre>
                            </div>
                        <?php else: ?>
                            <p class="text-sm text-gray-500">Chưa có ghi chú từ admin</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Panel -->
        <div class="space-y-6">
            <!-- Status Update -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Cập nhật Trạng thái</h3>
                </div>
                <div class="p-6">
                    <form method="POST" action="<?php echo e(route('admin.service-orders.update-status', $serviceOrder)); ?>">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Status -->
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
                            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    id="status" name="status" required>
                                <option value="pending" <?php echo e($serviceOrder->status == 'pending' ? 'selected' : ''); ?>>Chờ xử lý</option>
                                <option value="completed" <?php echo e($serviceOrder->status == 'completed' ? 'selected' : ''); ?>>Hoàn thành</option>
                                <option value="cancelled" <?php echo e($serviceOrder->status == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                            </select>
                        </div>

                        <!-- Admin Notes -->
                        <div class="mb-6">
                            <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">Ghi chú Admin</label>
                            <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                      id="admin_notes" name="admin_notes" rows="4" 
                                      placeholder="Ghi chú về quá trình xử lý..."><?php echo e($serviceOrder->admin_notes); ?></textarea>
                        </div>

                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            <i class="fas fa-save mr-2"></i>Cập nhật
                        </button>
                    </form>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Thao tác nhanh</h3>
                </div>
                <div class="p-6 space-y-3">
                    <?php if($serviceOrder->status === 'pending'): ?>
                        <form method="POST" action="<?php echo e(route('admin.service-orders.update-status', $serviceOrder)); ?>">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="status" value="completed">
                            <input type="hidden" name="admin_notes" value="Đã hoàn thành xử lý dịch vụ">
                            <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors" 
                                    onclick="return confirm('Xác nhận hoàn thành đơn hàng này?')">
                                <i class="fas fa-check mr-2"></i>Hoàn thành
                            </button>
                        </form>

                        <form method="POST" action="<?php echo e(route('admin.service-orders.update-status', $serviceOrder)); ?>">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="status" value="cancelled">
                            <input type="hidden" name="admin_notes" value="Đơn hàng đã bị hủy">
                            <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors" 
                                    onclick="return confirm('Xác nhận hủy đơn hàng này?')">
                                <i class="fas fa-times mr-2"></i>Hủy đơn
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/service-orders/show.blade.php ENDPATH**/ ?>