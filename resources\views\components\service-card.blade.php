@props(['service'])

<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <!-- Service Image -->
    <div class="relative h-40 bg-gray-200">
        <img src="{{ $service->first_image }}" 
             alt="{{ $service->name }}" 
             class="w-full h-full object-contain">
        
        <!-- Status Badge -->
        @if($service->status === 'inactive')
            <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                Tạm dừng
            </div>
        @endif
    </div>

    <!-- Service Info -->
    <div class="p-4">
        <!-- Category -->
        <div class="text-xs text-gray-500 mb-1">
            {{ $service->category->name }}
        </div>

        <!-- Service Name -->
        <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 text-lg">
            {{ $service->name }}
        </h3>

        <!-- Description -->
        <p class="text-gray-600 text-sm mb-3 line-clamp-2">
            {{ Str::limit($service->description, 100) }}
        </p>

        <!-- Price -->
        <div class="flex items-center justify-between">
            <div class="text-xl font-bold text-orange-600">
                {{ $service->formatted_price }}
            </div>
            
            <!-- Action Button -->
            <a href="{{ route('services.show', $service) }}" 
               class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                Xem chi tiết
            </a>
        </div>
    </div>
</div>
