<?php

namespace App\Http\Controllers;

use App\Models\BalanceTransaction;
use App\Models\TopupTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class SePayController extends Controller
{
    /**
     * Xử lý webhook từ SePay
     */
    public function webhook(Request $request)
    {
        try {
            // Kiểm tra API key từ header Authorization: Apikey xxx
            $apiKey = $this->getApiKeyFromHeader($request);

            if (config('sepay.api_key') && $apiKey !== config('sepay.api_key')) {
                Log::warning('SePay webhook: Invalid API key', ['provided_key' => $apiKey]);
                return response()->json(['message' => 'Invalid API key'], 401);
            }

            // Validate dữ liệu từ SePay
            $validated = $request->validate([
                'id' => 'required|integer',
                'gateway' => 'required|string',
                'transactionDate' => 'required|string',
                'accountNumber' => 'required|string',
                'subAccount' => 'nullable|string',
                'code' => 'nullable|string',
                'content' => 'required|string',
                'transferType' => 'required|string',
                'description' => 'nullable|string',
                'transferAmount' => 'required|integer',
                'referenceCode' => 'nullable|string',
                'accumulated' => 'nullable|integer',
            ]);

            // Kiểm tra transaction đã tồn tại chưa
            $existingTransaction = TopupTransaction::where('sepay_id', $validated['id'])->first();
            if ($existingTransaction) {
                Log::info('SePay webhook: Transaction already exists', ['sepay_id' => $validated['id']]);
                return response()->json(['message' => 'Transaction already processed'], 200);
            }

            // Tìm user ID từ content (case-insensitive)
            // User nhập: "guidang123", SePay có thể gửi: "GUIDANG123- Ma GD..." -> user_id = 123
            $pattern = '/' . preg_quote(config('sepay.pattern', 'guidang'), '/') . '(\d+)/i';
            preg_match($pattern, trim($validated['content']), $matches);

            if (!isset($matches[1])) {
                Log::warning('SePay webhook: Invalid content format - must contain guidang+id', [
                    'content' => $validated['content'],
                    'pattern' => $pattern,
                    'expected_format' => config('sepay.pattern', 'guidang') . '{user_id}',
                    'case_insensitive' => true
                ]);
                return response()->json(['message' => 'Invalid content format - must contain guidang+id'], 400);
            }

            $userId = (int) $matches[1];
            $user = User::find($userId);
            
            if (!$user) {
                Log::warning('SePay webhook: User not found', ['user_id' => $userId]);
                return response()->json(['message' => 'User not found'], 404);
            }

            // Tạo transaction và cập nhật balance trong transaction
            DB::transaction(function () use ($validated, $user) {
                // Tạo topup transaction
                $transaction = TopupTransaction::create([
                    'method' => TopupTransaction::METHOD_ATM,
                    'user_id' => $user->id,
                    'amount' => $validated['transferAmount'],
                    'status' => TopupTransaction::STATUS_COMPLETED,
                    'transaction_code' => $validated['referenceCode'] ?? $validated['code'],
                    'sepay_id' => $validated['id'],
                    'gateway' => $validated['gateway'],
                    'transactionDate' => $validated['transactionDate'],
                    'accountNumber' => $validated['accountNumber'],
                    'subAccount' => $validated['subAccount'],
                    'code' => $validated['code'],
                    'content' => $validated['content'],
                    'transferType' => $validated['transferType'],
                    'description' => $validated['description'],
                    'referenceCode' => $validated['referenceCode'],
                ]);

                // Tạo balance transaction log
                BalanceTransaction::createTransaction(
                    user: $user,
                    type: BalanceTransaction::TYPE_TOPUP_ATM,
                    amount: $validated['transferAmount'],
                    description: "Nạp tiền ATM qua {$validated['gateway']} - {$validated['content']}",
                    reference: $transaction,
                    metadata: [
                        'sepay_id' => $validated['id'],
                        'gateway' => $validated['gateway'],
                        'reference_code' => $validated['referenceCode'],
                        'transaction_date' => $validated['transactionDate']
                    ]
                );

                // Cập nhật balance cho user
                $user->addBalance($validated['transferAmount']);

                Log::info('SePay webhook: Transaction processed successfully', [
                    'transaction_id' => $transaction->id,
                    'user_id' => $user->id,
                    'amount' => $validated['transferAmount'],
                    'new_balance' => $user->fresh()->balance
                ]);
            });

            return response()->json(['message' => 'Transaction processed successfully'], 200);

        } catch (ValidationException $e) {
            Log::error('SePay webhook: Validation error', ['errors' => $e->errors()]);
            return response()->json(['message' => 'Validation error', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error('SePay webhook: Unexpected error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['message' => 'Internal server error'], 500);
        }
    }

    /**
     * Lấy API key từ header Authorization: Apikey xxx
     */
    private function getApiKeyFromHeader(Request $request): ?string
    {
        $header = $request->header('Authorization', '');

        // Kiểm tra format "Apikey xxx" (theo cấu hình SePay)
        if (str_starts_with($header, 'Apikey ')) {
            return substr($header, 7);
        }

        // Fallback: Kiểm tra format "Bearer xxx"
        if (str_starts_with($header, 'Bearer ')) {
            return substr($header, 7);
        }

        return null;
    }
}
