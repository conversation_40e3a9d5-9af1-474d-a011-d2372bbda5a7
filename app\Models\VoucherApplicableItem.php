<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VoucherApplicableItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'voucher_id',
        'applicable_type',
        'applicable_id',
    ];

    // Relationships
    public function voucher()
    {
        return $this->belongsTo(Voucher::class);
    }

    public function applicable()
    {
        return $this->morphTo();
    }

    // Helper methods
    public function isProduct()
    {
        return $this->applicable_type === 'product';
    }

    public function isService()
    {
        return $this->applicable_type === 'service';
    }

    public function getApplicableItem()
    {
        if ($this->isProduct()) {
            return \App\Models\Product::find($this->applicable_id);
        } elseif ($this->isService()) {
            return \App\Models\Service::find($this->applicable_id);
        }
        
        return null;
    }
}
