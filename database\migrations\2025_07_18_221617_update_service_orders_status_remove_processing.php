<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing 'processing' status to 'pending'
        DB::table('service_orders')
            ->where('status', 'processing')
            ->update(['status' => 'pending']);

        // Modify the enum to remove 'processing'
        DB::statement("ALTER TABLE service_orders MODIFY COLUMN status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add 'processing' back to enum
        DB::statement("ALTER TABLE service_orders MODIFY COLUMN status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending'");
    }
};
