@extends('layouts.app')

@section('title', '<PERSON> tiết đơn hàng #' . $order->order_number . ' - AccReroll')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-4 lg:py-8">
    <!-- Breadcrumb -->
    <nav class="mb-4 lg:mb-6" aria-label="Breadcrumb">
        <ol class="flex flex-wrap items-center gap-1 lg:gap-2 text-sm lg:text-base">
            <li>
                <a href="{{ route('home') }}" class="text-gray-500 hover:text-blue-600 px-1">
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
            <li><i class="fas fa-chevron-right text-gray-400 text-xs"></i></li>
            <li>
                <a href="{{ route('orders.index') }}" class="text-gray-500 hover:text-blue-600 px-1">
                    <span class="hidden sm:inline">Đ<PERSON>n hàng</span>
                    <span class="sm:hidden">Orders</span>
                </a>
            </li>
            <li><i class="fas fa-chevron-right text-gray-400 text-xs"></i></li>
            <li class="text-gray-900 font-medium px-1 truncate max-w-[120px] sm:max-w-[200px] lg:max-w-none" title="{{ $order->order_number }}">
                {{ $order->order_number }}
            </li>
        </ol>
    </nav>

    <!-- Order Header -->
    <div class="bg-white rounded-lg shadow-md p-4 lg:p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 space-y-2 lg:space-y-0">
            <div>
                <h1 class="text-xl lg:text-2xl font-bold text-gray-900">Đơn hàng {{ $order->order_number }}</h1>
                <p class="text-gray-600 text-sm lg:text-base">Đặt hàng lúc {{ $order->created_at->format('H:i d/m/Y') }}</p>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 self-start lg:self-auto">
                <i class="fas fa-check mr-2"></i>Hoàn thành
            </span>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6">
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Tổng tiền</div>
                <div class="text-lg lg:text-xl font-bold text-red-600">{{ format_money($order->total_amount) }}</div>
            </div>
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Số lượng</div>
                <div class="text-lg lg:text-xl font-bold text-gray-900">{{ $order->orderItems->count() }} sản phẩm</div>
            </div>
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Trạng thái</div>
                <div class="text-lg lg:text-xl font-bold text-green-600">Hoàn thành</div>
            </div>
            <div class="bg-gray-50 p-3 lg:p-4 rounded-lg">
                <div class="text-sm text-gray-500 mb-1">Voucher sử dụng</div>
                @if($order->usedVoucher && $order->usedVoucher->voucher)
                    <div class="text-sm font-medium text-green-600">
                        <i class="fas fa-ticket-alt mr-1"></i>{{ $order->usedVoucher->voucher->code }}
                    </div>
                    <div class="text-xs text-gray-500">
                        @if($order->usedVoucher->voucher->discount_type === 'fixed')
                            Giảm {{ number_format($order->usedVoucher->voucher->discount_value, 0, ',', '.') }}đ
                        @else
                            Giảm {{ $order->usedVoucher->voucher->discount_value }}%
                        @endif
                    </div>
                @else
                    <div class="text-sm text-gray-400">Không sử dụng</div>
                @endif
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="bg-white rounded-lg shadow-md p-4 lg:p-6">
        <h2 class="text-lg lg:text-xl font-semibold text-gray-900 mb-4 lg:mb-6">Sản phẩm đã mua</h2>

        <div class="space-y-6">
            @php
                // Group items by product
                $groupedItems = $order->orderItems->groupBy('product_id');
            @endphp

            @foreach($groupedItems as $productId => $items)
            @php
                $firstItem = $items->first();
                $totalQuantity = $items->sum('quantity');
                $totalPrice = $items->sum(function($item) { return $item->price * $item->quantity; });
            @endphp
            <div class="rounded-lg p-3 lg:p-4">
                <!-- Product Info -->
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 space-y-3 lg:space-y-0">
                    <div class="flex items-center space-x-3 lg:space-x-4" >
                        <div class=" w-14 h-14 lg:w-16 lg:h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            @if($firstItem->product->main_image)
                                <img src="{{ storage_url($firstItem->product->main_image) }}" alt="{{ $firstItem->product->name }}"
                                     class="w-full h-full object-cover rounded-lg">
                            @else
                                <i class="fas fa-gamepad text-gray-400 text-lg lg:text-xl"></i>
                            @endif
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-gray-900 text-sm lg:text-base">{{ $firstItem->product->name }}</h3>
                            <p class="text-xs lg:text-sm text-gray-500">{{ $firstItem->product->category->name }}</p>
                            <p class="text-xs lg:text-sm text-gray-500">Số lượng: {{ $totalQuantity }} tài khoản</p>
                        </div>
                    </div>
                    <div class="text-right lg:text-right">
                        <div class="text-base lg:text-lg font-bold text-red-600">{{ format_money($totalPrice) }}</div>
                        <div class="text-xs lg:text-sm text-gray-500">{{ format_money($firstItem->price) }} x{{ $totalQuantity }}</div>
                    </div>
                </div>

                <!-- Account Details -->
                <div class="space-y-3">
                    @foreach($items as $index => $item)
                    @if($item->productAccount)
                        <!-- Hiển thị tài khoản thật như cũ -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 lg:p-4">
                            <div class="mb-3">
                                <h4 class="font-medium text-blue-900 text-sm lg:text-base">
                                    Tài khoản {{ $index + 1 }}
                                </h4>
                            </div>
                            <div class="space-y-2">
                                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-1 lg:space-y-0">
                                    <span class="text-blue-700 font-medium text-sm">Tên đăng nhập:</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-blue-900 font-mono text-sm break-all">{{ $item->productAccount->username }}</span>
                                        <button onclick="copyToClipboard('{{ $item->productAccount->username }}')"
                                                class="text-blue-600 hover:text-blue-800 text-xs">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-1 lg:space-y-0">
                                    <span class="text-blue-700 font-medium text-sm">Mật khẩu:</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-blue-900 font-mono text-sm break-all">{{ $item->productAccount->password }}</span>
                                        <button onclick="copyToClipboard('{{ $item->productAccount->password }}')"
                                                class="text-blue-600 hover:text-blue-800 text-xs">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Hiển thị mã đơn hàng cho preorder -->
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-3 lg:p-4">
                            <div class="mb-3">
                                <h4 class="font-medium text-orange-900 text-sm lg:text-base">
                                    Sản phẩm {{ $index + 1 }} - Đang chờ xử lý
                                </h4>
                            </div>
                            <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-1 lg:space-y-0">
                                <span class="text-orange-700 font-medium text-sm">Mã đơn hàng:</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-orange-900 font-mono text-sm break-all">{{ $order->order_number }}</span>
                                    <button onclick="copyToClipboard('{{ $order->order_number }}')" class="text-orange-600 hover:text-orange-800 text-xs">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif
                    @endforeach
                </div>
            </div>
            @endforeach
        </div>
    </div>


</div>

<script>
function copyToClipboard(text) {
    const button = event.target.closest('button');

    navigator.clipboard.writeText(text).then(function() {
        showCopySuccess(button);
    }).catch(function(err) {
        // Fallback for older browsers or non-HTTPS
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopySuccess(button);
    });
}

function showCopySuccess(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('text-green-600');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('text-green-600');
    }, 2000);
}
</script>
@endsection
