<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductAccount;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testUser = User::where('username', 'testuser')->first();
        $demoUser = User::where('username', 'demo')->first();
        $products = Product::with('productAccounts')->get();

        // Tạo 2 đơn hàng mẫu
        $orders = [
            [
                'user_id' => $testUser->id,
                'order_number' => 'ORD' . date('Ymd') . '0001',
                'total_amount' => 50000,
                'status' => 'completed',
            ],
            [
                'user_id' => $demoUser->id,
                'order_number' => 'ORD' . date('Ymd') . '0002',
                'total_amount' => 80000,
                'status' => 'pending',
            ],
        ];

        foreach ($orders as $index => $orderData) {
            $order = Order::create($orderData);
            
            // Tạo order item cho mỗi đơn hàng
            $product = $products[$index % $products->count()];
            $productAccount = $product->productAccounts->where('status', 'available')->first();
            
            if ($productAccount) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_account_id' => $productAccount->id,
                    'quantity' => 1,
                    'price' => $product->price,
                ]);

                // Nếu đơn hàng đã hoàn thành, cập nhật trạng thái account
                if ($order->status === 'completed') {
                    $productAccount->update([
                        'status' => 'sold',
                        'sold_at' => now(),
                    ]);
                }
            }
        }
    }
}
