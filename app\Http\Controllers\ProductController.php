<?php

namespace App\Http\Controllers;

use App\Models\Product;

class ProductController extends Controller
{


    /**
     * <PERSON><PERSON><PERSON> thị chi tiết sản phẩm
     */
    public function show($slug)
    {
        $product = Product::with(['category', 'availableAccounts'])
            ->where('slug', $slug)
            ->where('status', 'active')
            ->firstOrFail();

        // Sản phẩm liên quan (cùng danh mục)
        $relatedProducts = Product::with('category')
            ->withCount('availableAccounts')
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('status', 'active')
            ->limit(4)
            ->get();

        return view('products.show', compact('product', 'relatedProducts'));
    }
}
