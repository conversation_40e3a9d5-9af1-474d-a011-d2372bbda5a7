<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class OptimizeImageServing
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only apply to image requests
        if ($request->is('storage/*/*')) {
            // Add cache headers
            $response->header('Cache-Control', 'public, max-age=31536000'); // 1 year
            $response->header('Expires', gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000));
            
            // Add ETag for better caching
            $etag = md5($response->getContent());
            $response->header('ETag', $etag);
            
            // Check if client has cached version
            if ($request->header('If-None-Match') === $etag) {
                return response('', 304);
            }
        }

        return $response;
    }
}
