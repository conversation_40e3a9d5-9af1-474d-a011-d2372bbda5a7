

<?php $__env->startSection('title', 'Lịch sử giao dịch'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Lịch sử giao dịch</h1>
            <p class="text-gray-600 mt-1"><PERSON> dõi tất cả các giao dịch tiền trong hệ thống</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-sm font-medium text-gray-500 uppercase">Tổng giao dịch</div>
            <div class="text-2xl font-bold text-gray-900"><?php echo e(format_money($stats['total_transactions'], false)); ?></div>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-sm font-medium text-gray-500 uppercase">Tổng tiền vào</div>
            <div class="text-2xl font-bold text-green-600"><?php echo e(format_money($stats['total_amount_in'])); ?></div>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-sm font-medium text-gray-500 uppercase">Tổng tiền ra</div>
            <div class="text-2xl font-bold text-red-600"><?php echo e(format_money($stats['total_amount_out'])); ?></div>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
            <div class="text-sm font-medium text-gray-500 uppercase">Admin +/-</div>
            <div class="text-2xl font-bold <?php echo e($stats['admin_net'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                <?php echo e($stats['admin_net'] >= 0 ? '+' : ''); ?><?php echo e(format_money($stats['admin_net'])); ?>

            </div>
            <?php if($stats['admin_add'] > 0 || $stats['admin_subtract'] > 0): ?>
                <div class="text-xs text-gray-500 mt-1">
                    +<?php echo e(format_money($stats['admin_add'])); ?> / -<?php echo e(format_money($stats['admin_subtract'])); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <form method="GET" action="<?php echo e(route('admin.transaction-logs.index')); ?>" class="flex flex-wrap items-center gap-3">
            <!-- Search -->
            <div class="flex-1 min-w-0 sm:min-w-64">
                <input type="text" name="search" value="<?php echo e(request('search')); ?>"
                       placeholder="Tìm kiếm theo mô tả giao dịch..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Type Filter -->
            <div class="w-40">
                <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Tất cả loại</option>
                    <option value="topup_atm" <?php echo e(request('type') == 'topup_atm' ? 'selected' : ''); ?>>Nạp ATM</option>
                    <option value="topup_card" <?php echo e(request('type') == 'topup_card' ? 'selected' : ''); ?>>Nạp thẻ</option>
                    <option value="admin_add" <?php echo e(request('type') == 'admin_add' ? 'selected' : ''); ?>>Admin +</option>
                    <option value="admin_subtract" <?php echo e(request('type') == 'admin_subtract' ? 'selected' : ''); ?>>Admin -</option>
                    <option value="purchase" <?php echo e(request('type') == 'purchase' ? 'selected' : ''); ?>>Mua hàng</option>
                    <option value="service_purchase" <?php echo e(request('type') == 'service_purchase' ? 'selected' : ''); ?>>Đặt dịch vụ</option>
                </select>
            </div>

            <!-- Date From -->
            <div class="w-36">
                <input type="date" name="date_from" value="<?php echo e(request('date_from')); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Date To -->
            <div class="w-36">
                <input type="date" name="date_to" value="<?php echo e(request('date_to')); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors">
                    <i class="fas fa-search"></i>
                </button>
                <?php if(request()->hasAny(['type', 'date_from', 'date_to', 'search'])): ?>
                    <a href="<?php echo e(route('admin.transaction-logs.index')); ?>"
                       class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors">
                        <i class="fas fa-times"></i>
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Transactions Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h3 class="text-lg font-medium text-gray-900">
                Danh sách giao dịch 
                <?php if($transactions->total() > 0): ?>
                    (<?php echo e(format_money($transactions->total(), false)); ?> giao dịch)
                <?php endif; ?>
            </h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số tiền</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mô tả</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">#<?php echo e($transaction->id); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($transaction->created_at->format('d/m/Y')); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($transaction->created_at->format('H:i:s')); ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php if($transaction->user): ?>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($transaction->user->username); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($transaction->user->email); ?></div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-400">N/A</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <?php
                                    $typeColors = [
                                        'topup_atm' => 'bg-blue-100 text-blue-800',
                                        'topup_card' => 'bg-green-100 text-green-800',
                                        'admin_add' => 'bg-purple-100 text-purple-800',
                                        'admin_subtract' => 'bg-red-100 text-red-800',
                                        'purchase' => 'bg-orange-100 text-orange-800',
                                        'service_purchase' => 'bg-orange-100 text-orange-800',
                                        'refund' => 'bg-yellow-100 text-yellow-800',
                                    ];
                                    $typeLabels = [
                                        'topup_atm' => 'Nạp ATM',
                                        'topup_card' => 'Nạp thẻ',
                                        'admin_add' => 'Admin +',
                                        'admin_subtract' => 'Admin -',
                                        'purchase' => 'Mua hàng',
                                        'service_purchase' => 'Đặt dịch vụ',
                                        'refund' => 'Hoàn tiền',
                                    ];
                                ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($typeColors[$transaction->type] ?? 'bg-gray-100 text-gray-800'); ?>">
                                    <?php echo e($typeLabels[$transaction->type] ?? $transaction->type); ?>

                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium <?php echo e($transaction->amount >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                    <?php echo e($transaction->formatted_amount); ?>

                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Trước: <?php echo e(format_money($transaction->balance_before)); ?></div>
                                <div class="text-sm text-gray-500">Sau: <?php echo e(format_money($transaction->balance_after)); ?></div>
                            </td>
                            <td class="px-4 py-4 max-w-xs">
                                <div class="text-sm text-gray-900 truncate" title="<?php echo e($transaction->description); ?>">
                                    <?php echo e($transaction->description ?: '-'); ?>

                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500"><?php echo e($transaction->admin->username ?? 'Hệ thống'); ?></div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="px-4 py-12 text-center">
                            <div class="text-gray-500">
                                <i class="fas fa-receipt text-4xl text-gray-300 mb-4 block"></i>
                                <p class="text-lg">Chưa có giao dịch nào</p>
                                <p class="text-sm">Giao dịch sẽ hiển thị ở đây khi có hoạt động</p>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if($transactions->hasPages()): ?>
        <div class="px-6 py-4 border-t">
            <?php echo e($transactions->appends(request()->query())->links('pagination.custom')); ?>

        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/transaction-logs/index.blade.php ENDPATH**/ ?>