<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Guide;
use App\Models\User;
use Illuminate\Support\Str;

class GuideSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('role', 'admin')->first();

        $guides = [
            [
                'title' => 'Hướng dẫn mua tài khoản game',
                'slug' => 'huong-dan-mua-tai-khoan-game',
                'content' => '<h2>Cách mua tài khoản game</h2>
<p>Để mua tài khoản game trên website, bạn cần thực hiện các bước sau:</p>
<ol>
<li>Đăng ký tài khoản và đăng nhập</li>
<li>Nạp tiền vào tài khoản</li>
<li>Chọn sản phẩm muốn mua</li>
<li><PERSON><PERSON> to<PERSON> và nhận tài khoản</li>
</ol>
<p>L<PERSON>u ý: Vui lòng đổi mật khẩu ngay sau khi nhận tài khoản.</p>',
                'status' => 'published',
                'sort_order' => 1,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Hướng dẫn sử dụng dịch vụ',
                'slug' => 'huong-dan-su-dung-dich-vu',
                'content' => '<h2>Cách sử dụng dịch vụ</h2>
<p>Để sử dụng dịch vụ trên website:</p>
<ol>
<li>Chọn dịch vụ phù hợp</li>
<li>Điền thông tin yêu cầu</li>
<li>Thanh toán</li>
<li>Chờ admin xử lý</li>
</ol>
<p>Thời gian xử lý thường từ 1-24 giờ tùy loại dịch vụ.</p>',
                'status' => 'published',
                'sort_order' => 2,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Chính sách bảo hành',
                'slug' => 'chinh-sach-bao-hanh',
                'content' => '<h2>Chính sách bảo hành</h2>
<p>Chúng tôi cam kết:</p>
<ul>
<li>Bảo hành tài khoản trong 7 ngày</li>
<li>Hỗ trợ 24/7 qua Telegram</li>
<li>Hoàn tiền 100% nếu tài khoản lỗi</li>
</ul>
<p>Liên hệ admin để được hỗ trợ nhanh nhất.</p>',
                'status' => 'published',
                'sort_order' => 3,
                'created_by' => $admin->id,
            ],
        ];

        foreach ($guides as $guide) {
            Guide::create($guide);
        }
    }
}
