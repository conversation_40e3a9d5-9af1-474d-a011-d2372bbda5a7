<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Voucher extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // Khi voucher bị deactivate, xóa user_vouchers chưa sử dụng
        static::updated(function (Voucher $voucher) {
            if ($voucher->isDirty('is_active') && !$voucher->is_active) {
                $voucher->userVouchers()
                    ->whereNull('used_at')
                    ->delete();
            }
        });

        // Khi voucher bị xóa, user_vouchers sẽ tự động bị xóa theo cascade constraint
        // Không cần xử lý thêm gì
    }

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'discount_type',
        'discount_value',
        'applicable_to',
        'category_id',
        'min_order_amount',
        'max_uses_per_user',
        'total_uses_limit',
        'expires_at',
        'is_active',
        'created_by_admin_id',
        'auto_config',
        'trigger_conditions',
        'user_expires_days',
        'specific_items_only',
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'min_order_amount' => 'decimal:2',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'auto_config' => 'array',
        'trigger_conditions' => 'array',
        'specific_items_only' => 'boolean',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function createdByAdmin()
    {
        return $this->belongsTo(User::class, 'created_by_admin_id');
    }

    public function userVouchers()
    {
        return $this->hasMany(UserVoucher::class);
    }

    public function applicableItems()
    {
        return $this->hasMany(VoucherApplicableItem::class);
    }

    public function applicableProducts()
    {
        return $this->applicableItems()->where('applicable_type', 'product');
    }

    public function applicableServices()
    {
        return $this->applicableItems()->where('applicable_type', 'service');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeManual($query)
    {
        return $query->where('type', 'manual');
    }

    public function scopeAuto($query)
    {
        return $query->where('type', 'auto_product_purchase');
    }

    public function scopeAutoTrigger($query)
    {
        return $query->where('type', 'auto_product_purchase')
                    ->whereNotNull('trigger_conditions');
    }

    public function getTriggerConditionsArrayAttribute()
    {
        $conditions = $this->trigger_conditions;

        // Ensure conditions is an array
        if (is_string($conditions)) {
            $conditions = json_decode($conditions, true) ?? [];
        }

        return is_array($conditions) ? $conditions : [];
    }

    public function getAutoConfigArrayAttribute()
    {
        $config = $this->auto_config;

        // Ensure config is an array
        if (is_string($config)) {
            $config = json_decode($config, true) ?? [];
        }

        return is_array($config) ? $config : [];
    }

    // Helper methods
    public function isExpired()
    {
        return $this->expires_at && $this->expires_at < now();
    }

    public function isApplicableToProducts()
    {
        return in_array($this->applicable_to, ['products', 'both']);
    }

    public function isApplicableToServices()
    {
        return in_array($this->applicable_to, ['services', 'both']);
    }

    public function canBeUsedByUser($userId)
    {
        if (!$this->is_active || $this->isExpired()) {
            return false;
        }

        // Sử dụng direct query thay vì cache để đảm bảo tính chính xác
        $userUsageCount = $this->userVouchers()
            ->where('user_id', $userId)
            ->whereNotNull('used_at')
            ->count();

        return $userUsageCount < $this->max_uses_per_user;
    }

    public function getTotalUsageCount()
    {
        // Sử dụng direct query thay vì cache để đảm bảo tính chính xác
        return $this->userVouchers()->whereNotNull('used_at')->count();
    }

    public function hasReachedTotalLimit()
    {
        if (!$this->total_uses_limit) {
            return false;
        }

        return $this->getTotalUsageCount() >= $this->total_uses_limit;
    }

    /**
     * Bulk check multiple vouchers for user usage
     */
    public static function checkMultipleVouchersForUser($voucherIds, $userId)
    {
        $usageCounts = UserVoucher::whereIn('voucher_id', $voucherIds)
            ->where('user_id', $userId)
            ->whereNotNull('used_at')
            ->groupBy('voucher_id')
            ->selectRaw('voucher_id, COUNT(*) as usage_count')
            ->pluck('usage_count', 'voucher_id')
            ->toArray();

        return $usageCounts;
    }
}
