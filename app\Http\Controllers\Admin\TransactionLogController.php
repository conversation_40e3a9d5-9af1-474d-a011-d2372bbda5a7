<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BalanceTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TransactionLogController extends Controller
{
    /**
     * Hiển thị danh sách tất cả giao dịch
     */
    public function index(Request $request)
    {
        $query = BalanceTransaction::with(['user', 'admin', 'reference'])
            ->latest();

        // Filter theo type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter theo user
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter theo ngày
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search theo description
        if ($request->filled('search')) {
            $query->where('description', 'like', '%' . $request->search . '%');
        }

        $transactions = $query->paginate(50);

        // Thống kê
        $stats = $this->getTransactionStats($request);

        // Danh sách users cho filter
        $users = User::where('role', 'user')
            ->select('id', 'username', 'email')
            ->orderBy('username')
            ->get();

        return view('admin.transaction-logs.index', compact('transactions', 'stats', 'users'));
    }

    /**
     * Lấy thống kê giao dịch
     */
    private function getTransactionStats($request)
    {
        $query = BalanceTransaction::query();

        // Áp dụng cùng filter như danh sách
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        if ($request->filled('search')) {
            $query->where('description', 'like', '%' . $request->search . '%');
        }

        return [
            'total_transactions' => $query->count(),
            'total_amount_in' => (clone $query)->whereIn('type', [
                BalanceTransaction::TYPE_TOPUP_ATM,
                BalanceTransaction::TYPE_TOPUP_CARD,
                BalanceTransaction::TYPE_ADMIN_ADD,
                BalanceTransaction::TYPE_REFUND
            ])->sum('amount'),
            'total_amount_out' => abs((clone $query)->whereIn('type', [
                BalanceTransaction::TYPE_PURCHASE,
                BalanceTransaction::TYPE_ADMIN_SUBTRACT
            ])->sum('amount')),
            'admin_add' => (clone $query)->where('type', BalanceTransaction::TYPE_ADMIN_ADD)->sum('amount'),
            'admin_subtract' => abs((clone $query)->where('type', BalanceTransaction::TYPE_ADMIN_SUBTRACT)->sum('amount')),
            'admin_net' => (clone $query)->where('type', BalanceTransaction::TYPE_ADMIN_ADD)->sum('amount') -
                          abs((clone $query)->where('type', BalanceTransaction::TYPE_ADMIN_SUBTRACT)->sum('amount')),
        ];
    }



    /**
     * Export giao dịch ra CSV
     */
    public function export(Request $request)
    {
        $query = BalanceTransaction::with(['user', 'admin'])
            ->latest();

        // Áp dụng cùng filter
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $transactions = $query->get();

        $filename = 'transaction_logs_' . now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');
            
            // Header CSV
            fputcsv($file, [
                'ID',
                'Thời gian',
                'User',
                'Loại giao dịch',
                'Số tiền',
                'Balance trước',
                'Balance sau',
                'Mô tả',
                'Admin thực hiện'
            ]);

            // Data
            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->created_at->format('d/m/Y H:i:s'),
                    $transaction->user->username ?? 'N/A',
                    $this->getTypeLabel($transaction->type),
                    format_money($transaction->amount, false),
                    format_money($transaction->balance_before, false),
                    format_money($transaction->balance_after, false),
                    $transaction->description,
                    $transaction->admin->username ?? 'Hệ thống'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Lấy label cho type
     */
    private function getTypeLabel($type)
    {
        $labels = [
            BalanceTransaction::TYPE_TOPUP_ATM => 'Nạp ATM',
            BalanceTransaction::TYPE_TOPUP_CARD => 'Nạp thẻ cào',
            BalanceTransaction::TYPE_ADMIN_ADD => 'Admin cộng tiền',
            BalanceTransaction::TYPE_ADMIN_SUBTRACT => 'Admin trừ tiền',
            BalanceTransaction::TYPE_PURCHASE => 'Mua hàng',
            BalanceTransaction::TYPE_REFUND => 'Hoàn tiền',
        ];

        return $labels[$type] ?? $type;
    }
}
