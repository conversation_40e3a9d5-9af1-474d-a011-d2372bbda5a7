@extends('layouts.admin')

@section('title', 'Chi tiết Voucher')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Chi tiết Voucher</h1>
            <p class="text-sm text-gray-600 mt-1">Thông tin chi tiết và thống kê sử dụng voucher</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.vouchers.index') }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
            @if(!$voucher->trashed())
                <a href="{{ route('admin.vouchers.edit', $voucher) }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <i class="fas fa-edit mr-2"></i>Chỉnh sửa
                </a>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Info -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Thông tin cơ bản</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tên voucher</label>
                        <p class="text-gray-900 font-medium">{{ $voucher->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Mã voucher</label>
                        <code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">{{ $voucher->code }}</code>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Loại</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                            {{ $voucher->type === 'manual' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                            {{ $voucher->type === 'manual' ? 'Thủ công' : 'Tự động' }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Giảm giá</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            @if($voucher->discount_type === 'fixed')
                                -{{ format_money($voucher->discount_value, false) }}đ
                            @else
                                -{{ $voucher->discount_value }}%
                            @endif
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Áp dụng cho</label>
                        <div class="flex items-center space-x-2">
                            @if($voucher->applicable_to === 'products')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-box mr-1"></i>Sản phẩm
                                </span>
                            @elseif($voucher->applicable_to === 'services')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-cogs mr-1"></i>Dịch vụ
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-globe mr-1"></i>Tất cả
                                </span>
                            @endif
                            
                            @if($voucher->category)
                                <span class="text-sm text-gray-600">- {{ $voucher->category->name }}</span>
                            @endif
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Đơn tối thiểu</label>
                        <p class="text-gray-900">
                            @if($voucher->min_order_amount)
                                {{ number_format($voucher->min_order_amount, 0, ',', '.') }} đ
                            @else
                                <span class="text-gray-400">Không giới hạn</span>
                            @endif
                        </p>
                    </div>
                </div>
                
                @if($voucher->description)
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
                        <p class="text-gray-900">{{ $voucher->description }}</p>
                    </div>
                @endif
            </div>

            <!-- Usage Statistics -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Thống kê sử dụng</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ $usageStats['total_granted'] }}</div>
                        <div class="text-sm text-gray-600">Tổng cấp phát</div>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">{{ $usageStats['total_used'] }}</div>
                        <div class="text-sm text-gray-600">Đã sử dụng</div>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ $usageStats['usage_rate'] }}%</div>
                        <div class="text-sm text-gray-600">Tỷ lệ sử dụng</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Trạng thái</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Trạng thái:</span>
                        @if($voucher->trashed())
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Đã lưu trữ
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                {{ $voucher->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $voucher->is_active ? 'Hoạt động' : 'Tạm dừng' }}
                            </span>
                        @endif
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Hết hạn:</span>
                        @if($voucher->expires_at)
                            <span class="text-sm {{ $voucher->expires_at < now() ? 'text-red-600' : 'text-gray-900' }}">
                                {{ $voucher->expires_at->format('d/m/Y H:i') }}
                            </span>
                        @else
                            <span class="text-sm text-green-600">Không giới hạn</span>
                        @endif
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Tạo bởi:</span>
                        <span class="text-sm text-gray-900">
                            {{ $voucher->createdByAdmin->name ?? 'System' }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Ngày tạo:</span>
                        <span class="text-sm text-gray-900">{{ $voucher->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                </div>
            </div>

            <!-- Limits -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Giới hạn</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Mỗi user:</span>
                        <span class="text-sm text-gray-900">{{ $voucher->max_uses_per_user }} lần</span>
                    </div>
                    
                    @if($voucher->total_uses_limit)
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Tổng giới hạn:</span>
                            <span class="text-sm text-gray-900">{{ $voucher->total_uses_limit }} lần</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- User Vouchers Table -->
    @if($voucher->userVouchers->count() > 0)
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Lịch sử cấp phát</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Người dùng
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ngày cấp
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Trạng thái
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ngày sử dụng
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($voucher->userVouchers->take(20) as $userVoucher)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $userVoucher->user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $userVoucher->user->email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $userVoucher->created_at->format('d/m/Y H:i') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($userVoucher->used_at)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Đã sử dụng
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Chưa sử dụng
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @if($userVoucher->used_at)
                                        {{ $userVoucher->used_at->format('d/m/Y H:i') }}
                                    @else
                                        <span class="text-gray-400">-</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            @if($voucher->userVouchers->count() > 20)
                <div class="px-6 py-3 bg-gray-50 text-center">
                    <span class="text-sm text-gray-500">Hiển thị 20/{{ $voucher->userVouchers->count() }} bản ghi</span>
                </div>
            @endif
        </div>
    @endif
</div>
@endsection
