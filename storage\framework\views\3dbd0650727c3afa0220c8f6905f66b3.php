<?php $__env->startSection('title', '<PERSON> tiết đơn dịch vụ #' . ($serviceOrder->order_number ?? $serviceOrder->id) . ' - AccReroll'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-4 sm:mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 text-xs sm:text-sm">
            <li class="inline-flex items-center">
                <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-1 sm:mr-2"></i>
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-1 sm:mx-2 text-xs"></i>
                    <a href="<?php echo e(route('service-orders.index')); ?>" class="font-medium text-gray-700 hover:text-blue-600 truncate">
                        <span class="hidden sm:inline">Lịch sử đơn dịch vụ</span>
                        <span class="sm:hidden">Lịch sử</span>
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-1 sm:mx-2 text-xs"></i>
                    <span class="font-medium text-gray-500 truncate max-w-20 sm:max-w-none"><?php echo e($serviceOrder->order_number ?? 'SRV' . str_pad($serviceOrder->id, 4, '0', STR_PAD_LEFT)); ?></span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="bg-white rounded-lg shadow mb-4 sm:mb-6 p-3 sm:p-4 lg:p-6">
        <div class="flex flex-col space-y-3 sm:space-y-4">
            <!-- Order Number and Date -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                <div class="min-w-0 flex-1">
                    <h1 class="text-base sm:text-lg lg:text-2xl font-bold text-gray-900 truncate"><?php echo e($serviceOrder->order_number ?? 'SRV' . str_pad($serviceOrder->id, 4, '0', STR_PAD_LEFT)); ?></h1>
                    <p class="text-xs sm:text-sm lg:text-base text-gray-600 mt-1"><?php echo e($serviceOrder->created_at->format('d/m/Y H:i')); ?></p>
                </div>
                <!-- Price - Mobile: Full width, Desktop: Right aligned -->
                <div class="sm:ml-4 sm:text-right">
                    <div class="text-lg sm:text-xl lg:text-2xl font-bold text-red-600"><?php echo e($serviceOrder->formatted_price); ?></div>
                </div>
            </div>

            <!-- Status Badge - Full width on mobile -->
            <div class="flex justify-start sm:justify-end">
                <span class="inline-flex items-center px-3 py-1.5 sm:px-2.5 sm:py-1 rounded-full text-xs sm:text-sm font-medium bg-<?php echo e($serviceOrder->status_color); ?>-100 text-<?php echo e($serviceOrder->status_color); ?>-800">
                    <?php if($serviceOrder->status === 'pending'): ?>
                        <i class="fas fa-clock mr-1.5 sm:mr-1 lg:mr-2"></i>
                    <?php elseif($serviceOrder->status === 'completed'): ?>
                        <i class="fas fa-check-circle mr-1.5 sm:mr-1 lg:mr-2"></i>
                    <?php elseif($serviceOrder->status === 'cancelled'): ?>
                        <i class="fas fa-times-circle mr-1.5 sm:mr-1 lg:mr-2"></i>
                    <?php endif; ?>
                    <?php echo e($serviceOrder->status_text); ?>

                </span>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-4 sm:space-y-6">
            <!-- Service Info -->
            <div class="bg-white rounded-lg shadow p-3 sm:p-4 lg:p-6">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
                    <i class="fas fa-cogs text-blue-600 mr-2"></i>Thông tin dịch vụ
                </h2>

                <div class="space-y-3 sm:space-y-4">
                    <div>
                        <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Tên dịch vụ</label>
                        <p class="text-sm sm:text-base text-gray-900 break-words"><?php echo e($serviceOrder->service->name ?? 'N/A'); ?></p>
                    </div>

                    <div>
                        <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Mô tả dịch vụ</label>
                        <div class="text-xs sm:text-sm text-gray-600 prose prose-sm max-w-none break-words">
                            <?php echo nl2br(e($serviceOrder->service->description ?? 'Không có mô tả')); ?>

                        </div>
                    </div>

                    <div>
                        <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Nội dung yêu cầu</label>
                        <div class="bg-gray-50 rounded-lg p-3 sm:p-4">
                            <p class="text-xs sm:text-sm text-gray-900 whitespace-pre-wrap break-words"><?php echo e($serviceOrder->content); ?></p>
                        </div>
                    </div>

                    <?php if($serviceOrder->notes): ?>
                        <div>
                            <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Ghi chú thêm</label>
                            <div class="bg-blue-50 rounded-lg p-3 sm:p-4">
                                <p class="text-xs sm:text-sm text-blue-900 whitespace-pre-wrap break-words"><?php echo e($serviceOrder->notes); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Admin Response -->
            <?php if($serviceOrder->admin_notes && $serviceOrder->status === 'completed'): ?>
                <div class="bg-white rounded-lg shadow p-3 sm:p-4 lg:p-6">
                    <h2 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
                        <i class="fas fa-reply text-green-600 mr-2"></i>Phản hồi từ Admin
                    </h2>

                    <div class="bg-green-50 rounded-lg p-3 sm:p-4">
                        <p class="text-xs sm:text-sm text-green-900 whitespace-pre-wrap break-words"><?php echo e($serviceOrder->admin_notes); ?></p>
                        <?php if($serviceOrder->completed_at): ?>
                            <p class="text-xs sm:text-sm text-green-600 mt-2">
                                <i class="fas fa-clock mr-1"></i>Hoàn thành lúc: <?php echo e($serviceOrder->completed_at->format('H:i d/m/Y')); ?>

                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4 sm:space-y-6">
            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow p-3 sm:p-4 lg:p-6">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
                    <i class="fas fa-receipt text-blue-600 mr-2"></i>Tóm tắt đơn hàng
                </h2>

                <div class="space-y-2 sm:space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-xs sm:text-sm text-gray-600">Giá dịch vụ:</span>
                        <span class="text-xs sm:text-sm font-medium"><?php echo e(format_money($serviceOrder->service->price ?? 0)); ?></span>
                    </div>

                    <?php if($serviceOrder->usedVoucher): ?>
                        <div class="flex justify-between items-center text-green-600">
                            <span class="text-xs sm:text-sm">Giảm giá:</span>
                            <span class="text-xs sm:text-sm">-<?php echo e(format_money(($serviceOrder->service->price ?? 0) - $serviceOrder->price, false)); ?>đ</span>
                        </div>
                    <?php endif; ?>

                    <hr class="border-gray-200">

                    <div class="flex justify-between items-center text-sm sm:text-lg font-semibold">
                        <span>Tổng tiền:</span>
                        <span class="text-blue-600"><?php echo e($serviceOrder->formatted_price); ?></span>
                    </div>
                </div>
            </div>

            <!-- Voucher Info -->
            <?php if($serviceOrder->usedVoucher): ?>
                <div class="bg-white rounded-lg shadow p-3 sm:p-4 lg:p-6">
                    <h2 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
                        <i class="fas fa-ticket-alt text-green-600 mr-2"></i>Voucher đã sử dụng
                    </h2>

                    <div class="bg-green-50 rounded-lg p-3 sm:p-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-tag text-green-600 mr-2"></i>
                            <span class="text-xs sm:text-sm font-medium text-green-900 break-words"><?php echo e($serviceOrder->usedVoucher->voucher->name ?? 'Voucher'); ?></span>
                        </div>
                        <p class="text-xs sm:text-sm text-green-700">
                            Mã: <code class="bg-green-100 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-xs"><?php echo e($serviceOrder->usedVoucher->voucher->code ?? 'N/A'); ?></code>
                        </p>
                        <p class="text-xs sm:text-sm text-green-700 mt-1">
                            <?php if($serviceOrder->usedVoucher->voucher->discount_type === 'fixed'): ?>
                                Giảm: <?php echo e(number_format($serviceOrder->usedVoucher->voucher->discount_value ?? 0, 0, ',', '.')); ?>đ
                            <?php else: ?>
                                Giảm: <?php echo e($serviceOrder->usedVoucher->voucher->discount_value ?? 0); ?>%
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Status Timeline -->
            <div class="bg-white rounded-lg shadow p-3 sm:p-4 lg:p-6">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">
                    <i class="fas fa-history text-blue-600 mr-2"></i>Trạng thái đơn hàng
                </h2>

                <div class="space-y-3 sm:space-y-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-plus text-blue-600 text-xs sm:text-sm"></i>
                        </div>
                        <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                            <p class="text-xs sm:text-sm font-medium text-gray-900">Đơn hàng được tạo</p>
                            <p class="text-xs text-gray-500"><?php echo e($serviceOrder->created_at->format('H:i d/m/Y')); ?></p>
                        </div>
                    </div>

                    <?php if($serviceOrder->status !== 'pending'): ?>
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-cog text-yellow-600 text-xs sm:text-sm"></i>
                            </div>
                            <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                                <p class="text-xs sm:text-sm font-medium text-gray-900">Đang xử lý</p>
                                <p class="text-xs text-gray-500">Admin đã tiếp nhận</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($serviceOrder->status === 'completed'): ?>
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-green-600 text-xs sm:text-sm"></i>
                            </div>
                            <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                                <p class="text-xs sm:text-sm font-medium text-gray-900">Hoàn thành</p>
                                <p class="text-xs text-gray-500"><?php echo e($serviceOrder->completed_at->format('H:i d/m/Y')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($serviceOrder->status === 'cancelled'): ?>
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-times text-red-600 text-xs sm:text-sm"></i>
                            </div>
                            <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                                <p class="text-xs sm:text-sm font-medium text-gray-900">Đã hủy</p>
                                <p class="text-xs text-gray-500">Đơn hàng bị hủy</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/service-orders/show.blade.php ENDPATH**/ ?>