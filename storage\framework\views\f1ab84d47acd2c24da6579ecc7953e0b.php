<?php $__env->startSection('title', $service->name . ' - AccReroll'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-6 lg:mb-8" aria-label="Breadcrumb">
        <ol class="flex flex-wrap items-center gap-1 md:gap-2">
            <li class="flex items-center">
                <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 px-2 py-1 rounded">
                    <i class="fas fa-home mr-1 md:mr-2"></i>
                    <span class="hidden sm:inline">Trang chủ</span>
                    <span class="sm:hidden">Home</span>
                </a>
            </li>
            <li class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                <a href="<?php echo e(route('category.show', $service->category->slug)); ?>" class="text-sm font-medium text-gray-700 hover:text-blue-600 px-2 py-1 rounded">
                    <span class="hidden sm:inline"><?php echo e($service->category->name); ?></span>
                    <span class="sm:hidden"><?php echo e(Str::limit($service->category->name, 10)); ?></span>
                </a>
            </li>
            <li aria-current="page" class="flex items-center">
                <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                <span class="text-sm font-medium text-gray-500 px-2 py-1 rounded max-w-[120px] sm:max-w-[200px] lg:max-w-none truncate" title="<?php echo e($service->name); ?>">
                    <?php echo e($service->name); ?>

                </span>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        <!-- Service Images -->
        <div class="space-y-3 lg:space-y-4">
            <!-- Main Image -->
            <div class="aspect-[5/3] bg-gray-100 rounded-lg overflow-hidden">
                <?php if($service->first_image): ?>
                    <img id="main-image" src="<?php echo e($service->first_image); ?>" alt="<?php echo e($service->name); ?>"
                         class="w-full h-full object-contain">
                <?php else: ?>
                    <div class="w-full h-full flex items-center justify-center">
                        <i class="fas fa-tools text-gray-400 text-4xl lg:text-6xl"></i>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Service Info -->
        <div class="space-y-4 lg:space-y-6">
            <!-- Service Title & Category -->
            <div>
                <div class="flex items-center gap-2 mb-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <?php echo e($service->category->name); ?>

                    </span>
                </div>
                <h1 class="text-2xl lg:text-3xl font-bold text-gray-900"><?php echo e($service->name); ?></h1>
            </div>

            <!-- Price -->
            <div class="border-t border-b border-gray-200 py-3 lg:py-4">
                <span class="text-2xl lg:text-3xl font-bold text-orange-600"><?php echo e($service->formatted_price); ?></span>
            </div>

            <!-- Desktop Purchase Section -->
            <div class="hidden lg:block space-y-3">
                <?php if(auth()->guard()->check()): ?>
                    <button onclick="openPurchaseModal()"
                            class="w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Đặt ngay
                    </button>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>"
                       class="block w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg text-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Đăng nhập
                    </a>
                <?php endif; ?>
            </div>

            <!-- Service Description -->
            <?php if($service->description): ?>
            <div class="mt-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 shadow-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-white text-sm"></i>
                        </div>
                        <h3 class="text-xl font-bold text-blue-900">Mô tả dịch vụ</h3>
                    </div>
                    <div class="prose prose-sm max-w-none text-gray-800 leading-relaxed">
                        <?php echo nl2br(e($service->description)); ?>

                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Related Services -->
    <?php if($relatedServices->count() > 0): ?>
    <div class="mt-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Dịch vụ liên quan</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                <?php $__currentLoopData = $relatedServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                        <!-- Service Image -->
                        <div class="relative">
                            <?php if($relatedService->first_image): ?>
                                <img src="<?php echo e($relatedService->first_image); ?>"
                                     alt="<?php echo e($relatedService->name); ?>"
                                     class="w-full h-40 object-contain">
                            <?php else: ?>
                                <div class="w-full h-40 bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center">
                                    <i class="fas fa-tools text-white text-3xl"></i>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Service Info -->
                        <div class="p-4 flex flex-col flex-1">
                            <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2"><?php echo e($relatedService->name); ?></h3>
                            <p class="text-sm text-gray-500 mb-3"><?php echo e($relatedService->category->name); ?></p>

                            <!-- Bottom section - always at bottom -->
                            <div class="mt-auto">
                                <div class="text-orange-600 font-bold text-xl"><?php echo e($relatedService->formatted_price); ?></div>
                            </div>
                        </div>

                        <!-- Clickable overlay -->
                        <a href="<?php echo e(route('services.show', $relatedService)); ?>" class="absolute inset-0 z-10"></a>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Mobile Purchase Button -->
<div class="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-40">
    <?php if(auth()->guard()->check()): ?>
        <button onclick="openPurchaseModal()"
                class="w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg">
            <i class="fas fa-shopping-cart mr-2"></i>
            Đặt ngay - <?php echo e($service->formatted_price); ?>

        </button>
    <?php else: ?>
        <a href="<?php echo e(route('login')); ?>"
           class="block w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg text-center">
            <i class="fas fa-sign-in-alt mr-2"></i>
            Đăng nhập
        </a>
    <?php endif; ?>
</div>

<!-- Purchase Modal -->
<?php if(auth()->guard()->check()): ?>
<div id="purchaseModal" class="fixed inset-0 hidden z-50" style="backdrop-filter: blur(4px); background-color: rgba(0, 0, 0, 0.4);">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Xác nhận đặt dịch vụ</h3>
                <button onclick="closePurchaseModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="purchaseForm" onsubmit="submitPurchase(event)">
                <?php echo csrf_field(); ?>
                <!-- Content Input -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Thông tin cần thiết <span class="text-red-500">*</span>
                    </label>

                    <textarea name="content"
                              required
                              rows="4"
                              placeholder="<?php echo e($service->order_instructions ?: 'Nhập code, thông tin tài khoản hoặc yêu cầu cụ thể...'); ?>"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>

                <!-- Voucher Section -->
                <div class="mb-4 border-t border-gray-200 pt-4">
                    <label class="text-sm font-medium text-gray-700 mb-3 block">Mã giảm giá</label>

                    <!-- Available Vouchers List -->
                    <div id="availableVouchers" class="hidden mb-3">
                        <div class="text-xs text-gray-600 mb-2">Voucher khả dụng:</div>
                        <div id="vouchersList" class="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2 bg-gray-50"></div>
                    </div>

                    <!-- Voucher Input -->
                    <div class="flex space-x-2">
                        <div class="flex-1">
                            <input type="text"
                                   id="service_voucher_code"
                                   name="voucher_code"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                   placeholder="Nhập mã voucher hoặc chọn từ danh sách"
                                   oninput="toggleServiceVoucherApplyButton()">
                        </div>
                        <button type="button"
                                onclick="toggleVouchersList()"
                                class="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                                title="Hiển thị voucher khả dụng">
                            <i class="fas fa-list"></i>
                        </button>
                        <button type="button"
                                id="applyServiceVoucherBtn"
                                onclick="validateServiceVoucher()"
                                disabled
                                class="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200 text-sm">
                            Áp dụng
                        </button>
                    </div>

                    <!-- Voucher validation message -->
                    <div id="service-voucher-message" class="mt-2 text-sm hidden"></div>

                    <!-- Applied voucher display -->
                    <div id="service-applied-voucher" class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md text-sm text-green-800 hidden">
                        <div class="flex items-center justify-between">
                            <span id="service-voucher-info"></span>
                            <button type="button" onclick="removeServiceVoucher()" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Price -->
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <div class="flex justify-between items-center">
                        <span class="font-medium">Tổng tiền:</span>
                        <span class="text-xl font-bold text-blue-600" id="serviceTotal"><?php echo e($service->formatted_price); ?></span>
                    </div>
                </div>

                <!-- Balance Check Warning -->
                <div id="balanceWarning" class="hidden mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-red-800 font-medium text-sm">Số dư không đủ</p>
                            <p class="text-red-600 text-sm" id="balanceWarningText">Số tiền cần nạp thêm: 0 đ</p>
                        </div>
                        <a href="<?php echo e(route('topup.index')); ?>"
                           class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                            Nạp tiền
                        </a>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex items-center justify-end space-x-3">
                    <button type="button"
                            onclick="closePurchaseModal()"
                            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Hủy
                    </button>
                    <button type="submit"
                            id="confirmServiceButton"
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        Xác nhận
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Image gallery
function changeMainImage(src, element) {
    document.getElementById('mainImage').src = src;
    
    // Update border styles
    document.querySelectorAll('.aspect-square img').forEach(img => {
        img.classList.remove('border-blue-500');
        img.classList.add('border-gray-200');
    });
    element.classList.remove('border-gray-200');
    element.classList.add('border-blue-500');
}

// Purchase modal
function openPurchaseModal() {
    document.getElementById('purchaseModal').classList.remove('hidden');
}

function closePurchaseModal() {
    document.getElementById('purchaseModal').classList.add('hidden');
}

// Submit purchase
async function submitPurchase(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    try {
        const response = await fetch('<?php echo e(route("services.purchase", $service)); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (result.success) {
            // Redirect to service order detail page
            window.location.href = result.redirect;
        } else {
            alert(result.message);
        }
    } catch (error) {
        alert('Có lỗi xảy ra. Vui lòng thử lại.');
    }
}

// Close modal when clicking outside
document.getElementById('purchaseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePurchaseModal();
    }
});

// Service voucher functionality
let serviceVoucher = null;
let availableVouchers = [];
const serviceOriginalPrice = <?php echo e($service->price); ?>;
const userBalance = <?php echo e(Auth::user()->balance ?? 0); ?>;

// Toggle vouchers list visibility
function toggleVouchersList() {
    const vouchersContainer = document.getElementById('availableVouchers');

    if (vouchersContainer.classList.contains('hidden')) {
        loadAvailableVouchers();
    } else {
        vouchersContainer.classList.add('hidden');
    }
}

// Load available vouchers
function loadAvailableVouchers() {
    fetch('/vouchers/available?order_type=service&category_id=<?php echo e($service->category_id); ?>')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            availableVouchers = data.vouchers;
            displayVouchersList();
        })
        .catch(error => {
            console.error('Error loading vouchers:', error);
            alert('Có lỗi xảy ra khi tải voucher: ' + error.message);
        });
}

// Display vouchers list
function displayVouchersList() {
    const vouchersContainer = document.getElementById('availableVouchers');
    const vouchersList = document.getElementById('vouchersList');

    if (availableVouchers.length === 0) {
        vouchersList.innerHTML = '<div class="text-gray-500 text-xs p-2">Không có voucher khả dụng cho dịch vụ này</div>';
    } else {
        vouchersList.innerHTML = availableVouchers.map(voucher => {
            return `
                <div class="voucher-item p-2 border border-gray-200 rounded cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
                     onclick="selectVoucher('${voucher.code}')">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="font-medium text-sm text-gray-900">${voucher.name}</div>
                            <div class="text-xs text-gray-600">Mã: ${voucher.code}</div>
                            <div class="text-xs text-green-600">
                                ${voucher.discount_type === 'fixed' ?
                                    `Giảm ${new Intl.NumberFormat('de-DE').format(voucher.discount_value)}đ` :
                                    `Giảm ${voucher.discount_value}%`
                                }
                            </div>
                            ${voucher.min_order_amount ? `<div class="text-xs text-gray-500">Đơn tối thiểu: ${new Intl.NumberFormat('de-DE').format(voucher.min_order_amount)}đ</div>` : ''}
                        </div>
                        <button type="button" class="text-blue-600 hover:text-blue-800 text-xs">
                            Chọn
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    vouchersContainer.classList.remove('hidden');
}

// Toggle apply button state based on voucher input
function toggleServiceVoucherApplyButton() {
    const voucherInput = document.getElementById('service_voucher_code');
    const applyBtn = document.getElementById('applyServiceVoucherBtn');
    const hasValue = voucherInput.value.trim().length > 0;

    if (hasValue) {
        applyBtn.disabled = false;
        applyBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm';
    } else {
        applyBtn.disabled = true;
        applyBtn.className = 'px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed transition-colors duration-200 text-sm';
    }
}

// Select voucher from list
function selectVoucher(code) {
    document.getElementById('service_voucher_code').value = code;
    document.getElementById('availableVouchers').classList.add('hidden');
    toggleServiceVoucherApplyButton(); // Update button state
    validateServiceVoucher();
}

function validateServiceVoucher() {
    const voucherCode = document.getElementById('service_voucher_code').value.trim();
    const messageDiv = document.getElementById('service-voucher-message');
    const applyBtn = document.getElementById('applyServiceVoucherBtn');

    // Prevent validation if button is disabled
    if (applyBtn.disabled) {
        return;
    }

    if (!voucherCode) {
        removeServiceVoucher();
        return;
    }

    fetch('/vouchers/validate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            voucher_code: voucherCode,
            order_type: 'service',
            category_id: '<?php echo e($service->category_id); ?>',
            total_amount: serviceOriginalPrice,
            item_ids: [<?php echo e($service->id); ?>]
        })
    })
    .then(response => {
        if (response.status === 429) {
            // Handle rate limiting
            return response.json().then(data => {
                throw new Error(data.message || 'Quá nhiều yêu cầu. Vui lòng thử lại sau.');
            });
        }
        return response.json();
    })
    .then(data => {
        messageDiv.classList.remove('hidden');

        if (data.valid) {
            messageDiv.className = 'mt-2 text-sm text-green-600';
            messageDiv.textContent = 'Voucher hợp lệ!';

            serviceVoucher = data;
            showServiceAppliedVoucher(data);
            updateServiceTotal(); // Sẽ kiểm tra lại số dư với giá mới
        } else {
            messageDiv.className = 'mt-2 text-sm text-red-600';
            messageDiv.textContent = data.message;
            // Không reset input, chỉ clear voucher data và applied voucher display
            serviceVoucher = null;
            document.getElementById('service-applied-voucher').classList.add('hidden');
            updateServiceTotal();
        }
    })
    .catch(error => {
        messageDiv.classList.remove('hidden');
        messageDiv.className = 'mt-2 text-sm text-red-600';

        // Handle different error types
        if (error.message.includes('Quá nhiều yêu cầu')) {
            messageDiv.textContent = error.message;
        } else {
            messageDiv.textContent = 'Có lỗi xảy ra khi kiểm tra voucher';
        }

        // Không reset input khi có lỗi network
        serviceVoucher = null;
        document.getElementById('service-applied-voucher').classList.add('hidden');
        updateServiceTotal();
    });
}

function showServiceAppliedVoucher(voucherData) {
    const appliedDiv = document.getElementById('service-applied-voucher');
    const infoSpan = document.getElementById('service-voucher-info');

    const discountText = voucherData.voucher.discount_type === 'fixed' ?
        `-${new Intl.NumberFormat('de-DE').format(voucherData.voucher.discount_value)}đ` :
        `-${voucherData.voucher.discount_value}%`;

    infoSpan.textContent = `${voucherData.voucher.name} (${discountText})`;
    appliedDiv.classList.remove('hidden');
}

function removeServiceVoucher() {
    serviceVoucher = null;
    document.getElementById('service_voucher_code').value = '';
    document.getElementById('service-voucher-message').classList.add('hidden');
    document.getElementById('service-applied-voucher').classList.add('hidden');
    toggleServiceVoucherApplyButton(); // Update button state
    updateServiceTotal(); // Sẽ kiểm tra lại số dư với giá gốc
}

function updateServiceTotal() {
    let total = serviceOriginalPrice;

    if (serviceVoucher) {
        total -= serviceVoucher.discount_amount;
    }

    document.getElementById('serviceTotal').textContent = new Intl.NumberFormat('de-DE').format(Math.max(0, total)) + ' đ';

    // Kiểm tra số dư
    checkBalance(total);
}

function checkBalance(totalAmount) {
    const balanceWarning = document.getElementById('balanceWarning');
    const balanceWarningText = document.getElementById('balanceWarningText');
    const confirmButton = document.getElementById('confirmServiceButton');

    if (userBalance < totalAmount) {
        const needAmount = totalAmount - userBalance;
        balanceWarningText.textContent = `Số tiền cần nạp thêm: ${new Intl.NumberFormat('de-DE').format(needAmount)} đ`;
        balanceWarning.classList.remove('hidden');

        // Disable button và đổi màu
        confirmButton.disabled = true;
        confirmButton.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        confirmButton.classList.add('bg-gray-400', 'cursor-not-allowed');
        confirmButton.innerHTML = '<i class="fas fa-shopping-cart mr-2"></i>Mua hàng';
    } else {
        balanceWarning.classList.add('hidden');

        // Enable button và khôi phục màu
        confirmButton.disabled = false;
        confirmButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
        confirmButton.classList.add('bg-blue-600', 'hover:bg-blue-700');
        confirmButton.innerHTML = '<i class="fas fa-shopping-cart mr-2"></i>Xác nhận';
    }
}

// Reset voucher when opening modal
function openPurchaseModal() {
    document.getElementById('purchaseModal').classList.remove('hidden');
    removeServiceVoucher();
    // Hide vouchers list when opening modal
    document.getElementById('availableVouchers').classList.add('hidden');
    // Kiểm tra số dư ngay khi mở modal
    updateServiceTotal();
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/services/show.blade.php ENDPATH**/ ?>