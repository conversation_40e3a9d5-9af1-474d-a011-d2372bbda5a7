<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule voucher cleanup to run daily at 2 AM (no grace period)
Schedule::command('vouchers:cleanup --force --days=0')
    ->dailyAt('02:00')
    ->withoutOverlapping()
    ->runInBackground();
