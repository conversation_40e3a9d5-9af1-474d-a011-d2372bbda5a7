@extends('layouts.admin')

@section('title', '<PERSON><PERSON> mục: ' . $category->name)

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Chi tiết danh mục</h1>
            <p class="text-gray-600">Thông tin chi tiết về danh mục: <strong>{{ $category->name }}</strong></p>
        </div>
        <div class="flex gap-2">
            <a href="{{ route('admin.categories.edit', $category) }}"
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                <i class="fas fa-edit"></i>
                Chỉnh sửa
            </a>
            <a href="{{ route('admin.categories.index') }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                <i class="fas fa-arrow-left"></i>
                Quay lại
            </a>
        </div>
    </div>

    <!-- Category Info -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column - Info -->
            <div class="lg:col-span-2">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Thông tin danh mục</h3>
                <dl class="grid grid-cols-1 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tên danh mục</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $category->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">URL Slug</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">{{ $category->slug }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Nhà phát hành</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $category->publisher ?? 'Chưa có' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Thứ tự sắp xếp</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $category->sort_order ?? 0 }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Trạng thái</dt>
                        <dd class="mt-1">
                            @if($category->status === 'active')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Hoạt động
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-pause-circle mr-1"></i>Tạm dừng
                                </span>
                            @endif
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Ngày tạo</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $category->created_at->format('d/m/Y H:i') }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Cập nhật lần cuối</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $category->updated_at->format('d/m/Y H:i') }}</dd>
                    </div>
                </dl>
            </div>

            <!-- Right Column - Image -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Ảnh danh mục</h3>
                @if($category->image)
                    <div class="relative">
                        <img src="{{ storage_url($category->image) }}" alt="{{ $category->name }}"
                             class="w-full h-48 object-contain rounded-lg border">
                        <div class="absolute top-2 right-2">
                            <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">
                                <i class="fas fa-check mr-1"></i>Có ảnh
                            </span>
                        </div>
                    </div>
                @else
                    <div class="w-full h-48 bg-gray-100 rounded-lg border flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-500">Chưa có ảnh</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Products in Category -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">
                Sản phẩm trong danh mục
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm ml-2">
                    {{ $products->total() }} sản phẩm
                </span>
            </h3>
            <a href="{{ route('admin.products.create') }}?category={{ $category->id }}"
               class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2 transition-colors">
                <i class="fas fa-plus"></i>
                Thêm sản phẩm
            </a>
        </div>

        @if($products->count() > 0)
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sản phẩm</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giá</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày tạo</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($products as $product)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            @if($product->main_image)
                                                <img class="h-10 w-10 rounded-lg object-cover"
                                                     src="{{ storage_url($product->main_image) }}"
                                                     alt="{{ $product->name }}">
                                            @else
                                                <div class="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $product->slug }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ format_money($product->price) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($product->status === 'active')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Hoạt động
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Tạm dừng
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $product->created_at->format('d/m/Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.products.edit', $product) }}"
                                       class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('admin.products.show', $product) }}"
                                       class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $products->links('pagination.custom') }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có sản phẩm</h3>
                <p class="text-gray-500 mb-4">Danh mục này chưa có sản phẩm nào.</p>
                <a href="{{ route('admin.products.create') }}?category={{ $category->id }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2 transition-colors">
                    <i class="fas fa-plus"></i>
                    Thêm sản phẩm đầu tiên
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
