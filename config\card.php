<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Card API Configuration
    |--------------------------------------------------------------------------
    |
    | C<PERSON><PERSON> hình cho tích hợp Card API để nạp thẻ cào
    |
    */

    // API Connection Settings
    'api' => [
        'url' => env('CARD_API_URL', 'https://gachthefast.com/chargingws/v2'),
        'partner_id' => env('CARD_PARTNER_ID', '1936489980'),
        'partner_key' => env('CARD_PARTNER_KEY', 'cd636161a39d2a63a7c1869af09cd31b'),
        'wallet_id' => env('CARD_WALLET_ID', '9536037461'),
        'timeout' => 30,
    ],

    // Supported Telcos and Amounts
    'supported_telcos' => [
        'VIETTEL' => [
            'name' => 'Viettel',
            'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000],
        ],
        'MOBIFONE' => [
            'name' => 'Mobifone',
            'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000],
        ],
        'VINAPHONE' => [
            'name' => 'Vinaphone',
            'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000],
        ],
        'ZING' => [
            'name' => 'Zing',
            'amounts' => [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000],
        ]
    ],

    // Status Messages
    'status_messages' => [
        1 => 'Thành công',
        2 => 'Sai mệnh giá',
        3 => 'Thẻ lỗi',
        4 => 'Hệ thống bảo trì',
        99 => 'Thẻ chờ xử lý',
        100 => 'Gửi thẻ thất bại - Có thể do kiểm tra lại thông tin',
    ],

    // Status Codes
    'status' => [
        'success' => 1,
        'pending' => 99,
    ],
];
