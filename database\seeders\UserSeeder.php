<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin user
        User::create([
            'username' => 'admin',
            'display_name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'balance' => 0,
            'is_active' => true,
        ]);

        // Test user
        User::create([
            'username' => 'testuser',
            'display_name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
            'balance' => 100000,
            'is_active' => true,
        ]);

        // Demo user
        User::create([
            'username' => 'demo',
            'display_name' => 'Demo User',
            'email' => '<EMAIL>',
            'password' => Hash::make('demo123'),
            'role' => 'user',
            'balance' => 50000,
            'is_active' => true,
        ]);
    }
}
