<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Thêm 'service_purchase' vào enum granted_reason
        DB::statement("ALTER TABLE user_vouchers MODIFY COLUMN granted_reason ENUM('manual', 'product_purchase', 'service_purchase') DEFAULT 'manual'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert về enum cũ
        DB::statement("ALTER TABLE user_vouchers MODIFY COLUMN granted_reason ENUM('manual', 'product_purchase') DEFAULT 'manual'");
    }
};
