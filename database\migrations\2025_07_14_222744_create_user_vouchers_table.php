<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_vouchers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('voucher_id')->constrained()->onDelete('cascade');
            $table->timestamp('granted_at');
            $table->timestamp('used_at')->nullable();
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('service_order_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('granted_reason', ['manual', 'product_purchase'])->default('manual');
            $table->foreignId('reference_order_id')->nullable()->constrained('orders')->onDelete('set null');
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            // Index for performance
            $table->index(['user_id', 'voucher_id']);
            $table->index(['voucher_id', 'used_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_vouchers');
    }
};
