<?php $__env->startSection('title', 'Báo cáo Doanh thu'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div>
        <h1 class="text-2xl font-semibold text-gray-900">Báo cáo Doanh thu</h1>
        <p class="text-gray-600 mt-1">Phân tích chi tiết doanh thu theo nguồn và thời gian</p>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="GET" class="flex flex-wrap items-end gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Từ ngày</label>
                <input type="date" name="start_date" value="<?php echo e($startDate->format('Y-m-d')); ?>" 
                       class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Đến ngày</label>
                <input type="date" name="end_date" value="<?php echo e($endDate->format('Y-m-d')); ?>" 
                       class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                <i class="fas fa-search mr-2"></i>Lọc
            </button>
        </form>
    </div>

    <!-- Revenue Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Revenue (Real Revenue) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Doanh thu thực</p>
                    <p class="text-3xl font-bold text-purple-600 mt-2"><?php echo e(format_money($revenueOverview['topup_revenue'])); ?></p>
                    <p class="text-gray-500 text-xs mt-1">Tiền nạp vào hệ thống</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-coins text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- ATM Revenue -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Nạp ATM/Banking</p>
                    <p class="text-3xl font-bold text-blue-600 mt-2"><?php echo e(format_money($topupMethodStats['atm_revenue'])); ?></p>
                    <p class="text-gray-500 text-xs mt-1"><?php echo e($topupMethodStats['atm_percentage']); ?>% tổng nạp tiền</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-university text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Card Revenue -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Nạp thẻ cào</p>
                    <p class="text-3xl font-bold text-orange-600 mt-2"><?php echo e(format_money($topupMethodStats['card_revenue'])); ?></p>
                    <p class="text-gray-500 text-xs mt-1"><?php echo e($topupMethodStats['card_percentage']); ?>% tổng nạp tiền</p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-credit-card text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Business Volume -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm font-medium uppercase tracking-wide">Doanh số kinh doanh</p>
                    <p class="text-3xl font-bold text-green-600 mt-2"><?php echo e(format_money($revenueOverview['business_revenue'])); ?></p>
                    <p class="text-gray-500 text-xs mt-1">Luân chuyển tiền trong hệ thống</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-green-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-medium text-gray-900">Biểu đồ Doanh thu theo Ngày</h3>
            <div class="flex space-x-4 text-sm">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span>Doanh thu thực (Nạp tiền)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span>Doanh số sản phẩm</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                    <span>Doanh số dịch vụ</span>
                </div>
            </div>
        </div>
        <div class="h-96">
            <canvas id="revenueChart"></canvas>
        </div>
    </div>

    <!-- Topup Methods & Business Revenue -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Topup Methods -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Phương thức Nạp tiền</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <i class="fas fa-university text-blue-600 mr-3"></i>
                        <span class="font-medium">ATM/Banking</span>
                    </div>
                    <div class="text-right">
                        <div class="font-semibold"><?php echo e(format_money($topupMethodStats['atm_revenue'])); ?></div>
                        <div class="text-sm text-gray-500"><?php echo e($topupMethodStats['atm_percentage']); ?>%</div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <i class="fas fa-credit-card text-orange-600 mr-3"></i>
                        <span class="font-medium">Thẻ cào</span>
                    </div>
                    <div class="text-right">
                        <div class="font-semibold"><?php echo e(format_money($topupMethodStats['card_revenue'])); ?></div>
                        <div class="text-sm text-gray-500"><?php echo e($topupMethodStats['card_percentage']); ?>%</div>
                    </div>
                </div>
                <div class="border-t pt-4">
                    <div class="flex justify-between items-center font-semibold">
                        <span>Tổng nạp tiền</span>
                        <span><?php echo e(format_money($topupMethodStats['total_topup'])); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Revenue Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Doanh thu Kinh doanh</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <i class="fas fa-box text-green-600 mr-3"></i>
                        <span class="font-medium">Sản phẩm</span>
                    </div>
                    <div class="text-right">
                        <div class="font-semibold"><?php echo e(format_money($revenueOverview['product_revenue'])); ?></div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($revenueOverview['business_revenue'] > 0 ? round(($revenueOverview['product_revenue'] / $revenueOverview['business_revenue']) * 100, 1) : 0); ?>%
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <i class="fas fa-cogs text-orange-600 mr-3"></i>
                        <span class="font-medium">Dịch vụ</span>
                    </div>
                    <div class="text-right">
                        <div class="font-semibold"><?php echo e(format_money($revenueOverview['service_revenue'])); ?></div>
                        <div class="text-sm text-gray-500">
                            <?php echo e($revenueOverview['business_revenue'] > 0 ? round(($revenueOverview['service_revenue'] / $revenueOverview['business_revenue']) * 100, 1) : 0); ?>%
                        </div>
                    </div>
                </div>
                <div class="border-t pt-4">
                    <div class="flex justify-between items-center font-semibold">
                        <span>Tổng kinh doanh</span>
                        <span><?php echo e(format_money($revenueOverview['business_revenue'])); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($chartData['labels'], 15, 512) ?>,
        datasets: [
            {
                label: 'Doanh thu thực (Nạp tiền)',
                data: <?php echo json_encode($chartData['topup_data'], 15, 512) ?>,
                borderColor: 'rgb(147, 51, 234)',
                backgroundColor: 'rgba(147, 51, 234, 0.1)',
                tension: 0.1,
                borderWidth: 3,
                fill: true
            },
            {
                label: 'Doanh số sản phẩm',
                data: <?php echo json_encode($chartData['product_data'], 15, 512) ?>,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1,
                fill: false
            },
            {
                label: 'Doanh số dịch vụ',
                data: <?php echo json_encode($chartData['service_data'], 15, 512) ?>,
                borderColor: 'rgb(249, 115, 22)',
                backgroundColor: 'rgba(249, 115, 22, 0.1)',
                tension: 0.1,
                fill: false
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('de-DE').format(value) + 'đ';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + new Intl.NumberFormat('de-DE').format(context.parsed.y) + 'đ';
                    }
                }
            }
        }
    }
});


</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/revenue-report.blade.php ENDPATH**/ ?>