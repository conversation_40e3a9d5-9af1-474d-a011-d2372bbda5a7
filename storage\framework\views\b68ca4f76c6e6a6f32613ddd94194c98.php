<?php if($paginator->hasPages()): ?>
    <div class="flex flex-col items-center space-y-3">
        
        <nav role="navigation" aria-label="Pagination Navigation" class="flex items-center space-x-1">
            
            <?php if($paginator->onFirstPage()): ?>
                <span class="flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-100 rounded cursor-not-allowed">
                    <i class="fas fa-chevron-left text-xs"></i>
                </span>
            <?php else: ?>
                <a href="<?php echo e($paginator->previousPageUrl()); ?>"
                   class="flex items-center justify-center w-8 h-8 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors">
                    <i class="fas fa-chevron-left text-xs"></i>
                </a>
            <?php endif; ?>

            
            <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                
                <?php if(is_string($element)): ?>
                    <span class="flex items-center justify-center w-8 h-8 text-gray-500"><?php echo e($element); ?></span>
                <?php endif; ?>

                
                <?php if(is_array($element)): ?>
                    <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($page == $paginator->currentPage()): ?>
                            <span class="flex items-center justify-center w-8 h-8 text-white bg-blue-600 rounded font-medium">
                                <?php echo e($page); ?>

                            </span>
                        <?php else: ?>
                            <a href="<?php echo e($url); ?>"
                               class="flex items-center justify-center w-8 h-8 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors">
                                <?php echo e($page); ?>

                            </a>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            
            <?php if($paginator->hasMorePages()): ?>
                <a href="<?php echo e($paginator->nextPageUrl()); ?>"
                   class="flex items-center justify-center w-8 h-8 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors">
                    <i class="fas fa-chevron-right text-xs"></i>
                </a>
            <?php else: ?>
                <span class="flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-100 rounded cursor-not-allowed">
                    <i class="fas fa-chevron-right text-xs"></i>
                </span>
            <?php endif; ?>
        </nav>

        
        <div class="text-sm text-gray-600">
            Hiển thị <span class="font-medium text-gray-900"><?php echo e($paginator->firstItem()); ?></span> -
            <span class="font-medium text-gray-900"><?php echo e($paginator->lastItem()); ?></span>
            trong tổng số <span class="font-medium text-blue-600"><?php echo e($paginator->total()); ?></span> kết quả
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/pagination/custom.blade.php ENDPATH**/ ?>