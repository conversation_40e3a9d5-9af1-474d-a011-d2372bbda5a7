@if ($paginator->hasPages())
    <div class="flex flex-col items-center space-y-3">
        {{-- Navigation --}}
        <nav role="navigation" aria-label="Pagination Navigation" class="flex items-center space-x-1">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <span class="flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-100 rounded cursor-not-allowed">
                    <i class="fas fa-chevron-left text-xs"></i>
                </span>
            @else
                <a href="{{ $paginator->previousPageUrl() }}"
                   class="flex items-center justify-center w-8 h-8 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors">
                    <i class="fas fa-chevron-left text-xs"></i>
                </a>
            @endif

            {{-- Pagination Elements --}}
            @foreach ($elements as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <span class="flex items-center justify-center w-8 h-8 text-gray-500">{{ $element }}</span>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <span class="flex items-center justify-center w-8 h-8 text-white bg-blue-600 rounded font-medium">
                                {{ $page }}
                            </span>
                        @else
                            <a href="{{ $url }}"
                               class="flex items-center justify-center w-8 h-8 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors">
                                {{ $page }}
                            </a>
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <a href="{{ $paginator->nextPageUrl() }}"
                   class="flex items-center justify-center w-8 h-8 text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors">
                    <i class="fas fa-chevron-right text-xs"></i>
                </a>
            @else
                <span class="flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-100 rounded cursor-not-allowed">
                    <i class="fas fa-chevron-right text-xs"></i>
                </span>
            @endif
        </nav>

        {{-- Pagination Info --}}
        <div class="text-sm text-gray-600">
            Hiển thị <span class="font-medium text-gray-900">{{ $paginator->firstItem() }}</span> -
            <span class="font-medium text-gray-900">{{ $paginator->lastItem() }}</span>
            trong tổng số <span class="font-medium text-blue-600">{{ $paginator->total() }}</span> kết quả
        </div>
    </div>
@endif
