<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> tiền ATM - AccReroll'); ?>

<?php $__env->startSection('head'); ?>
<style>
.copy-btn {
    position: relative;
    padding: 4px;
    border-radius: 4px;
}

.copy-btn:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.copy-btn:active {
    transform: scale(0.95);
}

/* Tooltip */
.copy-btn::before {
    content: 'Copy';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #374151;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    z-index: 10;
}

.copy-btn::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(2px);
    border: 4px solid transparent;
    border-top-color: #374151;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
}

.copy-btn:hover::before,
.copy-btn:hover::after {
    opacity: 1;
    visibility: visible;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="<?php echo e(route('topup.index')); ?>" class="mr-4 text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Nạp tiền qua ATM</h1>
                <p class="text-gray-600 mt-2">Chuyển khoản ngân hàng qua QR code</p>
            </div>
        </div>
    </div>

    <!-- Main Content - Single Column -->
    <div class="max-w-lg mx-auto">
        <!-- QR Code -->
        <div>
            <div class="bg-white rounded-lg shadow-md p-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-8 text-center">Mã QR thanh toán</h2>

                <!-- QR Code -->
                <div class="text-center mb-8">
                    <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 inline-block qr-container">
                        <img src="<?php echo e($qrUrl); ?>"
                             alt="QR Code"
                             class="w-64 h-64 mx-auto">
                    </div>
                </div>

                <!-- Payment Info - Same width as QR container -->
                <div class="w-80 mx-auto space-y-4">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Ngân hàng:</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium"><?php echo e(config('sepay.bank_info.bank_name', 'MB Bank')); ?></span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Số tài khoản:</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium"><?php echo e(config('sepay.bank_info.account_number', '*********')); ?></span>
                                    <button onclick="copyToClipboard('<?php echo e(config('sepay.bank_info.account_number', '*********')); ?>', this)"
                                            class="copy-btn text-gray-400 hover:text-blue-600 transition-colors">
                                        <i class="fas fa-copy text-xs"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Chủ tài khoản:</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium"><?php echo e(config('sepay.bank_info.account_name', 'NGUYEN VAN A')); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transfer Content - Important -->
                    <div class="bg-yellow-50 border-2 border-yellow-300 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-700 font-medium">Nội dung:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-bold text-lg text-yellow-800"><?php echo e($user->topup_content); ?></span>
                                <button onclick="copyToClipboard('<?php echo e($user->topup_content); ?>', this)"
                                        class="copy-btn text-yellow-600 hover:text-yellow-800 transition-colors">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Important Notes -->
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="flex items-start text-sm">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-0.5"></i>
                        <span class="text-gray-700"><strong>Lưu ý:</strong> Vui lòng nhập chính xác nội dung chuyển khoản trên để giao dịch được xử lý tự động và nhanh chóng. Quá 5p chưa được cộng tiền thì hãy liên hệ FB để được hỗ trợ nhé.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Copy functions
function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        showCopySuccess(button);
    }).catch(function(err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopySuccess(button);
    });
}

function showCopySuccess(button) {
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check text-xs"></i>';
    button.classList.remove('text-gray-400', 'hover:text-blue-600');
    button.classList.add('text-green-600');

    setTimeout(() => {
        button.innerHTML = originalIcon;
        button.classList.remove('text-green-600');
        button.classList.add('text-gray-400', 'hover:text-blue-600');
    }, 1500);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/topup/atm.blade.php ENDPATH**/ ?>