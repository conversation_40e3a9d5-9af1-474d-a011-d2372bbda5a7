<?php $__env->startSection('title', 'Đơn hàng #' . $order->order_number); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.orders.index')); ?>" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900">Đơn hàng <?php echo e($order->order_number); ?></h1>
        </div>
    </div>

    <!-- Order Summary -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Order Info -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin đơn hàng</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">ID đơn hàng:</span>
                        <span class="text-sm font-medium text-gray-900">#<?php echo e($order->id); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Mã đơn hàng:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($order->order_number); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Ngày đặt:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($order->created_at->format('d/m/Y H:i')); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Trạng thái:</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check mr-1"></i>Hoàn thành
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Voucher sử dụng:</span>
                        <?php if($order->usedVoucher && $order->usedVoucher->voucher): ?>
                            <span class="text-sm text-green-600">
                                <i class="fas fa-ticket-alt mr-1"></i><?php echo e($order->usedVoucher->voucher->code); ?>

                                <span class="text-xs text-gray-500">
                                    <?php if($order->usedVoucher->voucher->discount_type === 'fixed'): ?>
                                        (Giảm <?php echo e(format_money($order->usedVoucher->voucher->discount_value, false)); ?>đ)
                                    <?php else: ?>
                                        (Giảm <?php echo e($order->usedVoucher->voucher->discount_value); ?>%)
                                    <?php endif; ?>
                                </span>
                            </span>
                        <?php else: ?>
                            <span class="text-sm text-gray-400">Không sử dụng</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex justify-between pt-2 border-t border-gray-200">
                        <span class="text-base font-medium text-gray-900">Tổng tiền:</span>
                        <span class="text-lg font-bold text-red-600"><?php echo e(format_money($order->total_amount)); ?></span>
                    </div>
                </div>
            </div>

            <!-- Customer Info -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin khách hàng</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Username:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($order->user->username); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Email:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($order->user->email); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Số dư hiện tại:</span>
                        <span class="text-sm font-medium text-green-600"><?php echo e(format_money($order->user->balance)); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Sản phẩm đã mua</h3>

        <div class="space-y-4">
            <?php
                $groupedItems = $order->orderItems->groupBy('product_id');
            ?>

            <?php $__currentLoopData = $groupedItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productId => $items): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $firstItem = $items->first();
                $totalQuantity = $items->sum('quantity');
                $totalPrice = $items->sum(function($item) { return $item->price * $item->quantity; });
            ?>
            <div class="border border-gray-200 rounded-lg p-4">
                <!-- Product Info -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                            <?php if($firstItem->product->main_image): ?>
                                <img src="<?php echo e(storage_url($firstItem->product->main_image)); ?>"
                                     alt="<?php echo e($firstItem->product->name); ?>"
                                     class="w-full h-full object-cover rounded-lg">
                            <?php else: ?>
                                <i class="fas fa-gamepad text-gray-400 text-xl"></i>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900"><?php echo e($firstItem->product->name); ?></h4>
                            <p class="text-sm text-gray-500"><?php echo e($firstItem->product->category->name); ?></p>
                            <p class="text-sm text-gray-500">Số lượng: <?php echo e($totalQuantity); ?> tài khoản</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-red-600"><?php echo e(format_money($totalPrice)); ?></div>
                        <div class="text-sm text-gray-500"><?php echo e(format_money($firstItem->price)); ?> x<?php echo e($totalQuantity); ?></div>
                    </div>
                </div>

                <!-- Account Details -->
                <?php if($totalQuantity > 0): ?>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between mb-3">
                        <h5 class="font-medium text-gray-900">Tài khoản game đã giao</h5>
                        <span class="text-sm text-gray-500"><?php echo e($totalQuantity); ?> tài khoản</span>
                    </div>

                    <div class="space-y-3">
                        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($item->productAccount): ?>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div class="mb-3">
                                <span class="text-sm font-medium text-gray-700">
                                    Tài khoản #<?php echo e($index + 1); ?>

                                </span>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div class="bg-white p-3 rounded border">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-xs text-gray-500">Username</div>
                                        <button onclick="copyText('<?php echo e($item->productAccount->username); ?>', 'username-<?php echo e($index + 1); ?>')"
                                                class="text-blue-600 hover:text-blue-800 text-xs" id="username-<?php echo e($index + 1); ?>">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <div class="text-sm font-mono text-gray-900 select-all"><?php echo e($item->productAccount->username); ?></div>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <div class="flex items-center justify-between mb-1">
                                        <div class="text-xs text-gray-500">Password</div>
                                        <button onclick="copyText('<?php echo e($item->productAccount->password); ?>', 'password-<?php echo e($index + 1); ?>')"
                                                class="text-blue-600 hover:text-blue-800 text-xs" id="password-<?php echo e($index + 1); ?>">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    <div class="text-sm font-mono text-gray-900 select-all"><?php echo e($item->productAccount->password); ?></div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>


function copyText(text, buttonId) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('text-blue-600');
        button.classList.add('text-green-600');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-green-600');
            button.classList.add('text-blue-600');
        }, 1500);
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show success message
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('text-blue-600');
        button.classList.add('text-green-600');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-green-600');
            button.classList.add('text-blue-600');
        }, 1500);
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/orders/show.blade.php ENDPATH**/ ?>