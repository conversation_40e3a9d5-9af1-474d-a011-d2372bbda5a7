<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voucher_applicable_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voucher_id')->constrained()->onDelete('cascade');
            $table->string('applicable_type'); // 'product' hoặc 'service'
            $table->unsignedBigInteger('applicable_id'); // ID của product hoặc service
            $table->timestamps();

            // Composite index để tránh duplicate
            $table->unique(['voucher_id', 'applicable_type', 'applicable_id'], 'voucher_applicable_unique');
            
            // Index để query nhanh
            $table->index(['applicable_type', 'applicable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voucher_applicable_items');
    }
};
