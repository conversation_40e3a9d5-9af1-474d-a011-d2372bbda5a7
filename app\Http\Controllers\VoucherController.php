<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\VoucherService;

class VoucherController extends Controller
{
    protected $voucherService;

    public function __construct(VoucherService $voucherService)
    {
        $this->voucherService = $voucherService;
    }



    /**
     * Validate voucher via AJAX
     */
    public function validate(Request $request)
    {
        $request->validate([
            'voucher_code' => 'required|string',
            'order_type' => 'required|in:product,service',
            'category_id' => 'nullable|integer',
            'total_amount' => 'required|numeric|min:0',
            'item_ids' => 'nullable|array',
            'item_ids.*' => 'integer'
        ]);

        $user = Auth::user();

        $validation = $this->voucherService->validateVoucherForOrder(
            $request->voucher_code,
            $user,
            $request->order_type,
            $request->category_id,
            $request->total_amount,
            $request->item_ids ?? []
        );
        return response()->json($validation);
    }

    /**
     * L<PERSON>y danh sách voucher available cho checkout
     */
    public function getAvailable(Request $request)
    {
        $request->validate([
            'order_type' => 'required|in:product,service',
            'category_id' => 'nullable|integer'
        ]);

        $user = Auth::user();
        $vouchers = $this->voucherService->getAvailableVouchersForUserCollection(
            $user,
            $request->order_type,
            $request->category_id
        );

        // Đảm bảo voucher relationship đã được eager load
        // Nếu chưa có thì load thêm để tránh N+1
        if ($vouchers->isNotEmpty() && !$vouchers->first()->relationLoaded('voucher')) {
            $vouchers->load('voucher');
        }

        return response()->json([
            'vouchers' => $vouchers->map(function ($userVoucher) {
                $voucher = $userVoucher->voucher; // Đã được eager load
                return [
                    'code' => $voucher->code,
                    'name' => $voucher->name,
                    'description' => $voucher->description,
                    'discount_value' => $voucher->discount_value,
                    'discount_type' => $voucher->discount_type,
                    'min_order_amount' => $voucher->min_order_amount,
                    'expires_at' => $voucher->expires_at?->format('d/m/Y H:i'),
                ];
            })
        ]);
    }
}
