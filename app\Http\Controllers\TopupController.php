<?php

namespace App\Http\Controllers;

use App\Services\CardApiService;
use App\Services\TopupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TopupController extends Controller
{
    protected $topupService;

    public function __construct(TopupService $topupService)
    {
        $this->topupService = $topupService;
    }

    /**
     * Hiển thị trang chọn phương thức nạp tiền
     */
    public function index()
    {
        $user = Auth::user();

        return view('topup.index', compact('user'));
    }

    /**
     * Hiển thị trang nạp tiền ATM với QR SePay
     */
    public function atm(Request $request)
    {
        $user = Auth::user();

        // Tạo QR code URL với nội dung nạp riêng cho user
        $qrUrl = $this->topupService->generateSePayQR($user->id);

        return view('topup.atm', compact('user', 'qrUrl'));
    }

    /**
     * Hiển thị trang nạp thẻ
     */
    public function card()
    {
        $user = Auth::user();
        $telcos = CardApiService::getSupportedTelcos();

        return view('topup.card', compact('user', 'telcos'));
    }

    /**
     * Xử lý nạp thẻ
     */
    public function processCard(Request $request)
    {
        // Validation cơ bản
        $request->validate([
            'telco' => 'required|string|in:VIETTEL,MOBIFONE,VINAPHONE,ZING',
            'amount' => 'required|integer|in:10000,20000,30000,50000,100000,200000,300000,500000',
            'serial' => 'required|string|min:8|max:20',
            'code' => 'required|string|min:8|max:20',
        ], [
            'telco.required' => 'Vui lòng chọn loại thẻ',
            'amount.required' => 'Vui lòng chọn mệnh giá',
            'serial.required' => 'Vui lòng nhập số serial',
            'code.required' => 'Vui lòng nhập mã thẻ',
            'serial.min' => 'Số serial phải có ít nhất 8 ký tự',
            'code.min' => 'Mã thẻ phải có ít nhất 8 ký tự',
            'serial.max' => 'Số serial không được quá 20 ký tự',
            'code.max' => 'Mã thẻ không được quá 20 ký tự',
        ]);

        // Validation riêng cho từng loại thẻ
        $telco = $request->telco;
        $code = $request->code;
        $serial = $request->serial;

        if ($telco === 'ZING') {
            // Thẻ Zing có format khác
            if (strlen($code) < 8 || strlen($code) > 15) {
                return redirect()->back()->withInput()->withErrors(['code' => 'Mã thẻ Zing phải từ 8-15 ký tự']);
            }
            if (strlen($serial) < 8 || strlen($serial) > 15) {
                return redirect()->back()->withInput()->withErrors(['serial' => 'Serial thẻ Zing phải từ 8-15 ký tự']);
            }
        } else {
            // Thẻ điện thoại (Viettel, Mobifone, Vinaphone)
            if (strlen($code) < 10 || strlen($code) > 20) {
                return redirect()->back()->withInput()->withErrors(['code' => 'Mã thẻ điện thoại phải từ 10-20 ký tự']);
            }
            if (strlen($serial) < 10 || strlen($serial) > 20) {
                return redirect()->back()->withInput()->withErrors(['serial' => 'Serial thẻ điện thoại phải từ 10-20 ký tự']);
            }
        }

        $user = Auth::user();

        $result = $this->topupService->processCardTopup($user, $request->only([
            'telco', 'amount', 'serial', 'code'
        ]));

        if ($result['success']) {
            return redirect()->back()->with('success', "Gửi thẻ thành công! Vào trang lịch sử nạp tiền để kiểm tra.");
        } else {
            return redirect()->back()->withInput()->withErrors(['card' => "Có lỗi xảy ra. Vui lòng thử lại."]);
        }
    }

    /**
     * Lịch sử nạp tiền
     */
    public function history()
    {
        $user = Auth::user();

        $transactions = $this->topupService->getUserTopupHistory($user->id);

        return view('topup.history', compact('user', 'transactions'));
    }
}
