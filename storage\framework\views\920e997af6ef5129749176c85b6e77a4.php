<?php if($latestProducts->count() > 0): ?>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <?php $__currentLoopData = $latestProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white border border-gray-300 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-blue-600 flex flex-col h-full relative">
                <!-- Product Image -->
                <div class="relative">
                    <?php if($product->main_image): ?>
                        <img src="<?php echo e(storage_url($product->main_image)); ?>"
                             alt="<?php echo e($product->name); ?>"
                             class="w-full h-40 object-contain">
                    <?php else: ?>
                        <div class="w-full h-40 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <i class="fas fa-gamepad text-white text-3xl"></i>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Product Info -->
                <div class="p-4 flex flex-col flex-1">
                    <h3 class="font-semibold text-gray-800 text-base mb-2 line-clamp-2"><?php echo e($product->name); ?></h3>
                    <p class="text-sm text-gray-500 mb-3"><?php echo e($product->category->name); ?></p>

                    <!-- Bottom section - always at bottom -->
                    <div class="mt-auto">
                        <?php if($product->available_quantity > 0): ?>
                            <div class="text-green-600 font-semibold text-sm mb-2">
                                Sẵn Có: <?php echo e($product->available_quantity); ?>

                            </div>
                        <?php elseif($product->allow_preorder): ?>
                            <div class="font-semibold text-sm mb-2" style="color: #0ea5e9;">
                                Còn hàng (nhận mã đơn)
                            </div>
                        <?php else: ?>
                            <div class="text-red-600 font-semibold text-sm mb-2">
                                Hết hàng
                            </div>
                        <?php endif; ?>
                        <div class="text-orange-600 font-bold text-xl"><?php echo e(number_format($product->price, 0, ',', '.')); ?> đ</div>
                    </div>
                </div>

                <!-- Clickable overlay -->
                <a href="<?php echo e(route('product.show', $product->slug)); ?>" class="absolute inset-0 z-10"></a>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Pagination -->
    <?php if($latestProducts->hasPages()): ?>
        <div class="mt-8">
            <?php echo e($latestProducts->appends(request()->query())->links('pagination.custom')); ?>

        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
            <i class="fas fa-box-open"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-600 mb-2">Chưa có sản phẩm nào</h3>
        <p class="text-gray-500">Hãy quay lại sau để xem các sản phẩm mới nhất!</p>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\rainshop\resources\views/partials/products-grid.blade.php ENDPATH**/ ?>