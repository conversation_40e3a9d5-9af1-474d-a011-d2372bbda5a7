<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Password;
use App\Rules\UniqueDisplayName;

class ProfileController extends Controller
{
    /**
     * Hiển thị trang thông tin cá nhân
     */
    public function index()
    {
        $user = Auth::user();
        return view('profile.index', compact('user'));
    }



    /**
     * Cập nhật tên hiển thị
     */
    public function updateDisplayName(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'display_name' => ['nullable', 'string', 'min:3', 'max:20', 'regex:/^[a-zA-Z0-9\s\p{L}]+$/u', new UniqueDisplayName($user->id)],
        ], [
            'display_name.min' => 'Tên hiển thị phải có ít nhất 3 ký tự.',
            'display_name.max' => 'Tên hiển thị không được vượt quá 20 ký tự.',
            'display_name.regex' => 'Tên hiển thị chỉ được chứa chữ cái, số và khoảng trắng.',
        ]);

        $user->update([
            'display_name' => $request->display_name,
        ]);

        return redirect()->route('profile.index')->with('success', 'Tên hiển thị đã được cập nhật thành công!');
    }

    /**
     * Cập nhật email
     */
    public function updateEmail(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
        ]);

        $user->update([
            'email' => $request->email,
        ]);

        return redirect()->route('profile.index')->with('success', 'Email đã được cập nhật thành công!');
    }

    /**
     * Đổi mật khẩu
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => ['required'],
            'password' => ['required', 'confirmed', 'min:8', 'max:30'],
        ], [
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự.',
            'password.max' => 'Mật khẩu không được vượt quá 30 ký tự.',
        ]);

        $user = Auth::user();

        // Kiểm tra mật khẩu hiện tại
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->route('profile.index')->with('error', 'Mật khẩu hiện tại không đúng!');
        }

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('profile.index')->with('success', 'Mật khẩu đã được thay đổi thành công!');
    }


}
