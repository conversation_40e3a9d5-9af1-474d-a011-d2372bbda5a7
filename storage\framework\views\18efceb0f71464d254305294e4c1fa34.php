

<?php $__env->startSection('title', 'Thêm sản phẩm'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Thêm sản phẩm</h1>
            <p class="text-gray-600 mt-1">Tạo sản phẩm mới và thêm tài khoản game</p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo e(route('admin.products.index')); ?>" class="inline-flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <form method="POST" action="<?php echo e(route('admin.products.store')); ?>" enctype="multipart/form-data" class="space-y-6">
            <?php echo csrf_field(); ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Product Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Tên sản phẩm <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="<?php echo e(old('name')); ?>"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="VD: Tài khoản Liên Quân Mobile VIP"
                    >
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Category -->
                <div class="relative">
                    <label for="category_search" class="block text-sm font-medium text-gray-700 mb-2">
                        Danh mục <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="category_search"
                        placeholder="Tìm kiếm danh mục..."
                        autocomplete="off"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        value="<?php echo e(old('category_id') ? $categories->find(old('category_id'))->name ?? '' : ''); ?>"
                    >
                    <input
                        type="hidden"
                        id="category_id"
                        name="category_id"
                        value="<?php echo e(old('category_id')); ?>"
                        required
                    >

                    <!-- Dropdown results -->
                    <div id="category_dropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto hidden">
                        <!-- Results will be populated here -->
                    </div>

                    <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Price -->
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                        Giá bán (VNĐ) <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="number"
                        id="price"
                        name="price"
                        value="<?php echo e(old('price')); ?>"
                        required
                        min="0"
                        step="1000"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="50000"
                    >
                    <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Trạng thái <span class="text-red-500">*</span>
                    </label>
                    <select
                        id="status"
                        name="status"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                    >
                        <option value="">Chọn trạng thái</option>
                        <option value="active" <?php echo e(old('status') === 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                        <option value="inactive" <?php echo e(old('status') === 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                    </select>
                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả sản phẩm
                </label>
                <textarea
                    id="description"
                    name="description"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                    placeholder="Mô tả chi tiết về sản phẩm..."
                ><?php echo e(old('description')); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Notes -->
            <div>
                <label for="note" class="block text-sm font-medium text-gray-700 mb-2">
                    Lưu ý
                </label>
                <textarea
                    id="note"
                    name="note"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                    placeholder="Nhập lưu ý về sản phẩm (tùy chọn)..."
                ><?php echo e(old('note')); ?></textarea>
                <?php $__errorArgs = ['note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                    Hình ảnh sản phẩm
                </label>

                <!-- Upload Zone -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors" id="upload-zone">
                    <div class="space-y-2">
                        <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                        <div>
                            <p class="text-sm text-gray-600">
                                <span class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer" id="file-trigger" onclick="triggerFileInput();">
                                    Nhấp để chọn ảnh
                                </span>
                                hoặc kéo thả ảnh vào đây
                            </p>
                            <p class="text-xs text-gray-500 mt-1">PNG, JPG, GIF tối đa 2MB mỗi file. Tối đa 5 ảnh.</p>
                        </div>
                    </div>
                    <input
                        type="file"
                        id="images"
                        name="images[]"
                        accept="image/*"
                        multiple
                        class="hidden"
                    >
                </div>

                <?php $__errorArgs = ['images.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-2 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                <!-- Action Buttons -->
                <div class="flex gap-2 mt-3">
                    <button type="button" id="add-more-images" onclick="triggerFileInput();" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Thêm ảnh
                    </button>
                    <button type="button" id="clear-images" class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 transition-colors hidden">
                        <i class="fas fa-trash mr-2"></i>Xóa tất cả
                    </button>
                </div>

                <!-- Hidden inputs for additional files -->
                <div id="additional-inputs"></div>

                <!-- Image Preview -->
                <div id="image-preview" class="mt-6 hidden">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-sm font-medium text-gray-900">Ảnh đã chọn (<span id="image-count">0</span>/5)</h4>
                        <p class="text-xs text-gray-500">Ảnh đầu tiên sẽ là ảnh chính</p>
                    </div>
                    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-4" id="preview-container">
                        <!-- Preview images will be inserted here -->
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="<?php echo e(route('admin.products.index')); ?>"
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Hủy
                </a>
                <button type="submit"
                        class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Tạo sản phẩm
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Global function to trigger file input
function triggerFileInput() {
    const input = document.getElementById('images');
    if (input) {
        input.click();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Category search autocomplete
    setupCategoryAutocomplete();
    // Image management with accumulative file selection
    let allSelectedFiles = [];
    const imageInput = document.getElementById('images');
    const imagePreview = document.getElementById('image-preview');
    const previewContainer = document.getElementById('preview-container');
    const imageCount = document.getElementById('image-count');
    const addMoreBtn = document.getElementById('add-more-images');
    const clearBtn = document.getElementById('clear-images');
    const additionalInputsContainer = document.getElementById('additional-inputs');
    const uploadZone = document.getElementById('upload-zone');
    const fileTrigger = document.getElementById('file-trigger');

    // File input change handler
    imageInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFiles(Array.from(e.target.files));
        }
    });

    // Clear button handler
    clearBtn.addEventListener('click', function() {
        allSelectedFiles = [];
        additionalInputsContainer.innerHTML = '';
        updatePreview();
    });

    // Drag and drop handlers
    uploadZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadZone.classList.add('border-blue-400', 'bg-blue-50');
    });

    uploadZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('border-blue-400', 'bg-blue-50');
    });

    uploadZone.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadZone.classList.remove('border-blue-400', 'bg-blue-50');

        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        handleFiles(files);
    });

    function handleFiles(newFiles) {
        // Add new files to the collection (up to 5 total)
        newFiles.forEach(file => {
            if (allSelectedFiles.length < 5 && file.type.startsWith('image/')) {
                allSelectedFiles.push(file);
            }
        });

        updatePreview();
        createHiddenInputs();

        // Clear the input value after processing
        setTimeout(() => {
            imageInput.value = '';
        }, 100);
    }



    // Clear all images
    clearBtn.addEventListener('click', function() {
        if (confirm('Bạn có chắc chắn muốn xóa tất cả ảnh đã chọn?')) {
            allSelectedFiles = [];
            imageInput.value = '';
            additionalInputsContainer.innerHTML = '';
            updatePreview();
        }
    });

    function createHiddenInputs() {
        // Clear existing hidden inputs
        additionalInputsContainer.innerHTML = '';

        // Create hidden inputs for all files
        allSelectedFiles.forEach((file, index) => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'file';
            hiddenInput.name = `images[${index}]`;
            hiddenInput.style.display = 'none';

            // Create a new FileList with just this file
            const dt = new DataTransfer();
            dt.items.add(file);
            hiddenInput.files = dt.files;

            additionalInputsContainer.appendChild(hiddenInput);
        });
    }

    function updatePreview() {
        if (allSelectedFiles.length === 0) {
            imagePreview.classList.add('hidden');
            clearBtn.classList.add('hidden');
            return;
        }

        imagePreview.classList.remove('hidden');
        clearBtn.classList.remove('hidden');
        previewContainer.innerHTML = '';
        imageCount.textContent = allSelectedFiles.length;

        allSelectedFiles.forEach((file, index) => {
            const div = document.createElement('div');
            div.className = 'relative bg-gray-50 rounded-lg border border-gray-200 overflow-hidden';

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'w-full h-32 object-contain';

                const removeBtn = document.createElement('button');
                removeBtn.type = 'button';
                removeBtn.className = 'absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs';
                removeBtn.innerHTML = '×';
                removeBtn.onclick = function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    allSelectedFiles.splice(index, 1);
                    updatePreview();
                    createHiddenInputs();
                };

                const badge = document.createElement('div');
                badge.className = 'absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded font-medium';
                badge.textContent = index === 0 ? 'Chính' : index + 1;

                div.innerHTML = '';
                div.appendChild(img);
                div.appendChild(removeBtn);
                div.appendChild(badge);
            };

            reader.onerror = function() {
                div.innerHTML = '<div class="w-full h-32 bg-red-100 flex items-center justify-center text-red-500 text-sm">Lỗi tải ảnh</div>';
            };

            div.innerHTML = '<div class="w-full h-32 bg-gray-100 flex items-center justify-center text-gray-500 text-sm">Đang tải...</div>';
            previewContainer.appendChild(div);
            reader.readAsDataURL(file);
        });
    }



    // Category search autocomplete
    function setupCategoryAutocomplete() {
        const searchInput = document.getElementById('category_search');
        const hiddenInput = document.getElementById('category_id');
        const dropdown = document.getElementById('category_dropdown');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            clearTimeout(searchTimeout);

            if (query.length < 2) {
                hideDropdown();
                hiddenInput.value = '';
                return;
            }

            searchTimeout = setTimeout(() => {
                searchCategories(query);
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length >= 2) {
                searchCategories(this.value.trim());
            }
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                hideDropdown();
            }
        });

        function searchCategories(query) {
            fetch(`<?php echo e(route('admin.products.search-categories')); ?>?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(categories => {
                    displayResults(categories);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    hideDropdown();
                });
        }

        function displayResults(categories) {
            if (categories.length === 0) {
                dropdown.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm">Không tìm thấy danh mục nào</div>';
                dropdown.classList.remove('hidden');
                return;
            }

            dropdown.innerHTML = categories.map(category => `
                <div class="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                     onclick="selectCategory(${category.id}, '${category.display}')">
                    <div class="font-medium text-gray-900">${category.name}</div>
                    <div class="text-sm text-gray-500">${category.publisher}</div>
                </div>
            `).join('');

            dropdown.classList.remove('hidden');
        }

        function hideDropdown() {
            dropdown.classList.add('hidden');
        }

        // Make selectCategory global
        window.selectCategory = function(id, display) {
            hiddenInput.value = id;
            searchInput.value = display;
            hideDropdown();
        };
    }

});

</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/products/create.blade.php ENDPATH**/ ?>