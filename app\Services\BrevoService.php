<?php

namespace App\Services;

use Brevo\Client\Api\TransactionalEmailsApi;
use Brevo\Client\Configuration;
use Brevo\Client\Model\SendSmtpEmail;
use Brevo\Client\Model\SendSmtpEmailTo;
use Brevo\Client\Model\SendSmtpEmailSender;
use GuzzleHttp\Client;
use Exception;

class BrevoService
{
    private $apiInstance;
    private $config;

    public function __construct()
    {
        $this->config = Configuration::getDefaultConfiguration()->setApiKey('api-key', config('services.brevo.api_key'));
        $this->apiInstance = new TransactionalEmailsApi(
            new Client(),
            $this->config
        );
    }

    /**
     * Gửi email reset password qua Brevo API
     */
    public function sendPasswordResetEmail($email, $resetUrl, $userName = null)
    {
        try {
            $sendSmtpEmail = new SendSmtpEmail();
            
            // Người gửi
            $sender = new SendSmtpEmailSender();
            $sender->setName(config('mail.from.name'));
            $sender->setEmail(config('mail.from.address'));
            $sendSmtpEmail->setSender($sender);

            // Người nhận
            $to = new SendSmtpEmailTo();
            $to->setEmail($email);
            if ($userName) {
                $to->setName($userName);
            }
            $sendSmtpEmail->setTo([$to]);

            // Nội dung email
            $sendSmtpEmail->setSubject('Đặt lại mật khẩu - ' . config('app.name'));
            $sendSmtpEmail->setHtmlContent($this->getPasswordResetEmailTemplate($resetUrl, $userName));
            $sendSmtpEmail->setTextContent("Nhấn vào link này để đặt lại mật khẩu: {$resetUrl}");

            // Gửi email
            $result = $this->apiInstance->sendTransacEmail($sendSmtpEmail);
            
            return [
                'success' => true,
                'message_id' => $result->getMessageId()
            ];

        } catch (Exception $e) {
            \Log::error('Brevo API Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Template HTML cho email reset password
     */
    private function getPasswordResetEmailTemplate($resetUrl, $userName = null)
    {
        $appName = config('app.name');
        $greeting = $userName ? "Xin chào {$userName}," : "Xin chào,";
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1'>
            <title>Đặt lại mật khẩu</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
                .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>{$appName}</h1>
                    <p>Yêu cầu đặt lại mật khẩu</p>
                </div>
                <div class='content'>
                    <p>{$greeting}</p>
                    <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.</p>
                    <p>Nhấn vào nút bên dưới để đặt lại mật khẩu:</p>
                    <p style='text-align: center;'>
                        <a href='{$resetUrl}' class='button'>Đặt lại mật khẩu</a>
                    </p>
                    <p>Hoặc copy và paste link sau vào trình duyệt:</p>
                    <p style='word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px;'>{$resetUrl}</p>
                    <p><strong>Lưu ý:</strong> Link này sẽ hết hạn sau 10 phút.</p>
                    <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
                </div>
                <div class='footer'>
                    <p>© " . date('Y') . " {$appName}.</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
