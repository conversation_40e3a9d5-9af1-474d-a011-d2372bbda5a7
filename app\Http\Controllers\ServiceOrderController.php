<?php

namespace App\Http\Controllers;

use App\Models\ServiceOrder;
use Illuminate\Support\Facades\Auth;

class ServiceOrderController extends Controller
{
    /**
     * Display user's service orders
     */
    public function index()
    {
        $orders = ServiceOrder::with(['service', 'usedVoucher.voucher'])
            ->forUser(Auth::id())
            ->latest()
            ->paginate(10);

        return view('service-orders.index', compact('orders'));
    }

    /**
     * Show specific service order
     */
    public function show(ServiceOrder $serviceOrder)
    {
        // Check if order belongs to current user
        if ($serviceOrder->user_id != Auth::id()) {
            abort(404);
        }

        $serviceOrder->load(['service', 'usedVoucher.voucher']);

        return view('service-orders.show', compact('serviceOrder'));
    }
}
