<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'admin.access' => \App\Http\Middleware\AdminAccess::class,
            'optimize.images' => \App\Http\Middleware\OptimizeImageServing::class,
            'security.headers' => \App\Http\Middleware\SecurityHeaders::class,
        ]);

        // Apply security headers to all requests
        $middleware->web(append: [
            \App\Http\Middleware\SecurityHeaders::class,
        ]);

        // Exclude webhooks from CSRF protection
        $middleware->validateCsrfTokens(except: [
            '*/hooks/sepay-payment',
            '*/charge/callback',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
