<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    /**
     * Hi<PERSON>n thị danh sách đơn hàng
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'orderItems.product', 'orderItems.productAccount']);

        // Search by order number or user
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('username', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by order type (normal vs preorder)
        if ($request->filled('order_type')) {
            if ($request->order_type === 'preorder') {
                // Đơn có ít nhất 1 item chưa có tài khoản
                $query->whereHas('orderItems', function($q) {
                    $q->whereNull('product_account_id');
                });
            } elseif ($request->order_type === 'normal') {
                // Đơn có tất cả items đều có tài khoản
                $query->whereDoesntHave('orderItems', function($q) {
                    $q->whereNull('product_account_id');
                });
            }
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $orders = $query->paginate(15)->appends($request->query());

        // Stats
        $stats = [
            'total_orders' => Order::count(),
            'normal_orders' => Order::whereDoesntHave('orderItems', function($q) {
                $q->whereNull('product_account_id');
            })->count(),
            'preorders' => Order::whereHas('orderItems', function($q) {
                $q->whereNull('product_account_id');
            })->count(),
            'total_revenue' => Order::sum('total_amount'),
            'today_orders' => Order::whereDate('created_at', today())->count(),
            'today_revenue' => Order::whereDate('created_at', today())->sum('total_amount'),
            'this_month_orders' => Order::whereMonth('created_at', now()->month)->whereYear('created_at', now()->year)->count(),
            'this_month_revenue' => Order::whereMonth('created_at', now()->month)->whereYear('created_at', now()->year)->sum('total_amount'),
        ];

        return view('admin.orders.index', compact('orders', 'stats'));
    }

    /**
     * Hiển thị chi tiết đơn hàng
     */
    public function show(Order $order)
    {
        $order->load(['user', 'orderItems.product.category', 'orderItems.productAccount', 'usedVoucher']);

        return view('admin.orders.show', compact('order'));
    }


}
