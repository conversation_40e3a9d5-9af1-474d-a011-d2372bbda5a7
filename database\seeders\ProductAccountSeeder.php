<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductAccount;
use App\Models\Product;

class ProductAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();

        foreach ($products as $product) {
            // Tạo 2-3 tài kho<PERSON>n cho mỗi sản phẩm
            for ($i = 1; $i <= 3; $i++) {
                ProductAccount::create([
                    'product_id' => $product->id,
                    'username' => 'user' . $product->id . '_' . $i . '@gmail.com',
                    'password' => 'password' . $i . '123',
                    'status' => 'available',
                ]);
            }
        }
    }
}
