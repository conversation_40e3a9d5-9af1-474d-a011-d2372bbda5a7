<?php $__env->startSection('title', 'Quản lý Voucher'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Quản lý Voucher</h1>
            <p class="text-gray-600 mt-1">Quản lý mã giảm giá và khuyến mãi cho khách hàng</p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo e(route('admin.vouchers.create')); ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors w-full sm:w-auto">
                <i class="fas fa-plus mr-2"></i>Tạo Voucher
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex flex-wrap gap-2">
            <a href="<?php echo e(route('admin.vouchers.index')); ?>"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors <?php echo e(!request('status') ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                Tất cả
            </a>
            <a href="<?php echo e(route('admin.vouchers.index', ['status' => 'active'])); ?>"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors <?php echo e(request('status') === 'active' ? 'bg-green-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                Hoạt động
            </a>
            <a href="<?php echo e(route('admin.vouchers.index', ['status' => 'inactive'])); ?>"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors <?php echo e(request('status') === 'inactive' ? 'bg-yellow-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                Tạm dừng
            </a>
            <a href="<?php echo e(route('admin.vouchers.index', ['status' => 'archived'])); ?>"
               class="px-3 py-1.5 rounded-md text-sm font-medium transition-colors <?php echo e(request('status') === 'archived' ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                Đã lưu trữ
            </a>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-check-circle text-green-400 mr-3 mt-0.5"></i>
                <span><?php echo e(session('success')); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
                <span><?php echo e(session('error')); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Vouchers Table -->
    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Voucher</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Loại</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Giảm giá</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Áp dụng</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Sử dụng</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trạng thái</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Hành động</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $vouchers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $voucher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="px-4 py-3 whitespace-nowrap"><?php echo e($voucher->id); ?></td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($voucher->name); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo e($voucher->code); ?></div>
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    <?php echo e($voucher->type === 'manual' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'); ?>">
                                    <?php echo e($voucher->type === 'manual' ? 'Thủ công' : 'Tự động'); ?>

                                </span>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <?php if($voucher->discount_type === 'fixed'): ?>
                                    <span class="font-medium"><?php echo e(number_format($voucher->discount_value, 0, ',', '.')); ?>đ</span>
                                <?php else: ?>
                                    <span class="font-medium"><?php echo e($voucher->discount_value); ?>%</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div>
                                    <?php if($voucher->applicable_to === 'products'): ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                            <i class="fas fa-box mr-1"></i>Sản phẩm
                                        </span>
                                    <?php elseif($voucher->applicable_to === 'services'): ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                            <i class="fas fa-cogs mr-1"></i>Dịch vụ
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                                            <i class="fas fa-globe mr-1"></i>Tất cả
                                        </span>
                                    <?php endif; ?>
                                    <?php if($voucher->category): ?>
                                        <div class="text-xs text-gray-400 mt-1"><?php echo e($voucher->category->name); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <?php
                                    $totalGranted = $voucher->user_vouchers_count ?? 0;
                                    $totalUsed = $voucher->used_count ?? 0;
                                ?>
                                <div class="text-sm font-medium"><?php echo e($totalUsed); ?>/<?php echo e($totalGranted); ?></div>
                                <?php if($totalGranted > 0): ?>
                                    <div class="text-xs text-gray-400">(<?php echo e(round(($totalUsed / $totalGranted) * 100, 1)); ?>%)</div>
                                <?php else: ?>
                                    <div class="text-xs text-gray-400">(0%)</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <?php if($voucher->trashed()): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-archive mr-1"></i>Đã lưu trữ
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        <?php echo e($voucher->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                        <i class="fas fa-<?php echo e($voucher->is_active ? 'check-circle' : 'pause-circle'); ?> mr-1"></i>
                                        <?php echo e($voucher->is_active ? 'Hoạt động' : 'Tạm dừng'); ?>

                                    </span>
                                <?php endif; ?>
                                <?php if($voucher->expires_at && $voucher->expires_at < now()): ?>
                                    <div class="mt-1">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-clock mr-1"></i>Hết hạn
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex space-x-2">
                                    <?php if($voucher->trashed()): ?>
                                        <!-- Voucher đã archived -->
                                        <form method="POST" action="<?php echo e(route('admin.vouchers.restore', $voucher->id)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit" class="text-green-600 hover:text-green-900" title="Khôi phục">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </form>

                                        <form method="POST" action="<?php echo e(route('admin.vouchers.force-delete', $voucher->id)); ?>" class="inline"
                                              onsubmit="return confirm('Bạn có chắc chắn muốn XÓA VĨNH VIỄN voucher này? Hành động này không thể hoàn tác!')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="text-red-600 hover:text-red-900" title="Xóa vĩnh viễn">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <!-- Voucher active -->
                                        <a href="<?php echo e(route('admin.vouchers.edit', $voucher)); ?>" class="text-indigo-600 hover:text-indigo-900" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <form method="POST" action="<?php echo e(route('admin.vouchers.toggle-status', $voucher)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit" class="text-yellow-600 hover:text-yellow-900" title="<?php echo e($voucher->is_active ? 'Tạm dừng' : 'Kích hoạt'); ?>">
                                                <i class="fas fa-<?php echo e($voucher->is_active ? 'pause' : 'play'); ?>"></i>
                                            </button>
                                        </form>

                                        <?php if($voucher->type === 'manual'): ?>
                                            <form method="POST" action="<?php echo e(route('admin.vouchers.destroy', $voucher)); ?>" class="inline"
                                                  onsubmit="return confirm('Bạn có chắc chắn muốn lưu trữ (archive) voucher này?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-orange-600 hover:text-orange-900" title="Lưu trữ">
                                                    <i class="fas fa-archive"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="px-4 py-8 text-center">
                                <div class="text-gray-400">
                                    <i class="fas fa-ticket-alt text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">Chưa có voucher nào</p>
                                    <p class="text-sm">Tạo voucher đầu tiên để bắt đầu</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($vouchers->hasPages()): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <?php echo e($vouchers->links('pagination.custom')); ?>

        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/rainshop/resources/views/admin/vouchers/index.blade.php ENDPATH**/ ?>