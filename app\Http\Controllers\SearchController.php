<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;

class SearchController extends Controller
{
    /**
     * Hi<PERSON>n thị trang tìm kiếm
     */
    public function index(Request $request)
    {
        $query = $request->get('q');
        $categories = collect();

        if ($query) {
            $categories = Category::where('status', 'active')
                ->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('publisher', 'like', "%{$query}%");
                })
                ->withCount(['products' => function($q) {
                    $q->where('status', 'active')
                      ->whereHas('availableAccounts');
                }])
                ->orderBy('name')
                ->get();
        }

        return view('search', compact('query', 'categories'));
    }

    /**
     * API tìm kiếm danh mục cho autocomplete
     */
    public function searchCategories(Request $request)
    {
        $query = $request->get('q');
        
        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }

        $categories = Category::where('status', 'active')
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('publisher', 'like', "%{$query}%");
            })
            ->withCount(['products' => function($q) {
                $q->where('status', 'active')
                  ->whereHas('availableAccounts');
            }])
            ->orderBy('name')
            ->limit(8)
            ->get()
            ->map(function($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'publisher' => $category->publisher,
                    'slug' => $category->slug,
                    'image' => $category->image,
                    'image_url' => $category->image ? storage_url($category->image) : null,
                    'products_count' => $category->products_count,
                    'url' => route('category.show', $category->slug)
                ];
            });

        return response()->json($categories);
    }
}
